{"version": 3, "sources": [], "sections": [{"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/app/page.tsx"], "sourcesContent": ["import { SignIn<PERSON><PERSON>on, SignU<PERSON><PERSON><PERSON>on, User<PERSON><PERSON>on } from '@clerk/nextjs';\nimport { auth } from '@clerk/nextjs/server';\nimport Link from 'next/link';\n\nexport default async function Home() {\n  const { userId } = await auth();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5\">\n      {/* Navigation */}\n      <div className=\"navbar bg-base-100/80 backdrop-blur-md shadow-lg border-b border-base-300\">\n        <div className=\"navbar-start\">\n          <Link href=\"/\" className=\"btn btn-ghost text-xl font-bold text-primary hover:text-primary-focus\">\n            🚀 Influtify\n          </Link>\n        </div>\n        <div className=\"navbar-end\">\n          {userId ? (\n            <div className=\"flex items-center gap-3\">\n              <Link href=\"/dashboard\" className=\"btn btn-primary btn-sm\">\n                Dashboard\n              </Link>\n              <UserButton />\n            </div>\n          ) : (\n            <div className=\"flex items-center gap-2\">\n              <SignInButton>\n                <button className=\"btn btn-ghost btn-sm\">Sign In</button>\n              </SignInButton>\n              <SignUpButton>\n                <button className=\"btn btn-primary btn-sm\">Get Started</button>\n              </SignUpButton>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Hero Section */}\n      <div className=\"hero min-h-[calc(100vh-5rem)] py-12\">\n        <div className=\"hero-content text-center\">\n          <div className=\"max-w-5xl\">\n            <div className=\"mb-8\">\n              <div className=\"badge badge-primary badge-lg mb-4\">✨ AI-Powered Growth</div>\n              <h1 className=\"text-6xl font-extrabold mb-6 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent\">\n                Grow Your Social Media with AI-Powered Guidance\n              </h1>\n              <p className=\"text-xl mb-8 text-base-content/70 max-w-3xl mx-auto leading-relaxed\">\n                Master the art of going viral with personalized strategies based on Brendan Kane's proven methods.\n                Get AI mentoring, interactive roadmaps, and content creation tools all in one place.\n              </p>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-16\">\n              {userId ? (\n                <Link href=\"/dashboard\" className=\"btn btn-primary btn-lg gap-2 shadow-lg\">\n                  <span>Continue Your Journey</span>\n                  🚀\n                </Link>\n              ) : (\n                <>\n                  <SignUpButton>\n                    <button className=\"btn btn-primary btn-lg gap-2 shadow-lg hover:shadow-xl transition-all\">\n                      <span>Start Growing Today</span>\n                      🌟\n                    </button>\n                  </SignUpButton>\n                  <SignInButton>\n                    <button className=\"btn btn-outline btn-lg gap-2 hover:shadow-lg transition-all\">\n                      Sign In\n                    </button>\n                  </SignInButton>\n                </>\n              )}\n            </div>\n\n            {/* Features Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              <div className=\"card bg-base-100 shadow-2xl hover:shadow-3xl transition-all duration-300 border border-base-200 hover:border-primary/20\">\n                <div className=\"card-body text-center p-8\">\n                  <div className=\"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center text-3xl\">\n                    🤖\n                  </div>\n                  <h3 className=\"card-title justify-center text-xl mb-3 text-primary\">AI Mentor</h3>\n                  <p className=\"text-base-content/70 leading-relaxed\">\n                    Get personalized guidance through natural conversation with your AI social media mentor.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"card bg-base-100 shadow-2xl hover:shadow-3xl transition-all duration-300 border border-base-200 hover:border-secondary/20\">\n                <div className=\"card-body text-center p-8\">\n                  <div className=\"w-16 h-16 mx-auto mb-4 bg-secondary/10 rounded-full flex items-center justify-center text-3xl\">\n                    🗺️\n                  </div>\n                  <h3 className=\"card-title justify-center text-xl mb-3 text-secondary\">Interactive Roadmaps</h3>\n                  <p className=\"text-base-content/70 leading-relaxed\">\n                    Follow step-by-step roadmaps tailored to your goals and current follower count.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"card bg-base-100 shadow-2xl hover:shadow-3xl transition-all duration-300 border border-base-200 hover:border-accent/20\">\n                <div className=\"card-body text-center p-8\">\n                  <div className=\"w-16 h-16 mx-auto mb-4 bg-accent/10 rounded-full flex items-center justify-center text-3xl\">\n                    🎨\n                  </div>\n                  <h3 className=\"card-title justify-center text-xl mb-3 text-accent\">Viral Content Formats</h3>\n                  <p className=\"text-base-content/70 leading-relaxed\">\n                    Master the 4 proven viral formats with templates and real-world examples.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,eAAe;IAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;IAE5B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAwE;;;;;;;;;;;kCAInG,8OAAC;wBAAI,WAAU;kCACZ,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAyB;;;;;;8CAG3D,8OAAC,sLAAA,CAAA,aAAU;;;;;;;;;;iDAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sLAAA,CAAA,eAAY;8CACX,cAAA,8OAAC;wCAAO,WAAU;kDAAuB;;;;;;;;;;;8CAE3C,8OAAC,sLAAA,CAAA,eAAY;8CACX,cAAA,8OAAC;wCAAO,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAoC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDAAwG;;;;;;kDAGtH,8OAAC;wCAAE,WAAU;kDAAsE;;;;;;;;;;;;0CAMrF,8OAAC;gCAAI,WAAU;0CACZ,uBACC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;;sDAChC,8OAAC;sDAAK;;;;;;wCAA4B;;;;;;yDAIpC;;sDACE,8OAAC,sLAAA,CAAA,eAAY;sDACX,cAAA,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;kEAAK;;;;;;oDAA0B;;;;;;;;;;;;sDAIpC,8OAAC,sLAAA,CAAA,eAAY;sDACX,cAAA,8OAAC;gDAAO,WAAU;0DAA8D;;;;;;;;;;;;;;;;;;0CASxF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA8F;;;;;;8DAG7G,8OAAC;oDAAG,WAAU;8DAAsD;;;;;;8DACpE,8OAAC;oDAAE,WAAU;8DAAuC;;;;;;;;;;;;;;;;;kDAMxD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgG;;;;;;8DAG/G,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;oDAAE,WAAU;8DAAuC;;;;;;;;;;;;;;;;;kDAMxD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA6F;;;;;;8DAG5G,8OAAC;oDAAG,WAAU;8DAAqD;;;;;;8DACnE,8OAAC;oDAAE,WAAU;8DAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtE", "debugId": null}}]}