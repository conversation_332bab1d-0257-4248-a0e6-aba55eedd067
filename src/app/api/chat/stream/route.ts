import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { conversationService } from '@/lib/services/conversation';
import { UserService } from '@/lib/services/user';
import { callAIWithStreaming, getCurrentProvider } from '@/lib/services/ai-provider';
import { z } from 'zod';

const chatStreamRequestSchema = z.object({
  message: z.string().min(1, 'Message cannot be empty'),
  conversationId: z.string().optional(),
});

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { message, conversationId } = chatStreamRequestSchema.parse(body);

    // Get user profile for context
    const userProfile = await UserService.getCurrentUser();
    
    // Build conversation context
    const context = {
      userId,
      conversationId,
      userProfile,
      currentGoals: userProfile?.goals || [],
    };

    // Get or create conversation
    let finalConversationId = conversationId;
    if (!finalConversationId) {
      finalConversationId = await conversationService.createConversation(userId);
    }

    // Get conversation history for context
    const history = await conversationService.getConversationHistory(finalConversationId);
    
    // Build system prompt with context
    const systemPrompt = buildSystemPrompt(context);
    
    // Build conversation context for AI
    const conversationContext = history.map(msg => 
      `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`
    ).join('\n');
    
    const fullPrompt = `${conversationContext ? `Previous conversation:\n${conversationContext}\n\n` : ''}User: ${message}`;

    // Save user message
    await conversationService.saveMessage(finalConversationId, 'user', message);

    // Create streaming response
    const stream = await callAIWithStreaming(fullPrompt, systemPrompt);
    
    // Create a transform stream to collect the response for saving
    let fullResponse = '';
    const transformStream = new TransformStream({
      transform(chunk, controller) {
        const text = new TextDecoder().decode(chunk);
        fullResponse += text;
        controller.enqueue(chunk);
      },
      flush() {
        // Save the complete AI response after streaming is done
        conversationService.saveMessage(finalConversationId!, 'assistant', fullResponse)
          .catch(error => console.error('Error saving AI response:', error));
      }
    });

    // Return streaming response with conversation ID in headers
    return new Response(stream.pipeThrough(transformStream), {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'X-Conversation-Id': finalConversationId,
        'X-AI-Provider': getCurrentProvider(),
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  } catch (error) {
    console.error('Chat stream API error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Build system prompt with user context for Influtify AI mentor
 */
function buildSystemPrompt(context: any): string {
  const { userProfile, currentGoals } = context;

  return `You are an AI social media growth mentor for Influtify, based on Brendan Kane's "The Guide to Going Viral" methodology. Your role is to provide personalized, actionable advice to help users grow their social media presence.

CORE PRINCIPLES:
- Focus on proven viral content strategies from Brendan Kane's methods
- Provide specific, actionable advice tailored to the user's current situation
- Emphasize the 4 viral content formats: Educational, Entertaining, Inspirational, and Conversational
- Help users understand their target audience and create content that resonates
- Guide users through the process of testing, measuring, and optimizing their content

USER CONTEXT:
${userProfile ? `
- Name: ${userProfile.firstName || 'User'}
- Current follower goals: ${currentGoals?.join(', ') || 'Not specified'}
- Profile: ${JSON.stringify(userProfile, null, 2)}
` : '- No user profile available'}

RESPONSE GUIDELINES:
- Be conversational, encouraging, and supportive
- Ask clarifying questions to better understand their goals and challenges
- Provide specific examples and actionable steps
- Reference Brendan Kane's strategies when relevant
- Keep responses focused and practical
- Use emojis sparingly but effectively to maintain engagement
- If the user asks about platforms, focus on their specific platform needs
- Always aim to move the conversation toward actionable next steps

Remember: You're not just providing information, you're mentoring them through their social media growth journey with proven strategies that have helped creators go viral.`;
}
