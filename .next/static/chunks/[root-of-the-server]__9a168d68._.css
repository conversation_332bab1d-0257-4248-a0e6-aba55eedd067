/* [next]/internal/font/google/inter_59dee874.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n_wU-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n_wU-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n_wU-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n_wU-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n_wU-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n_wU-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_59dee874-module__9CtR0q__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}


/* [project]/src/app/globals.css [app-client] (css) */
*, :before, :after, ::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #3b82f680;
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border: 0 solid #e5e7eb;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
  font-family: ui-sans-serif, system-ui, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
  line-height: 1.5;
}

body {
  line-height: inherit;
  margin: 0;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button, input:where([type="button"]), input:where([type="reset"]), input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: #0000;
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  vertical-align: middle;
  display: block;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:where(:root), :root:has(input.theme-controller[value="light"]:checked), [data-theme="light"] {
  color-scheme: light;
  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(98% 0 0);
  --color-base-300: oklch(95% 0 0);
  --color-base-content: oklch(21% .006 285.885);
  --color-primary: oklch(45% .24 277.023);
  --color-primary-content: oklch(93% .034 272.788);
  --color-secondary: oklch(65% .241 354.308);
  --color-secondary-content: oklch(94% .028 342.258);
  --color-accent: oklch(77% .152 181.912);
  --color-accent-content: oklch(38% .063 188.416);
  --color-neutral: oklch(14% .005 285.823);
  --color-neutral-content: oklch(92% .004 286.32);
  --color-info: oklch(74% .16 232.661);
  --color-info-content: oklch(29% .066 243.157);
  --color-success: oklch(76% .177 163.223);
  --color-success-content: oklch(37% .077 168.94);
  --color-warning: oklch(82% .189 84.429);
  --color-warning-content: oklch(41% .112 45.904);
  --color-error: oklch(71% .194 13.428);
  --color-error-content: oklch(27% .105 12.094);
  --radius-selector: .5rem;
  --radius-field: .25rem;
  --radius-box: .5rem;
  --size-selector: .25rem;
  --size-field: .25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
    --color-base-100: oklch(25.33% .016 252.42);
    --color-base-200: oklch(23.26% .014 253.1);
    --color-base-300: oklch(21.15% .012 254.09);
    --color-base-content: oklch(97.807% .029 256.847);
    --color-primary: oklch(58% .233 277.117);
    --color-primary-content: oklch(96% .018 272.314);
    --color-secondary: oklch(65% .241 354.308);
    --color-secondary-content: oklch(94% .028 342.258);
    --color-accent: oklch(77% .152 181.912);
    --color-accent-content: oklch(38% .063 188.416);
    --color-neutral: oklch(14% .005 285.823);
    --color-neutral-content: oklch(92% .004 286.32);
    --color-info: oklch(74% .16 232.661);
    --color-info-content: oklch(29% .066 243.157);
    --color-success: oklch(76% .177 163.223);
    --color-success-content: oklch(37% .077 168.94);
    --color-warning: oklch(82% .189 84.429);
    --color-warning-content: oklch(41% .112 45.904);
    --color-error: oklch(71% .194 13.428);
    --color-error-content: oklch(27% .105 12.094);
    --radius-selector: .5rem;
    --radius-field: .25rem;
    --radius-box: .5rem;
    --size-selector: .25rem;
    --size-field: .25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}

:root:has(input.theme-controller[value="light"]:checked), [data-theme="light"] {
  color-scheme: light;
  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(98% 0 0);
  --color-base-300: oklch(95% 0 0);
  --color-base-content: oklch(21% .006 285.885);
  --color-primary: oklch(45% .24 277.023);
  --color-primary-content: oklch(93% .034 272.788);
  --color-secondary: oklch(65% .241 354.308);
  --color-secondary-content: oklch(94% .028 342.258);
  --color-accent: oklch(77% .152 181.912);
  --color-accent-content: oklch(38% .063 188.416);
  --color-neutral: oklch(14% .005 285.823);
  --color-neutral-content: oklch(92% .004 286.32);
  --color-info: oklch(74% .16 232.661);
  --color-info-content: oklch(29% .066 243.157);
  --color-success: oklch(76% .177 163.223);
  --color-success-content: oklch(37% .077 168.94);
  --color-warning: oklch(82% .189 84.429);
  --color-warning-content: oklch(41% .112 45.904);
  --color-error: oklch(71% .194 13.428);
  --color-error-content: oklch(27% .105 12.094);
  --radius-selector: .5rem;
  --radius-field: .25rem;
  --radius-box: .5rem;
  --size-selector: .25rem;
  --size-field: .25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

:root:has(input.theme-controller[value="dark"]:checked), [data-theme="dark"] {
  color-scheme: dark;
  --color-base-100: oklch(25.33% .016 252.42);
  --color-base-200: oklch(23.26% .014 253.1);
  --color-base-300: oklch(21.15% .012 254.09);
  --color-base-content: oklch(97.807% .029 256.847);
  --color-primary: oklch(58% .233 277.117);
  --color-primary-content: oklch(96% .018 272.314);
  --color-secondary: oklch(65% .241 354.308);
  --color-secondary-content: oklch(94% .028 342.258);
  --color-accent: oklch(77% .152 181.912);
  --color-accent-content: oklch(38% .063 188.416);
  --color-neutral: oklch(14% .005 285.823);
  --color-neutral-content: oklch(92% .004 286.32);
  --color-info: oklch(74% .16 232.661);
  --color-info-content: oklch(29% .066 243.157);
  --color-success: oklch(76% .177 163.223);
  --color-success-content: oklch(37% .077 168.94);
  --color-warning: oklch(82% .189 84.429);
  --color-warning-content: oklch(41% .112 45.904);
  --color-error: oklch(71% .194 13.428);
  --color-error-content: oklch(27% .105 12.094);
  --radius-selector: .5rem;
  --radius-field: .25rem;
  --radius-box: .5rem;
  --size-selector: .25rem;
  --size-field: .25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

@property --radialprogress {
  syntax: "<percentage>";
  inherits: true;
  initial-value: 0%;
}

:root {
  scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
  --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
}

:root, [data-theme] {
  background-color: var(--root-bg, var(--color-base-100));
  color: var(--color-base-content);
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--color-base-100);
  color: var(--color-base-content);
}

@keyframes radio {
  0% {
    padding: 5px;
  }

  50% {
    padding: 3px;
  }
}

.tabs {
  --tabs-height: auto;
  --tabs-direction: row;
  --tab-height: calc(var(--size-field, .25rem) * 10);
  height: var(--tabs-height);
  flex-wrap: wrap;
  flex-direction: var(--tabs-direction);
  display: flex;
}

.tab {
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  text-align: center;
  webkit-user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  display: inline-flex;
  position: relative;
}

@media (hover: hover) {
  .tab:hover {
    color: var(--color-base-content);
  }
}

.tab {
  --tab-p: 1rem;
  --tab-bg: var(--color-base-100);
  --tab-border-color: var(--color-base-300);
  --tab-radius-ss: 0;
  --tab-radius-se: 0;
  --tab-radius-es: 0;
  --tab-radius-ee: 0;
  --tab-order: 0;
  --tab-radius-min: calc(.75rem - var(--border));
  order: var(--tab-order);
  height: var(--tab-height);
  border-color: #0000;
  padding-inline-start: var(--tab-p);
  padding-inline-end: var(--tab-p);
  font-size: .875rem;
}

.tab:is(input[type="radio"]) {
  min-width: fit-content;
}

.tab:is(input[type="radio"]):after {
  content: attr(aria-label);
}

.tab:is(label) {
  position: relative;
}

.tab:is(label) input {
  cursor: pointer;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  opacity: 0;
  position: absolute;
  inset: 0;
}

.tab:checked + .tab-content, .tab:is(label:has(:checked)) + .tab-content, .tab:is(.tab-active, [aria-selected="true"]) + .tab-content {
  height: calc(100% - var(--tab-height)  + var(--border));
  display: block;
}

.tab:not(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"]) {
  color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
}

.tab:not(input):empty {
  cursor: default;
  flex-grow: 1;
}

.tab:focus {
  --tw-outline-style: none;
  outline-style: none;
}

@media (forced-colors: active) {
  .tab:focus {
    outline-offset: 2px;
    outline: 2px solid #0000;
  }
}

.tab:focus-visible, .tab:is(label:has(:checked:focus-visible)) {
  outline-offset: -5px;
  outline: 2px solid;
}

.tab[disabled] {
  pointer-events: none;
  opacity: .4;
}

.tabs-border .tab {
  --tab-border-color: #0000 #0000 var(--tab-border-color) #0000;
  border-radius: var(--radius-field);
  position: relative;
}

.tabs-border .tab:before {
  --tw-content: "";
  content: var(--tw-content);
  background-color: var(--tab-border-color);
  border-radius: var(--radius-field);
  width: 80%;
  height: 3px;
  transition: background-color .2s;
  position: absolute;
  bottom: 0;
  left: 10%;
}

.tabs-border .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):before, .tabs-border .tab:is(input:checked):before, .tabs-border .tab:is(label:has(:checked)):before {
  --tab-border-color: currentColor;
  border-top: 3px solid;
}

.tabs-lift > .tab {
  --tab-border: 0 0 var(--border) 0;
  --tab-radius-ss: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-se: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-es: 0;
  --tab-radius-ee: 0;
  --tab-paddings: var(--border) var(--tab-p) 0 var(--tab-p);
  --tab-border-colors: #0000 #0000 var(--tab-border-color) #0000;
  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
  --tab-corner-position: top left, top right;
  border-width: var(--tab-border);
  padding: var(--tab-paddings);
  border-color: var(--tab-border-colors);
  border-start-start-radius: var(--tab-radius-ss);
  border-start-end-radius: var(--tab-radius-se);
  border-end-end-radius: var(--tab-radius-ee);
  border-end-start-radius: var(--tab-radius-es);
}

.tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-lift > .tab:is(input:checked, label:has(:checked)) {
  --tab-border: var(--border) var(--border) 0 var(--border);
  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000 var(--tab-border-color);
  --tab-paddings: 0 calc(var(--tab-p)  - var(--border)) var(--border) calc(var(--tab-p)  - var(--border));
  --tab-inset: auto auto 0 auto;
  --tab-grad: calc(69% - var(--border));
  --radius-start: radial-gradient(circle at top left, #0000 var(--tab-grad), var(--tab-border-color) calc(var(--tab-grad)  + .25px), var(--tab-border-color) calc(var(--tab-grad)  + var(--border)), var(--tab-bg) calc(var(--tab-grad)  + var(--border)  + .25px));
  --radius-end: radial-gradient(circle at top right, #0000 var(--tab-grad), var(--tab-border-color) calc(var(--tab-grad)  + .25px), var(--tab-border-color) calc(var(--tab-grad)  + var(--border)), var(--tab-bg) calc(var(--tab-grad)  + var(--border)  + .25px));
  background-color: var(--tab-bg);
}

.tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):before {
  z-index: 1;
  content: "";
  width: var(--tab-corner-width);
  height: var(--tab-corner-height);
  background-position: var(--tab-corner-position);
  background-image: var(--radius-start), var(--radius-end);
  background-size: min(var(--radius-field), var(--tab-radius-min)) min(var(--radius-field), var(--tab-radius-min));
  inset: var(--tab-inset);
  background-repeat: no-repeat;
  display: block;
  position: absolute;
}

.tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):first-child:before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):first-child:before {
  --radius-start: none;
}

[dir="rtl"] .tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):first-child:before, [dir="rtl"] .tabs-lift > .tab:is(input:checked, label:has(:checked)):first-child:before {
  transform: rotateY(180deg);
}

.tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):last-child:before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):last-child:before {
  --radius-end: none;
}

[dir="rtl"] .tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):last-child:before, [dir="rtl"] .tabs-lift > .tab:is(input:checked, label:has(:checked)):last-child:before {
  transform: rotateY(180deg);
}

.tabs-lift:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected="true"]) {
  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000 var(--tab-border-color);
}

.tabs-lift :checked + .tab-content:first-child, .tabs-lift :checked + .tab-content:nth-child(n+3), .tabs-lift label:has(:checked) + .tab-content:first-child, .tabs-lift label:has(:checked) + .tab-content:nth-child(n+3), .tabs-lift :is(.tab-active, [aria-selected="true"]) + .tab-content:first-child, .tabs-lift :is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(n+3) {
  --tabcontent-radius-ss: var(--radius-box);
}

.tabs-top .tab {
  --tab-order: 0;
  --tab-border: 0 0 var(--border) 0;
  --tab-radius-ss: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-se: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-es: 0;
  --tab-radius-ee: 0;
  --tab-paddings: var(--border) var(--tab-p) 0 var(--tab-p);
  --tab-border-colors: #0000 #0000 var(--tab-border-color) #0000;
  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
  --tab-corner-position: top left, top right;
}

.tabs-top .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-top .tab:is(input:checked), .tabs-top .tab:is(label:has(:checked)) {
  --tab-border: var(--border) var(--border) 0 var(--border);
  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000 var(--tab-border-color);
  --tab-paddings: 0 calc(var(--tab-p)  - var(--border)) var(--border) calc(var(--tab-p)  - var(--border));
  --tab-inset: auto auto 0 auto;
  --radius-start: radial-gradient(circle at top left, #0000 var(--tab-grad), var(--tab-border-color) calc(var(--tab-grad)  + .25px), var(--tab-border-color) calc(var(--tab-grad)  + var(--border)), var(--tab-bg) calc(var(--tab-grad)  + var(--border)  + .25px));
  --radius-end: radial-gradient(circle at top right, #0000 var(--tab-grad), var(--tab-border-color) calc(var(--tab-grad)  + .25px), var(--tab-border-color) calc(var(--tab-grad)  + var(--border)), var(--tab-bg) calc(var(--tab-grad)  + var(--border)  + .25px));
}

.tabs-top:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected="true"]) {
  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000 var(--tab-border-color);
}

.tabs-top :checked + .tab-content:first-child, .tabs-top :checked + .tab-content:nth-child(n+3), .tabs-top label:has(:checked) + .tab-content:first-child, .tabs-top label:has(:checked) + .tab-content:nth-child(n+3), .tabs-top :is(.tab-active, [aria-selected="true"]) + .tab-content:first-child, .tabs-top :is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(n+3) {
  --tabcontent-radius-ss: var(--radius-box);
}

.tabs-bottom .tab {
  --tab-order: 1;
  --tab-border: var(--border) 0 0 0;
  --tab-radius-ss: 0;
  --tab-radius-se: 0;
  --tab-radius-es: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-ee: min(var(--radius-field), var(--tab-radius-min));
  --tab-border-colors: var(--tab-border-color) #0000 #0000 #0000;
  --tab-paddings: 0 var(--tab-p) var(--border) var(--tab-p);
  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
  --tab-corner-position: top left, top right;
}

.tabs-bottom .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-bottom .tab:is(input:checked), .tabs-bottom .tab:is(label:has(:checked)) {
  --tab-border: 0 var(--border) var(--border) var(--border);
  --tab-border-colors: #0000 var(--tab-border-color) var(--tab-border-color) var(--tab-border-color);
  --tab-paddings: var(--border) calc(var(--tab-p)  - var(--border)) 0 calc(var(--tab-p)  - var(--border));
  --tab-inset: 0 auto auto auto;
  --radius-start: radial-gradient(circle at bottom left, #0000 var(--tab-grad), var(--tab-border-color) calc(var(--tab-grad)  + .25px), var(--tab-border-color) calc(var(--tab-grad)  + var(--border)), var(--tab-bg) calc(var(--tab-grad)  + var(--border)  + .25px));
  --radius-end: radial-gradient(circle at bottom right, #0000 var(--tab-grad), var(--tab-border-color) calc(var(--tab-grad)  + .25px), var(--tab-border-color) calc(var(--tab-grad)  + var(--border)), var(--tab-bg) calc(var(--tab-grad)  + var(--border)  + .25px));
}

.tabs-bottom:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected="true"]) {
  --tab-border-colors: #0000 var(--tab-border-color) var(--tab-border-color) var(--tab-border-color);
}

.tabs-bottom > :checked + .tab-content:not(:nth-child(2)), .tabs-bottom > :is(label:has(:checked)) + .tab-content:not(:nth-child(2)), .tabs-bottom > :is(.tab-active, [aria-selected="true"]) + .tab-content:not(:nth-child(2)) {
  --tabcontent-radius-es: var(--radius-box);
}

.tabs-box .tab {
  border-radius: var(--radius-field);
  border-style: none;
}

.tabs-box .tab:focus-visible, .tabs-box .tab:is(label:has(:checked:focus-visible)) {
  outline-offset: 2px;
}

.tabs-box > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-box > :is(input:checked), .tabs-box > :is(label:has(:checked)) {
  background-color: var(--tab-bg, var(--color-base-100));
  box-shadow: 0 1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset, 0 1px 1px -1px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 50%), #0000), 0 1px 6px -4px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 100%), #0000);
}

@media (forced-colors: active) {
  .tabs-box > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-box > :is(input:checked), .tabs-box > :is(label:has(:checked)) {
    border: 1px solid;
  }
}

.tabs-xs :where(.tab) {
  --tab-p: .375rem;
  --tab-radius-min: calc(.5rem - var(--border));
  font-size: .75rem;
}

.tabs-sm :where(.tab) {
  --tab-p: .5rem;
  --tab-radius-min: calc(.5rem - var(--border));
  font-size: .875rem;
}

.tabs-md :where(.tab) {
  --tab-p: .75rem;
  --tab-radius-min: calc(.75rem - var(--border));
  font-size: .875rem;
}

.tabs-lg :where(.tab) {
  --tab-p: 1rem;
  --tab-radius-min: calc(1.5rem - var(--border));
  font-size: 1.125rem;
}

.tabs-xl :where(.tab) {
  --tab-p: 1.25rem;
  --tab-radius-min: calc(2rem - var(--border));
  font-size: 1.125rem;
}

.hero {
  background-position: center;
  background-size: cover;
  place-items: center;
  width: 100%;
  display: grid;
}

.hero > * {
  grid-row-start: 1;
  grid-column-start: 1;
}

.hero-content {
  isolation: isolate;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  max-width: 80rem;
  padding: 1rem;
  display: flex;
}

.avatar-group :where(.avatar) {
  border: 4px solid var(--color-base-100);
  border-radius: 3.40282e38px;
  overflow: hidden;
}

.avatar {
  vertical-align: middle;
  display: inline-flex;
  position: relative;
}

.avatar > div {
  aspect-ratio: 1;
  display: block;
  overflow: hidden;
}

.avatar img {
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

@keyframes skeleton {
  0% {
    background-position: 150%;
  }

  100% {
    background-position: -50%;
  }
}

.progress {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: var(--radius-box);
  background-color: color-mix(in oklab, currentColor 20%, transparent);
  width: 100%;
  height: .5rem;
  color: var(--color-base-content);
  position: relative;
  overflow: hidden;
}

.progress:indeterminate {
  background-image: repeating-linear-gradient(90deg, currentColor -1% 10%, #0000 10% 90%);
  background-position-x: 15%;
  background-size: 200%;
  animation: 5s ease-in-out infinite progress;
}

@supports ((-moz-appearance: none)) {
  .progress:indeterminate::-moz-progress-bar {
    background-color: #0000;
    background-image: repeating-linear-gradient(90deg, currentColor -1% 10%, #0000 10% 90%);
    background-position-x: 15%;
    background-size: 200%;
    animation: 5s ease-in-out infinite progress;
  }

  .progress::-moz-progress-bar {
    border-radius: var(--radius-box);
    background-color: currentColor;
  }
}

@supports ((-webkit-appearance: none)) {
  .progress::-webkit-progress-bar {
    border-radius: var(--radius-box);
    background-color: #0000;
  }

  .progress::-webkit-progress-value {
    border-radius: var(--radius-box);
    background-color: currentColor;
  }
}

@keyframes progress {
  50% {
    background-position-x: -115%;
  }
}

.menu :where(li) .badge {
  justify-self: flex-end;
}

:where(.btn) {
  width: unset;
}

.btn {
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  outline-offset: 2px;
  webkit-user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  padding-inline: var(--btn-p);
  color: var(--btn-fg);
  --tw-prose-links: var(--btn-fg);
  height: var(--size);
  font-size: var(--fontsize, .875rem);
  outline-color: var(--btn-color, var(--color-base-content));
  background-color: var(--btn-bg);
  background-size: auto, calc(var(--noise) * 100%);
  background-image: none, var(--btn-noise);
  border-width: var(--border);
  border-style: solid;
  border-color: var(--btn-border);
  text-shadow: 0 .5px oklch(100% 0 0 / calc(var(--depth) * .15));
  touch-action: manipulation;
  box-shadow: 0 .5px 0 .5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
  --size: calc(var(--size-field, .25rem) * 10);
  --btn-bg: var(--btn-color, var(--color-base-200));
  --btn-fg: var(--color-base-content);
  --btn-p: 1rem;
  --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
  --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000), 0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
  --btn-noise: var(--fx-noise);
  border-start-start-radius: var(--join-ss, var(--radius-field));
  border-start-end-radius: var(--join-se, var(--radius-field));
  border-end-end-radius: var(--join-ee, var(--radius-field));
  border-end-start-radius: var(--join-es, var(--radius-field));
  flex-wrap: nowrap;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  gap: .375rem;
  font-weight: 600;
  transition-property: color, background-color, border-color, box-shadow;
  transition-duration: .2s;
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
  display: inline-flex;
}

.prose .btn {
  text-decoration-line: none;
}

@media (hover: hover) {
  .btn:hover {
    --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
  }
}

.btn:focus-visible {
  isolation: isolate;
  outline-width: 2px;
  outline-style: solid;
}

.btn:active:not(.btn-active) {
  --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
  --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
  --btn-shadow: 0 0 0 0 oklch(0% 0 0 / 0), 0 0 0 0 oklch(0% 0 0 / 0);
  translate: 0 .5px;
}

.btn:is(:disabled, [disabled], .btn-disabled):not(.btn-link, .btn-ghost) {
  background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
  box-shadow: none;
}

.btn:is(:disabled, [disabled], .btn-disabled) {
  pointer-events: none;
  --btn-border: #0000;
  --btn-noise: none;
  --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
}

@media (hover: hover) {
  .btn:is(:disabled, [disabled], .btn-disabled):hover {
    pointer-events: none;
    background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
    --btn-border: #0000;
    --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
  }
}

.btn:is(input[type="checkbox"], input[type="radio"]) {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.btn:is(input[type="checkbox"], input[type="radio"]):after {
  content: attr(aria-label);
}

.btn:where(input:checked:not(.filter .btn)) {
  --btn-color: var(--color-primary);
  --btn-fg: var(--color-primary-content);
  isolation: isolate;
}

.btn-primary {
  --btn-color: var(--color-primary);
  --btn-fg: var(--color-primary-content);
}

.btn-secondary {
  --btn-color: var(--color-secondary);
  --btn-fg: var(--color-secondary-content);
}

.btn-accent {
  --btn-color: var(--color-accent);
  --btn-fg: var(--color-accent-content);
}

.btn-error {
  --btn-color: var(--color-error);
  --btn-fg: var(--color-error-content);
}

.btn-ghost:not(.btn-active, :hover, :active:focus, :focus-visible) {
  --btn-shadow: "";
  --btn-bg: #0000;
  --btn-border: #0000;
  --btn-noise: none;
}

.btn-ghost:not(.btn-active, :hover, :active:focus, :focus-visible):not(:disabled, [disabled], .btn-disabled) {
  --btn-fg: currentColor;
  outline-color: currentColor;
}

@media (hover: none) {
  .btn-ghost:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
    --btn-shadow: "";
    --btn-bg: #0000;
    --btn-border: #0000;
    --btn-noise: none;
    --btn-fg: currentColor;
  }
}

.btn-outline:not(.btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked) {
  --btn-shadow: "";
  --btn-bg: #0000;
  --btn-fg: var(--btn-color);
  --btn-border: var(--btn-color);
  --btn-noise: none;
}

@media (hover: none) {
  .btn-outline:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked) {
    --btn-shadow: "";
    --btn-bg: #0000;
    --btn-fg: var(--btn-color);
    --btn-border: var(--btn-color);
    --btn-noise: none;
  }
}

.btn-sm {
  --fontsize: .75rem;
  --btn-p: .75rem;
  --size: calc(var(--size-field, .25rem) * 8);
}

.btn-lg {
  --fontsize: 1.125rem;
  --btn-p: 1.25rem;
  --size: calc(var(--size-field, .25rem) * 12);
}

.tooltip {
  --tt-bg: var(--color-neutral);
  --tt-off: calc(100% + .5rem);
  --tt-tail: calc(100% + 1px + .25rem);
  display: inline-block;
  position: relative;
}

.tooltip > :where(.tooltip-content), .tooltip:where([data-tip]):before {
  border-radius: var(--radius-field);
  text-align: center;
  white-space: normal;
  max-width: 20rem;
  color: var(--color-neutral-content);
  opacity: 0;
  background-color: var(--tt-bg);
  pointer-events: none;
  z-index: 2;
  --tw-content: attr(data-tip);
  content: var(--tw-content);
  width: max-content;
  padding-block: .25rem;
  padding-inline: .5rem;
  font-size: .875rem;
  line-height: 1.25;
  transition: opacity .2s cubic-bezier(.4, 0, .2, 1) 75ms, transform .2s cubic-bezier(.4, 0, .2, 1) 75ms;
  position: absolute;
}

.tooltip:after {
  opacity: 0;
  background-color: var(--tt-bg);
  content: "";
  pointer-events: none;
  --mask-tooltip: url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");
  width: .625rem;
  height: .25rem;
  -webkit-mask-position: -1px 0;
  mask-position: -1px 0;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-image: var(--mask-tooltip);
  mask-image: var(--mask-tooltip);
  transition: opacity .2s cubic-bezier(.4, 0, .2, 1) 75ms, transform .2s cubic-bezier(.4, 0, .2, 1) 75ms;
  display: block;
  position: absolute;
}

.tooltip.tooltip-open > .tooltip-content, .tooltip.tooltip-open[data-tip]:before, .tooltip.tooltip-open:after, .tooltip[data-tip]:not([data-tip=""]):hover > .tooltip-content, .tooltip[data-tip]:not([data-tip=""]):hover[data-tip]:before, .tooltip[data-tip]:not([data-tip=""]):hover:after, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover > .tooltip-content, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover[data-tip]:before, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover:after, .tooltip:has(:focus-visible) > .tooltip-content, .tooltip:has(:focus-visible)[data-tip]:before, .tooltip:has(:focus-visible):after {
  opacity: 1;
  --tt-pos: 0rem;
  transition: opacity .2s cubic-bezier(.4, 0, .2, 1), transform .2s cubic-bezier(.4, 0, .2, 1);
}

.tooltip > .tooltip-content, .tooltip[data-tip]:before, .tooltip-top > .tooltip-content, .tooltip-top[data-tip]:before {
  transform: translateX(-50%) translateY(var(--tt-pos, .25rem));
  inset: auto auto var(--tt-off) 50%;
}

.tooltip:after, .tooltip-top:after {
  transform: translateX(-50%) translateY(var(--tt-pos, .25rem));
  inset: auto auto var(--tt-tail) 50%;
}

.label:is(.\!input > *, .select > *) {
  white-space: nowrap !important;
  height: calc(100% - .5rem) !important;
  font-size: inherit !important;
  align-items: center !important;
  padding-inline: .75rem !important;
  display: flex !important;
}

.label:is(.input > *, .select > *) {
  white-space: nowrap;
  height: calc(100% - .5rem);
  font-size: inherit;
  align-items: center;
  padding-inline: .75rem;
  display: flex;
}

.label:is(.\!input > *, .select > *):first-child {
  border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000) !important;
  margin-inline: -.75rem .75rem !important;
}

.label:is(.input > *, .select > *):first-child {
  border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
  margin-inline: -.75rem .75rem;
}

.label:is(.\!input > *, .select > *):last-child {
  border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000) !important;
  margin-inline: .75rem -.75rem !important;
}

.label:is(.input > *, .select > *):last-child {
  border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
  margin-inline: .75rem -.75rem;
}

.navbar {
  align-items: center;
  width: 100%;
  min-height: 4rem;
  padding: .5rem;
  display: flex;
}

.navbar-start {
  justify-content: flex-start;
  align-items: center;
  width: 50%;
  display: inline-flex;
}

.navbar-center {
  flex-shrink: 0;
  align-items: center;
  display: inline-flex;
}

.navbar-end {
  justify-content: flex-end;
  align-items: center;
  width: 50%;
  display: inline-flex;
}

.card {
  border-radius: var(--radius-box);
  outline-offset: 2px;
  outline: 0 solid #0000;
  flex-direction: column;
  transition: outline .2s ease-in-out;
  display: flex;
  position: relative;
}

.card:focus {
  --tw-outline-style: none;
  outline-style: none;
}

@media (forced-colors: active) {
  .card:focus {
    outline-offset: 2px;
    outline: 2px solid #0000;
  }
}

.card:focus-visible {
  outline-color: currentColor;
}

.card :where(figure:first-child) {
  border-start-start-radius: inherit;
  border-start-end-radius: inherit;
  border-end-end-radius: unset;
  border-end-start-radius: unset;
  overflow: hidden;
}

.card :where(figure:last-child) {
  border-start-start-radius: unset;
  border-start-end-radius: unset;
  border-end-end-radius: inherit;
  border-end-start-radius: inherit;
  overflow: hidden;
}

.card:where(.card-border) {
  border: var(--border) solid var(--color-base-200);
}

.card:where(.card-dash) {
  border: var(--border) dashed var(--color-base-200);
}

.card.image-full {
  display: grid;
}

.card.image-full > * {
  grid-row-start: 1;
  grid-column-start: 1;
}

.card.image-full > .card-body {
  color: var(--color-neutral-content);
  position: relative;
}

.card.image-full :where(figure) {
  border-radius: inherit;
  overflow: hidden;
}

.card.image-full > figure img {
  -o-object-fit: cover;
  object-fit: cover;
  filter: brightness(28%);
  height: 100%;
}

.card figure {
  justify-content: center;
  align-items: center;
  display: flex;
}

.card:has( > input:is(input[type="checkbox"], input[type="radio"])) {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.card:has( > :checked) {
  outline: 2px solid;
}

.card-title {
  font-size: var(--cardtitle-fs, 1.125rem);
  align-items: center;
  gap: .5rem;
  font-weight: 600;
  display: flex;
}

.card-body {
  padding: var(--card-p, 1.5rem);
  font-size: var(--card-fs, .875rem);
  flex-direction: column;
  flex: auto;
  gap: .5rem;
  display: flex;
}

.card-body :where(p) {
  flex-grow: 1;
}

.card-xs .card-body {
  --card-p: .5rem;
  --card-fs: .6875rem;
}

.card-xs .card-title {
  --cardtitle-fs: .875rem;
}

.card-sm .card-body {
  --card-p: 1rem;
  --card-fs: .75rem;
}

.card-sm .card-title {
  --cardtitle-fs: 1rem;
}

.card-md .card-body {
  --card-p: 1.5rem;
  --card-fs: .875rem;
}

.card-md .card-title {
  --cardtitle-fs: 1.125rem;
}

.card-lg .card-body {
  --card-p: 2rem;
  --card-fs: 1rem;
}

.card-lg .card-title {
  --cardtitle-fs: 1.25rem;
}

.card-xl .card-body {
  --card-p: 2.5rem;
  --card-fs: 1.125rem;
}

.card-xl .card-title {
  --cardtitle-fs: 1.375rem;
}

@keyframes toast {
  0% {
    opacity: 0;
    scale: .9;
  }

  100% {
    opacity: 1;
    scale: 1;
  }
}

.status {
  aspect-ratio: 1;
  border-radius: var(--radius-selector);
  background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
  vertical-align: middle;
  color: #0000004d;
  background-position: center;
  background-repeat: no-repeat;
  width: .5rem;
  height: .5rem;
  display: inline-block;
}

@supports (color: color-mix(in lab, red, red)) {
  .status {
    color: color-mix(in oklab, var(--color-black) 30%, transparent);
  }
}

.status {
  background-image: radial-gradient(circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * .5)), #0000);
  box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);
}

.badge {
  border-radius: var(--radius-selector);
  vertical-align: middle;
  color: var(--badge-fg);
  border: var(--border) solid var(--badge-color, var(--color-base-200));
  width: fit-content;
  padding-inline: calc(.25rem * 3 - var(--border));
  background-size: auto, calc(var(--noise) * 100%);
  background-image: none, var(--fx-noise);
  background-color: var(--badge-bg);
  --badge-bg: var(--badge-color, var(--color-base-100));
  --badge-fg: var(--color-base-content);
  --size: calc(var(--size-selector, .25rem) * 6);
  height: var(--size);
  justify-content: center;
  align-items: center;
  gap: .5rem;
  font-size: .875rem;
  display: inline-flex;
}

.badge-primary {
  --badge-color: var(--color-primary);
  --badge-fg: var(--color-primary-content);
}

.badge-ghost {
  border-color: var(--color-base-200);
  background-color: var(--color-base-200);
  color: var(--color-base-content);
  background-image: none;
}

.badge-sm {
  --size: calc(var(--size-selector, .25rem) * 5);
  padding-inline: calc(.25rem * 2.5 - var(--border));
  font-size: .75rem;
}

.badge-lg {
  --size: calc(var(--size-selector, .25rem) * 7);
  padding-inline: calc(.25rem * 3.5 - var(--border));
  font-size: 1rem;
}

.list {
  flex-direction: column;
  font-size: .875rem;
  display: flex;
}

.list :where(.list-row) {
  --list-grid-cols: minmax(0, auto) 1fr;
  border-radius: var(--radius-box);
  word-break: break-word;
  grid-auto-flow: column;
  grid-template-columns: var(--list-grid-cols);
  gap: 1rem;
  padding: 1rem;
  display: grid;
  position: relative;
}

.list :where(.list-row):has(.list-col-grow:first-child) {
  --list-grid-cols: 1fr;
}

.list :where(.list-row):has(.list-col-grow:nth-child(2)) {
  --list-grid-cols: minmax(0, auto) 1fr;
}

.list :where(.list-row):has(.list-col-grow:nth-child(3)) {
  --list-grid-cols: minmax(0, auto) minmax(0, auto) 1fr;
}

.list :where(.list-row):has(.list-col-grow:nth-child(4)) {
  --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
}

.list :where(.list-row):has(.list-col-grow:nth-child(5)) {
  --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
}

.list :where(.list-row):has(.list-col-grow:nth-child(6)) {
  --list-grid-cols: minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) minmax(0, auto) 1fr;
}

.list :where(.list-row) :not(.list-col-wrap) {
  grid-row-start: 1;
}

.list > :not(:last-child).list-row:after, .list > :not(:last-child) .list-row:after {
  content: "";
  border-bottom: var(--border) solid;
  inset-inline: var(--radius-box);
  border-color: color-mix(in oklab, var(--color-base-content) 5%, transparent);
  position: absolute;
  bottom: 0;
}

.mockup-browser .mockup-browser-toolbar .\!input {
  background-color: var(--color-base-200) !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  direction: ltr !important;
  align-items: center !important;
  gap: .5rem !important;
  height: 100% !important;
  margin-inline: auto !important;
  font-size: .75rem !important;
  display: flex !important;
  overflow: hidden !important;
}

.mockup-browser .mockup-browser-toolbar .input {
  background-color: var(--color-base-200);
  text-overflow: ellipsis;
  white-space: nowrap;
  direction: ltr;
  align-items: center;
  gap: .5rem;
  height: 100%;
  margin-inline: auto;
  font-size: .75rem;
  display: flex;
  overflow: hidden;
}

.mockup-browser .mockup-browser-toolbar .\!input:before {
  content: "" !important;
  opacity: .3 !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='currentColor' class='size-4'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' clip-rule='evenodd' /%3E%3C/svg%3E%0A") !important;
  width: 1rem !important;
  height: 1rem !important;
}

.mockup-browser .mockup-browser-toolbar .input:before {
  content: "";
  opacity: .3;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='currentColor' class='size-4'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' clip-rule='evenodd' /%3E%3C/svg%3E%0A");
  width: 1rem;
  height: 1rem;
}

@keyframes dropdown {
  0% {
    opacity: 0;
  }
}

.\!input {
  cursor: text !important;
  border: var(--border) solid #0000 !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-color: var(--color-base-100) !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  width: clamp(3rem, 20rem, 100%) !important;
  height: var(--size) !important;
  touch-action: manipulation !important;
  border-color: var(--input-color) !important;
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset !important;
  --size: calc(var(--size-field, .25rem) * 10) !important;
  --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000) !important;
  border-start-start-radius: var(--join-ss, var(--radius-field)) !important;
  border-start-end-radius: var(--join-se, var(--radius-field)) !important;
  border-end-end-radius: var(--join-ee, var(--radius-field)) !important;
  border-end-start-radius: var(--join-es, var(--radius-field)) !important;
  flex-shrink: 1 !important;
  align-items: center !important;
  gap: .5rem !important;
  padding-inline: .75rem !important;
  font-size: .875rem !important;
  display: inline-flex !important;
  position: relative !important;
}

.input {
  cursor: text;
  border: var(--border) solid #0000;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: var(--color-base-100);
  vertical-align: middle;
  white-space: nowrap;
  width: clamp(3rem, 20rem, 100%);
  height: var(--size);
  touch-action: manipulation;
  border-color: var(--input-color);
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * .1)) inset;
  --size: calc(var(--size-field, .25rem) * 10);
  --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
  border-start-start-radius: var(--join-ss, var(--radius-field));
  border-start-end-radius: var(--join-se, var(--radius-field));
  border-end-end-radius: var(--join-ee, var(--radius-field));
  border-end-start-radius: var(--join-es, var(--radius-field));
  flex-shrink: 1;
  align-items: center;
  gap: .5rem;
  padding-inline: .75rem;
  font-size: .875rem;
  display: inline-flex;
  position: relative;
}

.\!input:where(input) {
  display: inline-flex !important;
}

.input:where(input) {
  display: inline-flex;
}

.\!input :where(input) {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  appearance: none !important;
  background-color: #0000 !important;
  border: none !important;
  width: 100% !important;
  height: 100% !important;
  display: inline-flex !important;
}

.input :where(input) {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: #0000;
  border: none;
  width: 100%;
  height: 100%;
  display: inline-flex;
}

.input :where(input):focus, .input :where(input):focus-within {
  --tw-outline-style: none;
  outline-style: none;
}

.\!input :where(input):focus, .\!input :where(input):focus-within {
  --tw-outline-style: none !important;
  outline-style: none !important;
}

@media (forced-colors: active) {
  .input :where(input):focus, .input :where(input):focus-within {
    outline-offset: 2px;
    outline: 2px solid #0000;
  }

  .\!input :where(input):focus, .\!input :where(input):focus-within {
    outline-offset: 2px !important;
    outline: 2px solid #0000 !important;
  }
}

.input :where(input[type="url"]), .input :where(input[type="email"]) {
  direction: ltr;
}

.\!input :where(input[type="url"]), .\!input :where(input[type="email"]) {
  direction: ltr !important;
}

.\!input :where(input[type="date"]) {
  display: inline-block !important;
}

.input :where(input[type="date"]) {
  display: inline-block;
}

.input:focus, .input:focus-within {
  --input-color: var(--color-base-content);
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
  outline: 2px solid var(--input-color);
  outline-offset: 2px;
  isolation: isolate;
  z-index: 1;
}

.\!input:focus, .\!input:focus-within {
  --input-color: var(--color-base-content) !important;
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) !important;
  outline: 2px solid var(--input-color) !important;
  outline-offset: 2px !important;
  isolation: isolate !important;
  z-index: 1 !important;
}

.input:has( > input[disabled]), .input:is(:disabled, [disabled]) {
  cursor: not-allowed;
  border-color: var(--color-base-200);
  background-color: var(--color-base-200);
  color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
}

.\!input:has( > input[disabled]), .\!input:is(:disabled, [disabled]) {
  cursor: not-allowed !important;
  border-color: var(--color-base-200) !important;
  background-color: var(--color-base-200) !important;
  color: color-mix(in oklab, var(--color-base-content) 40%, transparent) !important;
}

.input:has( > input[disabled])::-moz-placeholder, .input:is(:disabled, [disabled])::-moz-placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
}

.input:has( > input[disabled])::placeholder, .input:is(:disabled, [disabled])::placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
}

.\!input:has( > input[disabled])::-moz-placeholder, .\!input:is(:disabled, [disabled])::-moz-placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
}

.\!input:has( > input[disabled])::placeholder, .\!input:is(:disabled, [disabled])::placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
}

.input:has( > input[disabled]), .input:is(:disabled, [disabled]) {
  box-shadow: none;
}

.\!input:has( > input[disabled]), .\!input:is(:disabled, [disabled]) {
  box-shadow: none !important;
}

.\!input:has( > input[disabled]) > input[disabled] {
  cursor: not-allowed !important;
}

.input:has( > input[disabled]) > input[disabled] {
  cursor: not-allowed;
}

.\!input::-webkit-date-and-time-value {
  text-align: inherit !important;
}

.input::-webkit-date-and-time-value {
  text-align: inherit;
}

.\!input[type="number"]::-webkit-inner-spin-button {
  margin-block: -.75rem !important;
  margin-inline-end: -.75rem !important;
}

.input[type="number"]::-webkit-inner-spin-button {
  margin-block: -.75rem;
  margin-inline-end: -.75rem;
}

.\!input::-webkit-calendar-picker-indicator {
  position: absolute !important;
  inset-inline-end: .75em !important;
}

.input::-webkit-calendar-picker-indicator {
  position: absolute;
  inset-inline-end: .75em;
}

.chat {
  -moz-column-gap: calc(.25rem * 3);
  --mask-chat: url("data:image/svg+xml,%3csvg width='13' height='13' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 11.5004C0 13.0004 2 13.0004 2 13.0004H12H13V0.00036329L12.5 0C12.5 0 11.977 2.09572 11.8581 2.50033C11.6075 3.35237 10.9149 4.22374 9 5.50036C6 7.50036 0 10.0004 0 11.5004Z'/%3e%3c/svg%3e");
  column-gap: .75rem;
  padding-block: .25rem;
  display: grid;
}

.chat-bubble {
  border-radius: var(--radius-field);
  background-color: var(--color-base-300);
  width: fit-content;
  color: var(--color-base-content);
  grid-row-end: 3;
  min-width: 2.5rem;
  max-width: 90%;
  min-height: 2rem;
  padding-block: .5rem;
  padding-inline: 1rem;
  display: block;
  position: relative;
}

.chat-bubble:before {
  background-color: inherit;
  content: "";
  width: .75rem;
  height: .75rem;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-image: var(--mask-chat);
  mask-image: var(--mask-chat);
  position: absolute;
  bottom: 0;
  -webkit-mask-position: 0 -1px;
  mask-position: 0 -1px;
  -webkit-mask-size: 13px;
  mask-size: 13px;
}

.chat-bubble-primary {
  background-color: var(--color-primary);
  color: var(--color-primary-content);
}

.chat-bubble-accent {
  background-color: var(--color-accent);
  color: var(--color-accent-content);
}

.chat-image {
  grid-row: span 2 / span 2;
  align-self: flex-end;
}

.chat-footer {
  grid-row-start: 3;
  gap: .25rem;
  font-size: .6875rem;
  display: flex;
}

.chat-start {
  grid-template-columns: auto 1fr;
  place-items: start;
}

.chat-start .chat-header, .chat-start .chat-footer {
  grid-column-start: 2;
}

.chat-start .chat-image {
  grid-column-start: 1;
}

.chat-start .chat-bubble {
  border-end-start-radius: 0;
  grid-column-start: 2;
}

.chat-start .chat-bubble:before {
  inset-inline-start: -.75rem;
  transform: rotateY(0);
}

[dir="rtl"] .chat-start .chat-bubble:before {
  transform: rotateY(180deg);
}

.chat-end {
  grid-template-columns: 1fr auto;
  place-items: end;
}

.chat-end .chat-header, .chat-end .chat-footer {
  grid-column-start: 1;
}

.chat-end .chat-image {
  grid-column-start: 2;
}

.chat-end .chat-bubble {
  border-end-end-radius: 0;
  grid-column-start: 1;
}

.chat-end .chat-bubble:before {
  inset-inline-start: 100%;
  transform: rotateY(180deg);
}

[dir="rtl"] .chat-end .chat-bubble:before {
  transform: rotateY(0);
}

@keyframes rating {
  0%, 40% {
    filter: brightness(1.05) contrast(1.05);
    scale: 1.1;
  }
}

.stats {
  border-radius: var(--radius-box);
  grid-auto-flow: column;
  display: inline-grid;
  position: relative;
  overflow-x: auto;
}

.stat {
  -moz-column-gap: calc(.25rem * 4);
  grid-template-columns: repeat(1, 1fr);
  column-gap: 1rem;
  width: 100%;
  padding-block: 1rem;
  padding-inline: 1.5rem;
  display: inline-grid;
}

.stat:not(:last-child) {
  border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
  border-block-end: none;
}

.stat-figure {
  grid-row: 1 / span 3;
  grid-column-start: 2;
  place-self: center flex-end;
}

.stat-title {
  white-space: nowrap;
  color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
  grid-column-start: 1;
  font-size: .75rem;
}

.stat-value {
  white-space: nowrap;
  grid-column-start: 1;
  font-size: 2rem;
  font-weight: 800;
}

.stat-desc {
  white-space: nowrap;
  color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
  grid-column-start: 1;
  font-size: .75rem;
}

.stats-horizontal .stat:not(:last-child) {
  border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
  border-block-end: none;
}

.stats-vertical {
  grid-auto-flow: row;
  overflow-y: auto;
}

.stats-vertical .stat:not(:last-child) {
  border-inline-end: none;
  border-block-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: .5rem;
}

.mb-3 {
  margin-bottom: .75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.ml-1 {
  margin-left: .25rem;
}

.mt-3 {
  margin-top: .75rem;
}

.inline-block {
  display: inline-block;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-16 {
  height: 4rem;
}

.h-4 {
  height: 1rem;
}

.h-5 {
  height: 1.25rem;
}

.h-8 {
  height: 2rem;
}

.h-\[calc\(100vh-5rem\)\] {
  height: calc(100vh - 5rem);
}

.h-full {
  height: 100%;
}

.min-h-\[calc\(100vh-5rem\)\] {
  min-height: calc(100vh - 5rem);
}

.min-h-screen {
  min-height: 100vh;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-16 {
  width: 4rem;
}

.w-2 {
  width: .5rem;
}

.w-4 {
  width: 1rem;
}

.w-8 {
  width: 2rem;
}

.w-96 {
  width: 24rem;
}

.w-full {
  width: 100%;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-5xl {
  max-width: 64rem;
}

.max-w-lg {
  max-width: 32rem;
}

.flex-1 {
  flex: 1;
}

.grow {
  flex-grow: 1;
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes pulse {
  50% {
    opacity: .5;
  }
}

.animate-pulse {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: 1s linear infinite spin;
}

.cursor-pointer {
  cursor: pointer;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.gap-2 {
  gap: .5rem;
}

.gap-3 {
  gap: .75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-8 {
  gap: 2rem;
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.25rem * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.75rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.overflow-y-auto {
  overflow-y: auto;
}

.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: .5rem;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-l {
  border-left-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-base-200 {
  border-color: var(--color-base-200);
}

.border-base-300 {
  border-color: var(--color-base-300);
}

.border-current {
  border-color: currentColor;
}

.border-t-transparent {
  border-top-color: #0000;
}

.bg-base-100 {
  background-color: var(--color-base-100);
}

.bg-base-200 {
  background-color: var(--color-base-200);
}

.bg-current {
  background-color: currentColor;
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-accent {
  --tw-gradient-from: var(--color-accent) var(--tw-gradient-from-position);
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-base-100 {
  --tw-gradient-from: var(--color-base-100) var(--tw-gradient-from-position);
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-base-200 {
  --tw-gradient-from: var(--color-base-200) var(--tw-gradient-from-position);
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-primary {
  --tw-gradient-from: var(--color-primary) var(--tw-gradient-from-position);
  --tw-gradient-to: #fff0 var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-base-100 {
  --tw-gradient-to: var(--color-base-100) var(--tw-gradient-to-position);
}

.to-base-200 {
  --tw-gradient-to: var(--color-base-200) var(--tw-gradient-to-position);
}

.to-base-300 {
  --tw-gradient-to: var(--color-base-300) var(--tw-gradient-to-position);
}

.to-secondary {
  --tw-gradient-to: var(--color-secondary) var(--tw-gradient-to-position);
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.p-3 {
  padding: .75rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.text-center {
  text-align: center;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-extrabold {
  font-weight: 800;
}

.font-medium {
  font-weight: 500;
}

.leading-relaxed {
  line-height: 1.625;
}

.text-accent {
  color: var(--color-accent);
}

.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-secondary);
}

.text-transparent {
  color: #0000;
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.opacity-50 {
  opacity: .5;
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px #00000040;
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 #0000000d;
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 #0000000d;
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.duration-300 {
  transition-duration: .3s;
}

.chat-container {
  height: calc(100vh - 4rem);
}

.artifacts-panel {
  height: calc(100vh - 4rem);
  overflow-y: auto;
}

.message-bubble {
  animation: .3s ease-out fadeInUp;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-indicator {
  animation: 1.5s ease-in-out infinite pulse;
}

.achievement-celebration {
  animation: .6s ease-out bounceIn;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }

  70% {
    transform: scale(.9);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.hover\:btn-primary:hover {
  --btn-color: var(--color-primary);
  --btn-fg: var(--color-primary-content);
}

.hover\:btn-secondary:hover {
  --btn-color: var(--color-secondary);
  --btn-fg: var(--color-secondary-content);
}

.hover\:btn-accent:hover {
  --btn-color: var(--color-accent);
  --btn-fg: var(--color-accent-content);
}

.hover\:badge-primary:hover {
  --badge-color: var(--color-primary);
  --badge-fg: var(--color-primary-content);
}

.hover\:badge-secondary:hover {
  --badge-color: var(--color-secondary);
  --badge-fg: var(--color-secondary-content);
}

.hover\:badge-accent:hover {
  --badge-color: var(--color-accent);
  --badge-fg: var(--color-accent-content);
}

.hover\:text-accent:hover {
  color: var(--color-accent);
}

.hover\:text-secondary:hover {
  color: var(--color-secondary);
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px #0000001a, 0 8px 10px -6px #0000001a;
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:shadow-md:focus {
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (width >= 640px) {
  .sm\:flex-row {
    flex-direction: row;
  }
}

@media (width >= 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__9a168d68._.css.map*/