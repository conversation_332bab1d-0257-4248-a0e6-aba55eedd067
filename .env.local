# Database
DATABASE_URL="postgresql://username:password@localhost:5432/influtify"

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Y29tcG9zZWQtbW9jY2FzaW4tOTguY2xlcmsuYWNjb3VudHMuZGV2JA
CLERK_SECRET_KEY=sk_test_0T17DZqqEott42mOYaT5cFlRWO6go2Tb4gPrrZMZjt
CLERK_WEBHOOK_SECRET="your_clerk_webhook_secret"

# AI Providers (choose one)
AI_PROVIDER="openai"
OPENAI_API_KEY="your_openai_api_key"
# ANTHROPIC_API_KEY="your_anthropic_api_key"
# GEMINI_API_KEY="your_gemini_api_key"

# Stripe (for subscriptions)
STRIPE_PUBLISHABLE_KEY="your_stripe_publishable_key"
STRIPE_SECRET_KEY="your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="your_stripe_webhook_secret"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3001"

# Default provider (optional, defaults to 'deepseek')
AI_PROVIDER=gemini

# API Keys for different providers
DEEPSEEK_API_KEY=***********************************
ANTHROPIC_API_KEY=************************************************************************************************************
OPENAI_API_KEY=********************************************************************************************************************************************************************
GEMINI_API_KEY=AIzaSyDr5OBKkSBNb6kQRSk16hKoCdI-oC8plXI
GEMINI_PROJECT_NUMBER=779721070036

