{"version": 3, "sources": [], "sections": [{"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/app/dashboard/page.tsx"], "sourcesContent": ["import { auth } from '@clerk/nextjs/server';\nimport { redirect } from 'next/navigation';\nimport { UserButton } from '@clerk/nextjs';\nimport Link from 'next/link';\nimport ChatInterface from '@/components/chat/ChatInterface';\n\nexport default async function DashboardPage() {\n  const { userId } = await auth();\n  \n  if (!userId) {\n    redirect('/');\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-base-200 to-base-300\">\n      {/* Navigation */}\n      <div className=\"navbar bg-base-100/90 backdrop-blur-md shadow-lg border-b border-base-300\">\n        <div className=\"navbar-start\">\n          <Link href=\"/\" className=\"btn btn-ghost text-xl font-bold text-primary hover:text-primary-focus\">\n            🚀 Influtify\n          </Link>\n        </div>\n        <div className=\"navbar-center\">\n          <div className=\"tabs tabs-boxed bg-base-200 shadow-inner\">\n            <a className=\"tab tab-active text-primary font-medium\">💬 Chat</a>\n            <a className=\"tab hover:text-secondary\">🗺️ Roadmap</a>\n            <a className=\"tab hover:text-accent\">📊 Progress</a>\n          </div>\n        </div>\n        <div className=\"navbar-end\">\n          <UserButton />\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex h-[calc(100vh-5rem)]\">\n        {/* Chat Panel */}\n        <div className=\"flex-1 flex flex-col bg-base-100 shadow-lg\">\n          <div className=\"bg-gradient-to-r from-primary/5 to-secondary/5 p-6 border-b border-base-200\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-2xl\">\n                🤖\n              </div>\n              <div>\n                <h2 className=\"text-2xl font-bold text-primary\">AI Mentor Chat</h2>\n                <p className=\"text-sm text-base-content/60\">Ask me anything about growing your social media presence</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Chat Messages Area */}\n          <div className=\"flex-1 p-6 overflow-y-auto bg-gradient-to-b from-base-50 to-base-100\">\n            <div className=\"space-y-6 max-w-4xl\">\n              {/* Welcome Message */}\n              <div className=\"chat chat-start\">\n                <div className=\"chat-image avatar\">\n                  <div className=\"w-12 rounded-full bg-gradient-to-br from-primary to-secondary text-white flex items-center justify-center shadow-lg\">\n                    🤖\n                  </div>\n                </div>\n                <div className=\"chat-bubble chat-bubble-primary shadow-lg max-w-lg\">\n                  <div className=\"font-medium mb-2\">Welcome to Influtify! 🚀</div>\n                  I'm your AI social media growth mentor. I'll help you build your presence using proven viral strategies from Brendan Kane's methods.\n                  <br /><br />\n                  <div className=\"bg-primary/10 rounded-lg p-3 mt-3\">\n                    <div className=\"font-medium text-primary-content/90 mb-2\">To get started, tell me:</div>\n                    <div className=\"space-y-1 text-sm\">\n                      <div>• What platform do you want to focus on?</div>\n                      <div>• What's your current follower count?</div>\n                      <div>• What type of content do you create?</div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          {/* Chat Input */}\n          <div className=\"bg-base-100 p-6 border-t border-base-200\">\n            <div className=\"flex gap-3\">\n              <input\n                type=\"text\"\n                placeholder=\"Type your message...\"\n                className=\"input input-bordered flex-1 shadow-sm focus:shadow-md transition-shadow\"\n                disabled\n              />\n              <button className=\"btn btn-primary shadow-lg hover:shadow-xl transition-all gap-2\" disabled>\n                <span>Send</span>\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n                </svg>\n              </button>\n            </div>\n            <div className=\"flex items-center gap-2 mt-3\">\n              <div className=\"badge badge-warning badge-sm\">⚠️</div>\n              <p className=\"text-xs text-base-content/50\">Chat functionality coming soon...</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Artifacts Panel */}\n        <div className=\"w-96 bg-base-100 border-l border-base-200 shadow-xl\">\n          <div className=\"bg-gradient-to-r from-secondary/5 to-accent/5 p-6 border-b border-base-200\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-10 h-10 bg-secondary/10 rounded-full flex items-center justify-center text-xl\">\n                ⚡\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold text-secondary\">Interactive Tools</h3>\n                <p className=\"text-sm text-base-content/60\">Roadmaps, progress tracking, and more</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"p-6 space-y-6 overflow-y-auto h-full\">\n            {/* Quick Stats */}\n            <div className=\"stats stats-vertical shadow-lg w-full bg-gradient-to-br from-base-100 to-base-200\">\n              <div className=\"stat bg-gradient-to-br from-primary/5 to-primary/10\">\n                <div className=\"stat-figure text-primary\">\n                  <div className=\"w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center\">\n                    🏆\n                  </div>\n                </div>\n                <div className=\"stat-title text-primary/70\">Level</div>\n                <div className=\"stat-value text-primary text-2xl\">1</div>\n                <div className=\"stat-desc text-primary/60\">Beginner</div>\n              </div>\n\n              <div className=\"stat bg-gradient-to-br from-secondary/5 to-secondary/10\">\n                <div className=\"stat-figure text-secondary\">\n                  <div className=\"w-8 h-8 rounded-full bg-secondary/20 flex items-center justify-center\">\n                    ⭐\n                  </div>\n                </div>\n                <div className=\"stat-title text-secondary/70\">XP Points</div>\n                <div className=\"stat-value text-secondary text-2xl\">0</div>\n                <div className=\"stat-desc text-secondary/60\">Start your journey!</div>\n              </div>\n\n              <div className=\"stat bg-gradient-to-br from-accent/5 to-accent/10\">\n                <div className=\"stat-figure text-accent\">\n                  <div className=\"w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center\">\n                    🔥\n                  </div>\n                </div>\n                <div className=\"stat-title text-accent/70\">Streak</div>\n                <div className=\"stat-value text-accent text-2xl\">0</div>\n                <div className=\"stat-desc text-accent/60\">Keep going!</div>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"card bg-gradient-to-br from-base-200 to-base-300 shadow-lg border border-base-300\">\n              <div className=\"card-body p-5\">\n                <h4 className=\"card-title text-base text-primary mb-4 flex items-center gap-2\">\n                  <span>🚀</span>\n                  Quick Start\n                </h4>\n                <div className=\"space-y-3\">\n                  <button className=\"btn btn-sm btn-outline btn-primary w-full hover:btn-primary hover:text-white transition-all gap-2\">\n                    <span>📊</span>\n                    Set Goals\n                  </button>\n                  <button className=\"btn btn-sm btn-outline btn-secondary w-full hover:btn-secondary hover:text-white transition-all gap-2\">\n                    <span>🗺️</span>\n                    View Roadmap\n                  </button>\n                  <button className=\"btn btn-sm btn-outline btn-accent w-full hover:btn-accent hover:text-white transition-all gap-2\">\n                    <span>📚</span>\n                    Browse Content Blocks\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Achievements */}\n            <div className=\"card bg-gradient-to-br from-base-200 to-base-300 shadow-lg border border-base-300\">\n              <div className=\"card-body p-5\">\n                <h4 className=\"card-title text-base text-secondary mb-3 flex items-center gap-2\">\n                  <span>🏆</span>\n                  Achievements\n                </h4>\n                <p className=\"text-xs text-base-content/60 mb-4\">Complete actions to unlock badges!</p>\n                <div className=\"grid grid-cols-3 gap-3\">\n                  <div className=\"tooltip\" data-tip=\"First Steps\">\n                    <div className=\"badge badge-ghost p-3 hover:badge-primary transition-all cursor-pointer\">🏆</div>\n                  </div>\n                  <div className=\"tooltip\" data-tip=\"Rising Star\">\n                    <div className=\"badge badge-ghost p-3 hover:badge-secondary transition-all cursor-pointer\">⭐</div>\n                  </div>\n                  <div className=\"tooltip\" data-tip=\"Growth Rocket\">\n                    <div className=\"badge badge-ghost p-3 hover:badge-accent transition-all cursor-pointer\">🚀</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;;;;;;AAGe,eAAe;IAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;IAE5B,IAAI,CAAC,QAAQ;QACX,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAwE;;;;;;;;;;;kCAInG,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAA2B;;;;;;8CACxC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAGzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sLAAA,CAAA,aAAU;;;;;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAiF;;;;;;sDAGhG,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAkC;;;;;;8DAChD,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;0CAMlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAAsH;;;;;;;;;;;0DAIvI,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAmB;;;;;;oDAA8B;kEAEhE,8OAAC;;;;;kEAAK,8OAAC;;;;;kEACP,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA2C;;;;;;0EAC1D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAI;;;;;;kFACL,8OAAC;kFAAI;;;;;;kFACL,8OAAC;kFAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;0DAEV,8OAAC;gDAAO,WAAU;gDAAiE,QAAQ;;kEACzF,8OAAC;kEAAK;;;;;;kEACN,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;kDAI3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA+B;;;;;;0DAC9C,8OAAC;gDAAE,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;;;;;;;kCAMlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAkF;;;;;;sDAGjG,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;0CAKlD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAsE;;;;;;;;;;;kEAIvF,8OAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAC5C,8OAAC;wDAAI,WAAU;kEAAmC;;;;;;kEAClD,8OAAC;wDAAI,WAAU;kEAA4B;;;;;;;;;;;;0DAG7C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAwE;;;;;;;;;;;kEAIzF,8OAAC;wDAAI,WAAU;kEAA+B;;;;;;kEAC9C,8OAAC;wDAAI,WAAU;kEAAqC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;kEAA8B;;;;;;;;;;;;0DAG/C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEAAqE;;;;;;;;;;;kEAItF,8OAAC;wDAAI,WAAU;kEAA4B;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;kEAAkC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;kEAA2B;;;;;;;;;;;;;;;;;;kDAK9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAK;;;;;;wDAAS;;;;;;;8DAGjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC;8EAAK;;;;;;gEAAS;;;;;;;sEAGjB,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC;8EAAK;;;;;;gEAAU;;;;;;;sEAGlB,8OAAC;4DAAO,WAAU;;8EAChB,8OAAC;8EAAK;;;;;;gEAAS;;;;;;;;;;;;;;;;;;;;;;;;kDAQvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAK;;;;;;wDAAS;;;;;;;8DAGjB,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAU,YAAS;sEAChC,cAAA,8OAAC;gEAAI,WAAU;0EAA0E;;;;;;;;;;;sEAE3F,8OAAC;4DAAI,WAAU;4DAAU,YAAS;sEAChC,cAAA,8OAAC;gEAAI,WAAU;0EAA4E;;;;;;;;;;;sEAE7F,8OAAC;4DAAI,WAAU;4DAAU,YAAS;sEAChC,cAAA,8OAAC;gEAAI,WAAU;0EAAyE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5G", "debugId": null}}]}