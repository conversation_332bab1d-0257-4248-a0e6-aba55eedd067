# Influtify Project Setup Prompt (Updated)

## Project Overview
Create a new SaaS application called "Influtify" - an AI-powered social media growth mentor that helps users build their social media presence using principles from <PERSON>'s "The Guide to Going Viral" book. The application will feature a conversational AI chat interface that naturally extracts user information and provides personalized roadmaps, content strategies, and ongoing guidance through an intuitive chat experience with interactive artifacts.

## Tech Stack Requirements
- NextJS with sliced-feature project structure
- Drizzle ORM for database operations
- Zod for schema validation
- DaisyUI for component library (with Tailwind CSS)
- Stripe for payment processing
- Local Postgres Database (during development)
- Next-intl for multilanguage support (English default, with Spanish as secondary language)
- Clerk for authentication
- Hybrid AI API integration (ability to swap between OpenAI, Anthropic, Gemini, etc.)

## Project Structure
Set up a NextJS project with a sliced-feature architecture with the following structure:

```
/src
  /app - Next.js app router
  /features
    /auth - Authentication related features
    /chat - AI chat interface and conversation management
    /artifacts - Interactive components rendered in artifacts area
    /dashboard - Main user dashboard
    /roadmap - Roadmap creation and management
    /content-blocks - Content strategy blocks
    /ai-mentor - AI mentorship and adaptation
    /analytics - User progress tracking
    /subscription - Payment and subscription management
  /shared
    /ui - Reusable UI components (DaisyUI based)
    /lib - Shared utilities
    /api - API client and integration
    /config - Configuration files
    /hooks - Custom React hooks
    /types - TypeScript type definitions
    /db - Database schema and operations
    /ai - AI provider abstraction and prompt management
```

## Database Schema Setup
Create a Drizzle ORM schema with the following main tables:
- users (linked to Clerk)
- conversations (storing chat conversations)
- messages (storing individual chat messages)
- user_profiles (storing extracted user information)
- roadmaps (storing user roadmaps)
- content_blocks (storing content strategy blocks)
- achievements (storing user achievements)
- progress_metrics (storing user progress data)
- ai_recommendations (storing AI recommendations history)
- artifacts (storing rendered artifacts and their state)

## Environment Configuration
Set up a comprehensive .env.example file with all required environment variables:
- Database connection
- Clerk authentication
- Stripe API keys
- AI provider API keys (OpenAI, Anthropic, Gemini)
- Next-intl configuration

## AI Provider Integration
Create a flexible AI provider integration that can be easily switched:

```typescript
// Example of the AI provider abstraction
export type AIProvider = 'openai' | 'anthropic' | 'gemini';

export interface AIProviderConfig {
  provider: AIProvider;
  apiKey: string;
  modelName: string;
}

export const getAIProvider = (): AIProviderConfig => {
  return {
    provider: (process.env.AI_PROVIDER as AIProvider) || 'openai',
    apiKey: process.env.AI_API_KEY || '',
    modelName: process.env.AI_MODEL_NAME || 'gpt-4',
  };
};

// AI Chat Service with function calling capabilities
export interface AIFunction {
  name: string;
  description: string;
  parameters: any;
}

export interface AIChatMessage {
  role: 'system' | 'user' | 'assistant' | 'function';
  content: string;
  function_call?: {
    name: string;
    arguments: string;
  };
}
```

## Authentication Setup
Implement Clerk authentication with:
- Sign up/Sign in pages
- User profile management
- Role-based access control (free users vs. premium users)

## DaisyUI Configuration
Set up DaisyUI with Tailwind CSS:

```javascript
// tailwind.config.js
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/features/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {},
  },
  plugins: [require("daisyui")],
  daisyui: {
    themes: [
      {
        influtify: {
          "primary": "#6366f1",
          "secondary": "#8b5cf6",
          "accent": "#06b6d4",
          "neutral": "#1f2937",
          "base-100": "#ffffff",
          "info": "#3abff8",
          "success": "#36d399",
          "warning": "#fbbd23",
          "error": "#f87272",
        },
      },
      "light",
      "dark",
    ],
  },
}
```

## Initial Pages and Components
Create the following initial pages and components:

### Landing Page
- Product description and value proposition
- Sign-up CTA with Clerk integration
- Responsive design using DaisyUI components

### Chat Interface Layout
- Split-screen layout with chat on the left and artifacts on the right
- Responsive design that stacks on mobile
- Chat input with send button and typing indicators
- Message bubbles with proper styling
- Artifacts area that can render interactive components

### Core Chat Components
```typescript
// Chat message component
interface ChatMessageProps {
  message: {
    id: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    artifacts?: ArtifactData[];
  };
}

// Artifacts renderer component
interface ArtifactsRendererProps {
  artifacts: ArtifactData[];
  onArtifactInteraction: (artifactId: string, action: string, data: any) => void;
}

// Chat input component
interface ChatInputProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  placeholder?: string;
}
```

### Authentication Pages
- Login/signup pages using Clerk components
- Styled with DaisyUI for consistent design

## Multilanguage Support
Set up next-intl with:
- English as default language
- Spanish as secondary language
- Language switcher component
- Translations for all UI elements including chat interface

## AI Chat System Architecture
Implement a conversational AI system that:

### Information Extraction
- Naturally extracts user information through conversation
- Maintains context across multiple messages
- Uses function calling to structure extracted data
- Validates and stores user profile information incrementally

### Artifact Generation
- Renders interactive components in the artifacts area
- Supports goal selection interfaces
- Displays progress visualizations
- Shows roadmap components
- Handles user interactions and updates

### Conversation Flow Management
- Manages conversation state and context
- Handles information gathering workflows
- Triggers artifact rendering at appropriate times
- Maintains conversation history

## Deployment Configuration
Set up the project for easy deployment to Vercel with:
- Production, staging, and development environments
- Environment variable configuration
- Database migration scripts

## Testing Setup
Configure testing environment with:
- Jest for unit testing
- React Testing Library for component testing
- Playwright for E2E testing
- AI conversation flow testing

## Additional Requirements
- Implement responsive design for all pages using DaisyUI
- Set up proper SEO metadata
- Implement error boundaries and fallbacks
- Create loading states for all async operations including AI responses
- Set up logging and monitoring
- Implement real-time chat updates
- Create typing indicators for AI responses
- Add message persistence and conversation history
- Implement artifact state management
- Create smooth transitions between chat and artifacts

