{"name": "influtify", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:setup": "tsx scripts/setup-db.ts"}, "dependencies": {"@clerk/nextjs": "^6.23.2", "@stripe/stripe-js": "^7.4.0", "@types/pg": "^8.15.4", "daisyui": "^5.0.43", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "next": "15.3.4", "next-intl": "^4.3.4", "pg": "^8.16.3", "react": "^19.0.0", "react-dom": "^19.0.0", "stripe": "^18.3.0", "svix": "^1.68.0", "zod": "^3.25.71"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}