self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"7fa95381110dc8a87e46c3e975cf2a2b837fbbab1d\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"7faa50246fa66de3d7955699b37f282c51ed957fad\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"7fdf42b214b512e9d7144081e49abcb4238304cda6\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"7f6e4262816711b250416fed254839d6940c98d16d\": {\n      \"workers\": {\n        \"app/dashboard/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/dashboard/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"Z+L0XyhGbxGIFnFFqhDzP8pp2/xpDkdK34gpGGdrve4=\"\n}"