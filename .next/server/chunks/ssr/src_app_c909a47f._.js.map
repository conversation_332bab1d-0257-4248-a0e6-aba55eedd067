{"version": 3, "sources": [], "sections": [{"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/app/page.tsx"], "sourcesContent": ["import { SignIn<PERSON><PERSON>on, SignU<PERSON><PERSON><PERSON>on, <PERSON>r<PERSON><PERSON>on } from '@clerk/nextjs';\nimport { auth } from '@clerk/nextjs/server';\nimport Link from 'next/link';\n\nexport default async function Home() {\n  const { userId } = await auth();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary/10 to-secondary/10\">\n      {/* Navigation */}\n      <nav className=\"navbar bg-base-100 shadow-lg\">\n        <div className=\"navbar-start\">\n          <Link href=\"/\" className=\"btn btn-ghost text-xl font-bold text-primary\">\n            🚀 Influtify\n          </Link>\n        </div>\n        <div className=\"navbar-end\">\n          {userId ? (\n            <div className=\"flex items-center gap-4\">\n              <Link href=\"/dashboard\" className=\"btn btn-primary\">\n                Dashboard\n              </Link>\n              <UserButton />\n            </div>\n          ) : (\n            <div className=\"flex items-center gap-2\">\n              <SignInButton>\n                <button className=\"btn btn-ghost\">Sign In</button>\n              </SignInButton>\n              <SignUpButton>\n                <button className=\"btn btn-primary\">Get Started</button>\n              </SignUpButton>\n            </div>\n          )}\n        </div>\n      </nav>\n\n      {/* Hero Section */}\n      <div className=\"hero min-h-[calc(100vh-4rem)]\">\n        <div className=\"hero-content text-center\">\n          <div className=\"max-w-4xl\">\n            <h1 className=\"text-5xl font-bold mb-6\">\n              Grow Your Social Media with\n              <span className=\"text-primary\"> AI-Powered</span> Guidance\n            </h1>\n            <p className=\"text-xl mb-8 opacity-80\">\n              Master the art of going viral with personalized strategies based on Brendan Kane's proven methods.\n              Get AI mentoring, interactive roadmaps, and content creation tools all in one place.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n              {userId ? (\n                <Link href=\"/dashboard\" className=\"btn btn-primary btn-lg\">\n                  Continue Your Journey 🚀\n                </Link>\n              ) : (\n                <>\n                  <SignUpButton>\n                    <button className=\"btn btn-primary btn-lg\">\n                      Start Growing Today 🌟\n                    </button>\n                  </SignUpButton>\n                  <SignInButton>\n                    <button className=\"btn btn-outline btn-lg\">\n                      Sign In\n                    </button>\n                  </SignInButton>\n                </>\n              )}\n            </div>\n\n            {/* Features Grid */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-16\">\n              <div className=\"card bg-base-100 shadow-xl\">\n                <div className=\"card-body text-center\">\n                  <div className=\"text-4xl mb-4\">🤖</div>\n                  <h3 className=\"card-title justify-center\">AI Mentor</h3>\n                  <p>Get personalized guidance through natural conversation with your AI social media mentor.</p>\n                </div>\n              </div>\n\n              <div className=\"card bg-base-100 shadow-xl\">\n                <div className=\"card-body text-center\">\n                  <div className=\"text-4xl mb-4\">🗺️</div>\n                  <h3 className=\"card-title justify-center\">Interactive Roadmaps</h3>\n                  <p>Follow step-by-step roadmaps tailored to your goals and current follower count.</p>\n                </div>\n              </div>\n\n              <div className=\"card bg-base-100 shadow-xl\">\n                <div className=\"card-body text-center\">\n                  <div className=\"text-4xl mb-4\">🎨</div>\n                  <h3 className=\"card-title justify-center\">Viral Content Formats</h3>\n                  <p>Master the 4 proven viral formats with templates and real-world examples.</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,eAAe;IAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;IAE5B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAA+C;;;;;;;;;;;kCAI1E,8OAAC;wBAAI,WAAU;kCACZ,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAkB;;;;;;8CAGpD,8OAAC,sLAAA,CAAA,aAAU;;;;;;;;;;iDAGb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sLAAA,CAAA,eAAY;8CACX,cAAA,8OAAC;wCAAO,WAAU;kDAAgB;;;;;;;;;;;8CAEpC,8OAAC,sLAAA,CAAA,eAAY;8CACX,cAAA,8OAAC;wCAAO,WAAU;kDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA0B;kDAEtC,8OAAC;wCAAK,WAAU;kDAAe;;;;;;oCAAkB;;;;;;;0CAEnD,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;0CAKvC,8OAAC;gCAAI,WAAU;0CACZ,uBACC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAyB;;;;;yDAI3D;;sDACE,8OAAC,sLAAA,CAAA,eAAY;sDACX,cAAA,8OAAC;gDAAO,WAAU;0DAAyB;;;;;;;;;;;sDAI7C,8OAAC,sLAAA,CAAA,eAAY;sDACX,cAAA,8OAAC;gDAAO,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;0CASnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,8OAAC;8DAAE;;;;;;;;;;;;;;;;;kDAIP,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,8OAAC;8DAAE;;;;;;;;;;;;;;;;;kDAIP,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB", "debugId": null}}]}