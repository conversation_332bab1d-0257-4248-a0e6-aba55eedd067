{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_351a010a._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_466025e5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z+L0XyhGbxGIFnFFqhDzP8pp2/xpDkdK34gpGGdrve4=", "__NEXT_PREVIEW_MODE_ID": "d45072c3b21c2911654ba5e300c3b79e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "eb9a000da7120c0accf2f397750f9bcfb955cc9fb1259099559cedc428238ccf", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "68f628ade5db1cd2b66ed16f1d3ce966e22f80a54b2905870d55702762d1a732"}}}, "sortedMiddleware": ["/"], "functions": {}}