# Influtify - Augment Code Prompts (Updated Version)

## Project Overview
Influtify is an AI-powered social media growth mentor SaaS application that helps users build their social media presence using principles from <PERSON>'s "The Guide to Going Viral" book. This updated version features a conversational AI chat interface that naturally extracts user information and provides personalized roadmaps through interactive artifacts.

## Key Changes in This Version
- **UI Framework**: Changed from Shadcn UI to DaisyUI with Tailwind CSS
- **User Experience**: Replaced multi-step onboarding with conversational AI chat interface
- **Interface Layout**: Split-screen design with chat on left, artifacts on right
- **Information Gathering**: Natural conversation flow that extracts user data progressively
- **Interactive Components**: Artifacts area renders goal selection, progress visualization, and other interactive elements

## Tech Stack
- **Frontend**: NextJS with sliced-feature project structure
- **Database**: Drizzle ORM with local Postgres (development)
- **Validation**: Zod for schema validation
- **UI Components**: DaisyUI with Tailwind CSS
- **Authentication**: Clerk
- **Payments**: Stripe integration
- **Internationalization**: Next-intl (English default, Spanish secondary)
- **AI Integration**: Hybrid AI API (OpenAI, Anthropic, Gemini - configurable via .env)

## Prompt Files

### 1. Project Setup (`01_project_setup.md`)
- Initial NextJS project configuration
- Tech stack setup and dependencies
- Project structure with sliced-feature architecture
- Environment configuration
- Basic authentication and database setup

### 2. AI Chat Interface (`02_ai_chat_interface.md`)
- Conversational AI chat system
- Natural information extraction through conversation
- Function calling for structured data collection
- Chat message management and persistence
- Real-time conversation with typing indicators

### 3. Artifacts System (`03_artifacts_system.md`)
- Interactive component rendering system
- Goal selection artifacts
- Roadmap preview artifacts
- Progress visualization components
- User interaction handling

### 4. Content Block System (`04_content_block_system.md`)
- Content creation guidance based on viral formats
- Pre-built content blocks and templates
- Task management and progress tracking
- Integration with chat interface
- Content evaluation and optimization tools

### 5. AI Mentor Adaptation System (`05_ai_mentor_adaptation_system.md`)
- Progress monitoring and analysis
- Strategy adjustment recommendations
- Automated check-ins and interventions
- Performance tracking and optimization
- Conversational mentoring through chat

### 6. Achievement Tracking System (`06_achievement_tracking_system.md`)
- Gamification with badges and achievements
- Progress tracking and visualization
- User statistics and leveling system
- Activity streaks and milestones
- Celebration artifacts and notifications

## Implementation Order

We recommend implementing the features in this order:

1. **Start with Project Setup** - Establish the foundation
2. **Implement AI Chat Interface** - Core user interaction system
3. **Add Artifacts System** - Interactive component framework
4. **Build Content Block System** - Core content guidance functionality
5. **Integrate AI Mentor Adaptation** - Progress monitoring and adjustments
6. **Add Achievement Tracking** - Gamification and motivation

## Key Features

### Conversational AI Interface
- Natural language interaction for gathering user information
- Progressive information extraction through follow-up questions
- Context-aware responses based on previously gathered data
- Function calling for structured data collection

### Interactive Artifacts
- Goal selection components
- Roadmap visualization
- Progress dashboards
- Strategy adjustment interfaces
- Achievement celebrations

### Viral Content Formats
Based on Brendan Kane's book, the system supports 4 viral formats:
1. **Visual Metaphor Format** - Using visual comparisons to explain ideas
2. **Two Characters, One Lightbulb Format** - Teaching through character interaction
3. **Untold Stories Format** - Behind-the-scenes narratives
4. **30-Day Challenge Format** - Documenting transformation journeys

### AI Mentor Capabilities
- Weekly progress analysis
- Strategy adjustment recommendations
- Performance trend detection
- Automated interventions when off-track
- Motivational guidance and support

### Gamification Elements
- Achievement system with badges
- User leveling and experience points
- Activity streaks and milestones
- Progress visualization
- Celebration animations

## Environment Variables

Create a `.env.local` file with the following variables:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/influtify"

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="your_clerk_publishable_key"
CLERK_SECRET_KEY="your_clerk_secret_key"

# AI Provider Configuration
AI_PROVIDER="openai" # or "anthropic", "gemini"
OPENAI_API_KEY="your_openai_api_key"
ANTHROPIC_API_KEY="your_anthropic_api_key"
GEMINI_API_KEY="your_gemini_api_key"

# Stripe
STRIPE_PUBLISHABLE_KEY="your_stripe_publishable_key"
STRIPE_SECRET_KEY="your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="your_stripe_webhook_secret"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
```

## Database Setup

1. Install and start PostgreSQL locally
2. Create a database named `influtify`
3. Run the Drizzle migrations to set up the schema
4. Seed the database with default achievements and content blocks

## Development Workflow

1. Use each prompt file individually with Augment Code
2. Test each feature thoroughly before moving to the next
3. Ensure proper integration between chat interface and artifacts
4. Test AI function calling and conversation flows
5. Verify multilanguage support works correctly

## Key Considerations

### AI Integration
- The hybrid AI system allows switching between providers easily
- Function calling is used for structured data extraction
- Conversation context is maintained throughout the session
- AI responses are tailored to user's progress and goals

### User Experience
- Chat interface provides natural, conversational interaction
- Artifacts area shows relevant interactive components
- Progress is visualized clearly with animations and celebrations
- System adapts to user's pace and preferences

### Scalability
- Sliced-feature architecture supports easy feature additions
- Database schema is designed for performance and flexibility
- AI provider abstraction allows for easy switching
- Component system supports reusability and maintainability

## Support and Documentation

Each prompt file contains:
- Detailed technical specifications
- Complete code examples
- Database schemas
- API route definitions
- Component implementations
- Integration guidelines

For questions or clarifications, refer to the specific prompt file for the feature you're implementing.

## Next Steps After MVP

Future enhancements could include:
- Community features and user collaboration
- Advanced analytics and reporting
- Content calendar and scheduling
- Multi-platform social media integration
- Advanced AI coaching capabilities
- Team collaboration features
- White-label solutions for agencies

