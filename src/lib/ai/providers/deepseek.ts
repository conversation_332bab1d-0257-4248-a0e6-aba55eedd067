import { AIProvider, AIMessage, AIFunction, AIResponse, AIFunctionCall } from '../types';

export class DeepSeekProvider implements AIProvider {
  name = 'deepseek';
  private apiKey: string;
  private baseUrl = 'https://api.deepseek.com/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    messages: AIMessage[],
    functions?: AIFunction[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      model?: string;
    }
  ): Promise<AIResponse> {
    const requestBody: any = {
      model: options?.model || 'deepseek-chat',
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
      })),
      temperature: options?.temperature || 0.7,
      max_tokens: options?.maxTokens || 2000,
    };

    // DeepSeek supports function calling similar to OpenAI
    if (functions && functions.length > 0) {
      requestBody.tools = functions.map(func => ({
        type: 'function',
        function: {
          name: func.name,
          description: func.description,
          parameters: func.parameters,
        },
      }));
      requestBody.tool_choice = 'auto';
    }

    const response = await fetch(`${this.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`DeepSeek API error: ${response.status} ${response.statusText} ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    const choice = data.choices[0];
    const message = choice.message;

    let functionCall: AIFunctionCall | undefined;
    if (message.tool_calls && message.tool_calls.length > 0) {
      const toolCall = message.tool_calls[0];
      if (toolCall.type === 'function') {
        functionCall = {
          name: toolCall.function.name,
          arguments: JSON.parse(toolCall.function.arguments),
        };
      }
    }

    return {
      content: message.content || '',
      functionCall,
      usage: {
        promptTokens: data.usage?.prompt_tokens || 0,
        completionTokens: data.usage?.completion_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0,
      },
      model: data.model,
      finishReason: choice.finish_reason,
    };
  }
}
