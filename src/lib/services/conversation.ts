import { db } from '@/lib/db';
import { conversations, messages } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

export interface ConversationContext {
  userId: string;
  conversationId?: string;
  userProfile?: any;
  currentGoals?: string[];
  recentActivity?: any[];
}

export class ConversationService {
  private aiService = createAIService();

  /**
   * Create a new conversation
   */
  async createConversation(userId: string, title?: string): Promise<string> {
    const conversation = await db
      .insert(conversations)
      .values({
        userId,
        title: title || 'New Conversation',
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return conversation[0].id;
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(conversationId: string): Promise<AIMessage[]> {
    const messageHistory = await db
      .select()
      .from(messages)
      .where(eq(messages.conversationId, conversationId))
      .orderBy(messages.createdAt);

    return messageHistory.map(msg => ({
      role: msg.role as 'system' | 'user' | 'assistant',
      content: msg.content,
    }));
  }

  /**
   * Save message to conversation
   */
  async saveMessage(
    conversationId: string,
    role: 'user' | 'assistant',
    content: string,
    metadata?: any
  ): Promise<void> {
    await db.insert(messages).values({
      conversationId,
      role,
      content,
      metadata,
      createdAt: new Date(),
    });

    // Update conversation timestamp
    await db
      .update(conversations)
      .set({ updatedAt: new Date() })
      .where(eq(conversations.id, conversationId));
  }

  /**
   * Generate AI mentor response with context
   */
  async generateMentorResponse(
    userMessage: string,
    context: ConversationContext
  ): Promise<{ response: string; conversationId: string }> {
    let conversationId = context.conversationId;

    // Create new conversation if none exists
    if (!conversationId) {
      conversationId = await this.createConversation(context.userId);
    }

    // Get conversation history
    const history = await this.getConversationHistory(conversationId);

    // Build system prompt with context
    const systemPrompt = this.buildSystemPrompt(context);

    // Prepare messages for AI
    const messages: AIMessage[] = [
      { role: 'system', content: systemPrompt },
      ...history,
      { role: 'user', content: userMessage },
    ];

    // Generate response
    const aiResponse = await this.aiService.generateResponse(messages);
    const response = aiResponse.content;

    // Save both user message and AI response
    await this.saveMessage(conversationId, 'user', userMessage);
    await this.saveMessage(conversationId, 'assistant', response);

    return { response, conversationId };
  }

  /**
   * Build system prompt with user context
   */
  private buildSystemPrompt(context: ConversationContext): string {
    const { userProfile, currentGoals } = context;

    return `You are an AI social media growth mentor based on Brendan Kane's "The Guide to Going Viral" methodology. Your role is to provide personalized, actionable advice to help users grow their social media presence.

User Context:
${userProfile ? `- Current followers: ${userProfile.currentFollowers || 0}` : ''}
${userProfile ? `- Target followers: ${userProfile.targetFollowers || 'Not set'}` : ''}
${userProfile ? `- Platforms: ${Object.keys(userProfile.platforms || {}).join(', ') || 'Not specified'}` : ''}
${currentGoals ? `- Current goals: ${currentGoals.join(', ')}` : ''}

Key Principles to Follow:
1. Focus on the 4 viral content formats: Visual Metaphor, Two Characters One Lightbulb, Reactionary, and Transformation
2. Emphasize authentic engagement over vanity metrics
3. Provide specific, actionable steps rather than generic advice
4. Reference relevant examples and case studies when helpful
5. Adapt advice to the user's current follower count and platform focus
6. Encourage consistent content creation and community building

Always be encouraging, specific, and focused on practical implementation. Ask clarifying questions when needed to provide more targeted advice.`;
  }

  /**
   * Get user's recent conversations
   */
  async getUserConversations(userId: string, limit: number = 10) {
    return await db
      .select()
      .from(conversations)
      .where(eq(conversations.userId, userId))
      .orderBy(desc(conversations.updatedAt))
      .limit(limit);
  }

  /**
   * Update conversation title
   */
  async updateConversationTitle(conversationId: string, title: string): Promise<void> {
    await db
      .update(conversations)
      .set({ 
        title,
        updatedAt: new Date() 
      })
      .where(eq(conversations.id, conversationId));
  }

  /**
   * Delete conversation and all its messages
   */
  async deleteConversation(conversationId: string): Promise<void> {
    // Delete messages first (due to foreign key constraint)
    await db.delete(messages).where(eq(messages.conversationId, conversationId));
    
    // Delete conversation
    await db.delete(conversations).where(eq(conversations.id, conversationId));
  }

  /**
   * Get conversation by ID with basic info
   */
  async getConversation(conversationId: string) {
    const conversation = await db
      .select()
      .from(conversations)
      .where(eq(conversations.id, conversationId))
      .limit(1);

    return conversation[0] || null;
  }
}

export const conversationService = new ConversationService();
