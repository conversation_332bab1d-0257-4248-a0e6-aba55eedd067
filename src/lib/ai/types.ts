export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface AIFunction {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

export interface AIFunctionCall {
  name: string;
  arguments: Record<string, any>;
}

export interface AIResponse {
  content: string;
  functionCall?: AIFunctionCall;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model?: string;
  finishReason?: string;
}

export interface AIProvider {
  name: string;
  generateResponse(
    messages: AIMessage[],
    functions?: AIFunction[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      model?: string;
    }
  ): Promise<AIResponse>;
}

export interface AIServiceConfig {
  provider: 'openai' | 'anthropic' | 'gemini' | 'deepseek';
  apiKey: string;
  defaultModel?: string;
  defaultTemperature?: number;
  defaultMaxTokens?: number;
}
