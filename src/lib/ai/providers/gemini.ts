import { AIProvider, AIMessage, AIFunction, AIResponse, AIFunctionCall } from '../types';

export class GeminiProvider implements AIProvider {
  name = 'gemini';
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    messages: AIMessage[],
    functions?: AIFunction[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      model?: string;
    }
  ): Promise<AIResponse> {
    const model = options?.model || 'gemini-2.0-flash-lite';
    
    // Convert messages to Gemini format
    const contents = this.convertMessagesToGeminiFormat(messages);

    const requestBody: any = {
      contents,
      generationConfig: {
        temperature: options?.temperature || 0.7,
        maxOutputTokens: options?.maxTokens || 2000,
      },
    };

    // Add function declarations if provided
    if (functions && functions.length > 0) {
      requestBody.tools = [{
        functionDeclarations: functions.map(func => ({
          name: func.name,
          description: func.description,
          parameters: func.parameters,
        }))
      }];
    }

    const endpoint = `${this.baseUrl}/models/${model}:generateContent?key=${this.apiKey}`;

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Gemini API error: ${response.status} ${response.statusText} ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    
    if (!data.candidates || data.candidates.length === 0) {
      throw new Error('No candidates returned from Gemini API');
    }

    const candidate = data.candidates[0];
    
    // Extract content
    let content = '';
    let functionCall: AIFunctionCall | undefined;

    if (candidate.content && candidate.content.parts) {
      for (const part of candidate.content.parts) {
        if (part.text) {
          content += part.text;
        } else if (part.functionCall) {
          // Handle function calls
          functionCall = {
            name: part.functionCall.name,
            arguments: part.functionCall.args || {},
          };
        }
      }
    }

    // Extract usage information if available
    const usage = {
      promptTokens: data.usageMetadata?.promptTokenCount || 0,
      completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
      totalTokens: data.usageMetadata?.totalTokenCount || 0,
    };

    return {
      content,
      functionCall,
      usage,
      model,
      finishReason: candidate.finishReason,
    };
  }

  private convertMessagesToGeminiFormat(messages: AIMessage[]): any[] {
    const contents: any[] = [];
    let systemMessage = '';
    
    // Extract system messages
    const systemMessages = messages.filter(msg => msg.role === 'system');
    if (systemMessages.length > 0) {
      systemMessage = systemMessages.map(msg => msg.content).join('\n\n');
    }
    
    // Convert user/assistant messages
    const conversationMessages = messages.filter(msg => msg.role !== 'system');
    
    for (const message of conversationMessages) {
      const role = message.role === 'user' ? 'user' : 'model';
      
      // For the first user message, prepend system message if it exists
      let content = message.content;
      if (role === 'user' && systemMessage && contents.length === 0) {
        content = `${systemMessage}\n\n${content}`;
      }
      
      contents.push({
        role,
        parts: [{ text: content }]
      });
    }
    
    // If no user messages but we have a system message, create a user message
    if (contents.length === 0 && systemMessage) {
      contents.push({
        role: 'user',
        parts: [{ text: systemMessage }]
      });
    }
    
    return contents;
  }
}
