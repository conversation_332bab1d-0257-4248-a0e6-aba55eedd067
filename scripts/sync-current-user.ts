#!/usr/bin/env tsx

import { clerkClient } from '@clerk/nextjs/server';
import { db } from '../src/lib/db';
import { users } from '../src/lib/db/schema';
import { eq } from 'drizzle-orm';

async function syncCurrentUser() {
  try {
    console.log('🔄 Syncing current Clerk user to database...');
    
    // Get the current user from Clerk (you'll need to replace this with the actual user ID)
    // You can find this in the Clerk dashboard or by checking the auth() in your app
    const userId = 'user_2zNAe4GSqAD2PPQj0Bt1LZoWOTD'; // Replace with actual user ID
    
    console.log(`Fetching user: ${userId}`);
    const clerkUser = await clerkClient.users.getUser(userId);
    
    if (!clerkUser) {
      console.error('❌ User not found in Clerk');
      return;
    }
    
    console.log('✅ Found user in Clerk:', clerkUser.emailAddresses[0]?.emailAddress);
    
    // Check if user already exists in database
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);
    
    const primaryEmail = clerkUser.emailAddresses.find(
      email => email.id === clerkUser.primaryEmailAddressId
    );
    
    const userData = {
      id: clerkUser.id,
      email: primaryEmail?.emailAddress || '',
      firstName: clerkUser.firstName || '',
      lastName: clerkUser.lastName || '',
      imageUrl: clerkUser.imageUrl || '',
      createdAt: new Date(clerkUser.createdAt),
      updatedAt: new Date(),
    };
    
    if (existingUser.length > 0) {
      // Update existing user
      await db
        .update(users)
        .set(userData)
        .where(eq(users.id, userId));
      console.log('✅ Updated existing user in database');
    } else {
      // Create new user
      await db.insert(users).values(userData);
      console.log('✅ Created new user in database');
    }
    
    console.log('🎉 User sync complete!');
    
  } catch (error) {
    console.error('❌ Error syncing user:', error);
  }
}

syncCurrentUser().then(() => process.exit(0));
