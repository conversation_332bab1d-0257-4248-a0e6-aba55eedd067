{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_351a010a._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_466025e5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*){(\\\\.json)}?", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "yWXTVRnPejRJaZdiuSfKs/mOHwU6vBt0/hisfirdddo=", "__NEXT_PREVIEW_MODE_ID": "5d609f4b8c878d8c37e0c3f0c3c9e4ce", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1915b1111a1ffeb92f95dad86b1d0193ccd936c8d1ce38cb86a8996760af87f9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ac7ae09720c686c871a7962faa9441e1f2c7564f51b0ee2845609f1fd7d7b1d7"}}}, "instrumentation": null, "functions": {}}