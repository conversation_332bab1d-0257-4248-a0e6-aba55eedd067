@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for Influtify */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-base-100 text-base-content;
  }
}

@layer components {
  .gradient-text {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply bg-base-100/80 backdrop-blur-md border border-base-300/50;
  }

  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-xl;
  }
}
.chat-container {
  height: calc(100vh - 4rem);
}

.artifacts-panel {
  height: calc(100vh - 4rem);
  overflow-y: auto;
}

.message-bubble {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-indicator {
  animation: pulse 1.5s ease-in-out infinite;
}

.achievement-celebration {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
