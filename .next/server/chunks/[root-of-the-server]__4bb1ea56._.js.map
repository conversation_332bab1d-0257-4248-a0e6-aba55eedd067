{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/lib/services/ai-provider.ts"], "sourcesContent": ["// AI Provider types and configurations for Influtify\nexport type AIProvider = 'deepseek' | 'anthropic' | 'openai' | 'gemini';\n\n// Influtify-specific message types\nexport interface InflutifyMessage {\n  role: 'user' | 'assistant' | 'system';\n  content: string;\n  timestamp?: Date;\n}\n\n// Influtify conversation context\nexport interface InflutifyContext {\n  userId: string;\n  userProfile?: any;\n  conversationHistory?: InflutifyMessage[];\n  currentGoals?: string[];\n  platform?: string;\n  followerCount?: number;\n}\n\n// Provider Configuration Types\nexport interface ProviderConfig {\n  endpoint: string;\n  model: string;\n  headers: (apiKey: string) => Record<string, string>;\n  formatRequest: (prompt: string, messages: any[], systemMessage?: string) => any;\n  streamProcessor: (response: Response, controller: ReadableStreamDefaultController) => Promise<void>;\n}\n\n// Helper function to remove Markdown code block delimiters from streams\nfunction sanitizeStreamText(text: string): string {\n  // Remove opening Markdown delimiters at the beginning of text\n  if (text.startsWith('```') || text.startsWith('```html')) {\n    return text.replace(/^```(?:html|markdown|text)?\\n?/, '');\n  }\n  \n  // Remove trailing Markdown delimiters at the end of text\n  if (text.endsWith('```')) {\n    return text.replace(/```$/, '');\n  }\n  \n  return text;\n}\n\n// OpenAI Configuration\nexport const openaiConfig: ProviderConfig = {\n  endpoint: 'https://api.openai.com/v1/chat/completions',\n  model: 'gpt-4o-mini',\n  headers: (apiKey) => ({\n    'Content-Type': 'application/json',\n    'Authorization': `Bearer ${apiKey}`,\n  }),\n  formatRequest: (prompt, messages = [], systemMessage = '') => ({\n    model: 'gpt-4o-mini',\n    messages: [\n      {\n        role: \"system\",\n        content: systemMessage\n      },\n      {\n        role: \"user\",\n        content: prompt,\n      },\n      ...messages\n    ],\n    stream: true,\n    temperature: 0.1,\n  }),\n  streamProcessor: async (response, controller) => {\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new Error(\"Response body reader not available\");\n    }\n\n    let buffer = \"\";\n    const encoder = new TextEncoder();\n    \n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) break;\n\n      const chunk = new TextDecoder().decode(value);\n      buffer += chunk;\n      \n      let lines = buffer.split(\"\\n\");\n      buffer = lines.pop() || \"\";\n\n      for (const line of lines) {\n        if (line.startsWith(\"data: \")) {\n          const data = line.slice(6);\n          if (data === \"[DONE]\") continue;\n          try {\n            const json = JSON.parse(data);\n            const content = json.choices[0]?.delta?.content || \"\";\n            if (content) {\n              const sanitizedContent = sanitizeStreamText(content);\n              controller.enqueue(encoder.encode(sanitizedContent));\n            }\n          } catch (e) {\n            console.error(\"Error parsing OpenAI stream:\", e);\n          }\n        }\n      }\n    }\n    \n    if (buffer && buffer.startsWith(\"data: \") && buffer !== \"data: [DONE]\") {\n      try {\n        const data = buffer.slice(6);\n        const json = JSON.parse(data);\n        const content = json.choices[0]?.delta?.content || \"\";\n        if (content) {\n          const sanitizedContent = sanitizeStreamText(content);\n          controller.enqueue(encoder.encode(sanitizedContent));\n        }\n      } catch (e) {\n        console.error(\"Error parsing final buffer in OpenAI stream:\", e);\n      }\n    }\n  }\n};\n\n// Anthropic Configuration\nexport const anthropicConfig: ProviderConfig = {\n  endpoint: 'https://api.anthropic.com/v1/messages',\n  model: 'claude-3-haiku-20240307',\n  headers: (apiKey) => ({\n    'Content-Type': 'application/json',\n    'x-api-key': apiKey,\n    'anthropic-version': '2023-06-01',\n  }),\n  formatRequest: (prompt, messages = [], systemMessage = '') => ({\n    model: 'claude-3-haiku-20240307',\n    system: systemMessage,\n    messages: [\n      {\n        role: \"user\",\n        content: prompt\n      }\n    ],\n    temperature: 0.1,\n    stream: true\n  }),\n  streamProcessor: async (response, controller) => {\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new Error(\"Response body reader not available\");\n    }\n\n    let buffer = \"\";\n    const encoder = new TextEncoder();\n    \n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) break;\n\n      const chunk = new TextDecoder().decode(value);\n      buffer += chunk;\n      \n      let lines = buffer.split(\"\\n\");\n      buffer = lines.pop() || \"\";\n\n      for (const line of lines) {\n        if (line.startsWith(\"event: content_block_delta\")) {\n          const dataIndex = lines.indexOf(line) + 1;\n          if (dataIndex < lines.length && lines[dataIndex].startsWith(\"data: \")) {\n            try {\n              const data = JSON.parse(lines[dataIndex].slice(6));\n              if (data.delta && data.delta.text) {\n                const sanitizedText = sanitizeStreamText(data.delta.text);\n                controller.enqueue(encoder.encode(sanitizedText));\n              }\n            } catch (e) {\n              console.error(\"Error parsing Anthropic stream:\", e);\n            }\n          }\n        } else if (line.startsWith(\"data: \")) {\n          try {\n            const data = JSON.parse(line.slice(6));\n            if (data.type === \"content_block_delta\" && data.delta && data.delta.text) {\n              const sanitizedText = sanitizeStreamText(data.delta.text);\n              controller.enqueue(encoder.encode(sanitizedText));\n            }\n          } catch (e) {\n            // Ignore parsing errors for lines that aren't content blocks\n          }\n        }\n      }\n    }\n  }\n};\n\n// DeepSeek Configuration\nexport const deepseekConfig: ProviderConfig = {\n  endpoint: 'https://api.deepseek.com/v1/chat/completions',\n  model: 'deepseek-chat',\n  headers: (apiKey) => ({\n    'Content-Type': 'application/json',\n    'Authorization': `Bearer ${apiKey}`,\n  }),\n  formatRequest: (prompt, messages = [], systemMessage = '') => ({\n    model: 'deepseek-chat',\n    messages: [\n      {\n        role: \"system\",\n        content: systemMessage\n      },\n      {\n        role: \"user\",\n        content: prompt,\n      },\n      ...messages\n    ],\n    temperature: 0.1,\n    stream: true,\n  }),\n  streamProcessor: async (response, controller) => {\n    // DeepSeek uses the same response format as OpenAI\n    await openaiConfig.streamProcessor(response, controller);\n  }\n};\n\n// Gemini Configuration\nexport const geminiConfig: ProviderConfig = {\n  endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:streamGenerateContent',\n  model: 'gemini-2.0-flash-lite',\n  headers: (apiKey) => ({\n    'Content-Type': 'application/json',\n  }),\n  formatRequest: (prompt, messages = [], systemMessage = '') => ({\n    contents: [\n      {\n        parts: [\n          { text: systemMessage },\n          { text: prompt }\n        ]\n      }\n    ],\n    generationConfig: {\n      temperature: 0.1,\n    }\n  }),\n  streamProcessor: async (response, controller) => {\n    const reader = response.body?.getReader();\n    if (!reader) {\n      throw new Error(\"Response body reader not available for Gemini stream\");\n    }\n\n    let buffer = \"\";\n    const decoder = new TextDecoder();\n    const encoder = new TextEncoder();\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) {\n          console.log(\"[Gemini Stream] Stream finished.\");\n          break;\n        }\n\n        buffer += decoder.decode(value, { stream: true });\n\n        let parseStartIndex = 0;\n        while (parseStartIndex < buffer.length) {\n          const objStartIndex = buffer.indexOf('{', parseStartIndex);\n          if (objStartIndex === -1) {\n            break;\n          }\n\n          let braceCount = 1;\n          let objEndIndex = objStartIndex + 1;\n          while (objEndIndex < buffer.length && braceCount > 0) {\n            if (buffer[objEndIndex] === '{') {\n              braceCount++;\n            } else if (buffer[objEndIndex] === '}') {\n              braceCount--;\n            }\n            objEndIndex++;\n          }\n\n          if (braceCount === 0) {\n            const jsonString = buffer.substring(objStartIndex, objEndIndex);\n\n            try {\n              const data = JSON.parse(jsonString);\n\n              if (data.candidates && data.candidates[0]?.content?.parts) {\n                for (const part of data.candidates[0].content.parts) {\n                  if (part.text) {\n                    const sanitizedText = sanitizeStreamText(part.text);\n                    controller.enqueue(encoder.encode(sanitizedText));\n                  }\n                }\n              }\n\n              buffer = buffer.substring(objEndIndex);\n              parseStartIndex = 0;\n            } catch (e) {\n              parseStartIndex = objStartIndex + 1;\n            }\n          } else {\n            break;\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error reading Gemini stream:\", error);\n      controller.error(error);\n    } finally {\n      const finalChunk = decoder.decode();\n      reader.releaseLock();\n      console.log(\"[Gemini Stream] Processor finished.\");\n    }\n  }\n};\n\n// Provider Configuration Map\nexport const PROVIDER_CONFIG = {\n  deepseek: deepseekConfig,\n  anthropic: anthropicConfig,\n  openai: openaiConfig,\n  gemini: geminiConfig\n};\n\n// Get current provider from environment\nexport function getCurrentProvider(): AIProvider {\n  const provider = process.env.AI_PROVIDER as AIProvider;\n  if (!provider || !PROVIDER_CONFIG[provider]) {\n    // Default to gemini if no provider is specified or invalid\n    return 'gemini';\n  }\n  return provider;\n}\n\n// Get API key for current provider\nexport function getApiKey(provider: AIProvider): string {\n  const keys = {\n    deepseek: process.env.DEEPSEEK_API_KEY,\n    anthropic: process.env.ANTHROPIC_API_KEY,\n    openai: process.env.OPENAI_API_KEY,\n    gemini: process.env.GEMINI_API_KEY\n  };\n\n  const apiKey = keys[provider];\n  if (!apiKey) {\n    throw new Error(`API key not configured for provider: ${provider}`);\n  }\n\n  return apiKey;\n}\n\n// Helper function for non-streaming requests\nexport async function callAIForCompletion(prompt: string, systemMessage = '', temperature = 0.1): Promise<string> {\n  const provider = getCurrentProvider();\n  const config = PROVIDER_CONFIG[provider];\n  const apiKey = getApiKey(provider);\n\n  console.log(`Using AI provider for completion: ${provider}`);\n\n  // Create endpoint URL (special handling for Gemini)\n  let endpoint = config.endpoint;\n  let requestBody;\n\n  if (provider === 'gemini') {\n    // Use a different model for non-streaming Gemini calls\n    const baseUrl = endpoint.split('/models/')[0] + '/models/';\n    const nonStreamingModel = 'gemini-2.0-flash-lite';\n    endpoint = `${baseUrl}${nonStreamingModel}:generateContent?key=${apiKey}`;\n\n    console.log(`[callAIForCompletion] Using ${nonStreamingModel} for non-streaming Gemini call`);\n\n    requestBody = {\n      ...config.formatRequest(prompt, [], systemMessage),\n      model: nonStreamingModel\n    };\n  } else {\n    endpoint = endpoint.replace('stream', '');\n    requestBody = config.formatRequest(prompt, [], systemMessage);\n  }\n\n  // For non-streaming requests, set stream to false\n  if (requestBody.stream !== undefined) {\n    requestBody.stream = false;\n  }\n\n  if (requestBody.temperature !== undefined) {\n    requestBody.temperature = temperature;\n  }\n\n  try {\n    const response = await fetch(endpoint, {\n      method: \"POST\",\n      headers: config.headers(apiKey),\n      body: JSON.stringify(requestBody),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(`${provider} API error: ${response.status} ${response.statusText} ${JSON.stringify(errorData)}`);\n    }\n\n    const data = await response.json();\n\n    // Extract content based on provider\n    let content = '';\n    if (provider === 'gemini') {\n      content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';\n    } else if (provider === 'anthropic') {\n      content = data.content?.[0]?.text || '';\n    } else {\n      // OpenAI and DeepSeek format\n      content = data.choices?.[0]?.message?.content || '';\n    }\n\n    // Clean up any markdown code blocks\n    content = content.replace(/```json\\n?/g, '').replace(/```\\n?/g, '').trim();\n\n    return content;\n  } catch (error) {\n    console.error(`Error calling ${provider} API for completion:`, error);\n    throw error;\n  }\n}\n\n/**\n * Function to call AI and parse the response as JSON\n * This is a wrapper around callAIForCompletion that ensures the response is valid JSON\n */\nexport async function callAIForJSON<T = any>(prompt: string, systemMessage = '', temperature = 0.1): Promise<T> {\n  const result = await callAIForCompletion(prompt, systemMessage, temperature);\n\n  // Clean up any markdown code fences from the response\n  let cleanResult = result;\n  if (cleanResult.includes('```')) {\n    cleanResult = cleanResult.replace(/```json\\n?|```\\n?|```$/gm, '');\n  }\n\n  try {\n    return JSON.parse(cleanResult) as T;\n  } catch (error) {\n    console.error('Failed to parse AI response as JSON:', error);\n    throw new Error(`Failed to parse AI response as JSON: ${error}`);\n  }\n}\n\n/**\n * Influtify-specific AI mentor response generator\n * Generates streaming responses with social media growth context\n */\nexport async function generateInflutifyMentorResponse(\n  userMessage: string,\n  context: InflutifyContext\n): Promise<ReadableStream> {\n  const provider = getCurrentProvider();\n  const config = PROVIDER_CONFIG[provider];\n  const apiKey = getApiKey(provider);\n\n  console.log(`[Influtify] Using AI provider: ${provider}`);\n\n  // Build Influtify-specific system prompt\n  const systemPrompt = buildInflutifySystemPrompt(context);\n\n  // Format conversation history for context\n  const conversationContext = formatConversationHistory(context.conversationHistory || []);\n\n  // Create the final prompt with context\n  const fullPrompt = conversationContext\n    ? `${conversationContext}\\n\\nUser: ${userMessage}`\n    : userMessage;\n\n  // Create the endpoint URL (special handling for Gemini)\n  let endpoint = config.endpoint;\n  if (provider === 'gemini') {\n    endpoint = `${endpoint}?key=${apiKey}`;\n  }\n\n  // Create request body based on provider\n  const requestBody = config.formatRequest(fullPrompt, [], systemPrompt);\n\n  // Create stream\n  return new ReadableStream({\n    async start(controller) {\n      try {\n        console.log(`[Influtify] Calling ${provider} API for mentor response`);\n\n        const res = await fetch(endpoint, {\n          method: \"POST\",\n          headers: config.headers(apiKey),\n          body: JSON.stringify(requestBody),\n        });\n\n        if (!res.ok) {\n          const errorData = await res.json().catch(() => ({}));\n          const errorMessage = `${provider} API error: ${res.status} ${res.statusText} ${JSON.stringify(errorData)}`;\n          console.error(errorMessage);\n          controller.error(new Error(errorMessage));\n          return;\n        }\n\n        // Process the stream using provider-specific logic\n        await config.streamProcessor(res, controller);\n\n      } catch (e) {\n        console.error(`[Influtify] Error in ${provider} stream:`, e);\n        controller.error(e);\n      } finally {\n        controller.close();\n      }\n    },\n  });\n}\n\n/**\n * Generate non-streaming AI response for Influtify use cases\n */\nexport async function generateInflutifyCompletion(\n  prompt: string,\n  context: InflutifyContext,\n  temperature = 0.1\n): Promise<string> {\n  const systemPrompt = buildInflutifySystemPrompt(context);\n  return callAIForCompletion(prompt, systemPrompt, temperature);\n}\n\n/**\n * Generate structured JSON response for Influtify features\n */\nexport async function generateInflutifyJSON<T = any>(\n  prompt: string,\n  context: InflutifyContext,\n  temperature = 0.1\n): Promise<T> {\n  const systemPrompt = buildInflutifySystemPrompt(context);\n  return callAIForJSON<T>(prompt, systemPrompt, temperature);\n}\n\n/**\n * Build Influtify-specific system prompt based on user context\n */\nfunction buildInflutifySystemPrompt(context: InflutifyContext): string {\n  const { userProfile, currentGoals, platform, followerCount } = context;\n\n  return `You are an AI social media growth mentor for Influtify, based on Brendan Kane's \"The Guide to Going Viral\" methodology. Your expertise comes from proven strategies that have helped creators achieve viral success.\n\nCORE MISSION:\nHelp users grow their social media presence through strategic, data-driven content creation and audience engagement.\n\nBRENDAN KANE'S VIRAL METHODOLOGY:\n1. The 4 Viral Content Formats:\n   - Educational: Teach something valuable\n   - Entertaining: Make people laugh or feel good\n   - Inspirational: Motivate and uplift\n   - Conversational: Start discussions and debates\n\n2. The Hook-Retain-Reward Framework:\n   - Hook: Grab attention in first 3 seconds\n   - Retain: Keep viewers engaged throughout\n   - Reward: Provide value that makes sharing worthwhile\n\n3. Platform-Specific Optimization:\n   - Understand each platform's algorithm\n   - Adapt content format to platform strengths\n   - Time posting for maximum engagement\n\nUSER CONTEXT:\n${userProfile ? `\n- Name: ${userProfile.firstName || 'User'}\n- Platform Focus: ${platform || 'Not specified'}\n- Current Followers: ${followerCount || 'Not specified'}\n- Goals: ${currentGoals?.join(', ') || 'Growing social media presence'}\n` : '- New user starting their growth journey'}\n\nRESPONSE GUIDELINES:\n- Be encouraging and supportive while providing actionable advice\n- Ask strategic questions to understand their specific challenges\n- Provide concrete examples and step-by-step guidance\n- Reference specific viral strategies when relevant\n- Focus on measurable outcomes and testing approaches\n- Adapt advice to their current follower count and platform\n- Use emojis strategically to maintain engagement\n- Always end with a clear next action step\n\nCONVERSATION STYLE:\n- Conversational and friendly, like a knowledgeable mentor\n- Confident in your expertise but humble in approach\n- Focus on practical implementation over theory\n- Celebrate small wins and progress\n- Challenge users to think bigger while being realistic\n\nRemember: Your goal is to transform users from content creators into viral content strategists using proven methodologies.`;\n}\n\n/**\n * Format conversation history for AI context\n */\nfunction formatConversationHistory(messages: InflutifyMessage[]): string {\n  if (!messages.length) return '';\n\n  // Take last 10 messages to avoid token limits\n  const recentMessages = messages.slice(-10);\n\n  return recentMessages\n    .map(msg => {\n      const role = msg.role === 'user' ? 'User' : 'Mentor';\n      return `${role}: ${msg.content}`;\n    })\n    .join('\\n');\n}\n\n/**\n * Extract user intent and goals from message\n */\nexport function analyzeUserIntent(message: string): {\n  intent: 'question' | 'goal_setting' | 'content_review' | 'strategy_request' | 'general';\n  platform?: string;\n  contentType?: string;\n  urgency: 'low' | 'medium' | 'high';\n} {\n  const lowerMessage = message.toLowerCase();\n\n  // Detect platform mentions\n  const platforms = ['instagram', 'tiktok', 'youtube', 'twitter', 'linkedin', 'facebook'];\n  const mentionedPlatform = platforms.find(platform => lowerMessage.includes(platform));\n\n  // Detect content types\n  const contentTypes = ['video', 'reel', 'post', 'story', 'short', 'carousel'];\n  const mentionedContentType = contentTypes.find(type => lowerMessage.includes(type));\n\n  // Determine intent\n  let intent: 'question' | 'goal_setting' | 'content_review' | 'strategy_request' | 'general' = 'general';\n\n  if (lowerMessage.includes('goal') || lowerMessage.includes('want to') || lowerMessage.includes('trying to')) {\n    intent = 'goal_setting';\n  } else if (lowerMessage.includes('?') || lowerMessage.includes('how') || lowerMessage.includes('what') || lowerMessage.includes('why')) {\n    intent = 'question';\n  } else if (lowerMessage.includes('review') || lowerMessage.includes('feedback') || lowerMessage.includes('thoughts on')) {\n    intent = 'content_review';\n  } else if (lowerMessage.includes('strategy') || lowerMessage.includes('plan') || lowerMessage.includes('approach')) {\n    intent = 'strategy_request';\n  }\n\n  // Determine urgency\n  let urgency: 'low' | 'medium' | 'high' = 'medium';\n  if (lowerMessage.includes('urgent') || lowerMessage.includes('asap') || lowerMessage.includes('quickly')) {\n    urgency = 'high';\n  } else if (lowerMessage.includes('when i have time') || lowerMessage.includes('eventually')) {\n    urgency = 'low';\n  }\n\n  return {\n    intent,\n    platform: mentionedPlatform,\n    contentType: mentionedContentType,\n    urgency\n  };\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;;AA6BrD,wEAAwE;AACxE,SAAS,mBAAmB,IAAY;IACtC,8DAA8D;IAC9D,IAAI,KAAK,UAAU,CAAC,UAAU,KAAK,UAAU,CAAC,YAAY;QACxD,OAAO,KAAK,OAAO,CAAC,kCAAkC;IACxD;IAEA,yDAAyD;IACzD,IAAI,KAAK,QAAQ,CAAC,QAAQ;QACxB,OAAO,KAAK,OAAO,CAAC,QAAQ;IAC9B;IAEA,OAAO;AACT;AAGO,MAAM,eAA+B;IAC1C,UAAU;IACV,OAAO;IACP,SAAS,CAAC,SAAW,CAAC;YACpB,gBAAgB;YAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ;QACrC,CAAC;IACD,eAAe,CAAC,QAAQ,WAAW,EAAE,EAAE,gBAAgB,EAAE,GAAK,CAAC;YAC7D,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;mBACG;aACJ;YACD,QAAQ;YACR,aAAa;QACf,CAAC;IACD,iBAAiB,OAAO,UAAU;QAChC,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,SAAS;QACb,MAAM,UAAU,IAAI;QAEpB,MAAO,KAAM;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;YACzC,IAAI,MAAM;YAEV,MAAM,QAAQ,IAAI,cAAc,MAAM,CAAC;YACvC,UAAU;YAEV,IAAI,QAAQ,OAAO,KAAK,CAAC;YACzB,SAAS,MAAM,GAAG,MAAM;YAExB,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;oBAC7B,MAAM,OAAO,KAAK,KAAK,CAAC;oBACxB,IAAI,SAAS,UAAU;oBACvB,IAAI;wBACF,MAAM,OAAO,KAAK,KAAK,CAAC;wBACxB,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,OAAO,WAAW;wBACnD,IAAI,SAAS;4BACX,MAAM,mBAAmB,mBAAmB;4BAC5C,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;wBACpC;oBACF,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,gCAAgC;oBAChD;gBACF;YACF;QACF;QAEA,IAAI,UAAU,OAAO,UAAU,CAAC,aAAa,WAAW,gBAAgB;YACtE,IAAI;gBACF,MAAM,OAAO,OAAO,KAAK,CAAC;gBAC1B,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,MAAM,UAAU,KAAK,OAAO,CAAC,EAAE,EAAE,OAAO,WAAW;gBACnD,IAAI,SAAS;oBACX,MAAM,mBAAmB,mBAAmB;oBAC5C,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;gBACpC;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,gDAAgD;YAChE;QACF;IACF;AACF;AAGO,MAAM,kBAAkC;IAC7C,UAAU;IACV,OAAO;IACP,SAAS,CAAC,SAAW,CAAC;YACpB,gBAAgB;YAChB,aAAa;YACb,qBAAqB;QACvB,CAAC;IACD,eAAe,CAAC,QAAQ,WAAW,EAAE,EAAE,gBAAgB,EAAE,GAAK,CAAC;YAC7D,OAAO;YACP,QAAQ;YACR,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,QAAQ;QACV,CAAC;IACD,iBAAiB,OAAO,UAAU;QAChC,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,SAAS;QACb,MAAM,UAAU,IAAI;QAEpB,MAAO,KAAM;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;YACzC,IAAI,MAAM;YAEV,MAAM,QAAQ,IAAI,cAAc,MAAM,CAAC;YACvC,UAAU;YAEV,IAAI,QAAQ,OAAO,KAAK,CAAC;YACzB,SAAS,MAAM,GAAG,MAAM;YAExB,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,UAAU,CAAC,+BAA+B;oBACjD,MAAM,YAAY,MAAM,OAAO,CAAC,QAAQ;oBACxC,IAAI,YAAY,MAAM,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,WAAW;wBACrE,IAAI;4BACF,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC;4BAC/C,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;gCACjC,MAAM,gBAAgB,mBAAmB,KAAK,KAAK,CAAC,IAAI;gCACxD,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;4BACpC;wBACF,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,mCAAmC;wBACnD;oBACF;gBACF,OAAO,IAAI,KAAK,UAAU,CAAC,WAAW;oBACpC,IAAI;wBACF,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;wBACnC,IAAI,KAAK,IAAI,KAAK,yBAAyB,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI,EAAE;4BACxE,MAAM,gBAAgB,mBAAmB,KAAK,KAAK,CAAC,IAAI;4BACxD,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;wBACpC;oBACF,EAAE,OAAO,GAAG;oBACV,6DAA6D;oBAC/D;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,iBAAiC;IAC5C,UAAU;IACV,OAAO;IACP,SAAS,CAAC,SAAW,CAAC;YACpB,gBAAgB;YAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ;QACrC,CAAC;IACD,eAAe,CAAC,QAAQ,WAAW,EAAE,EAAE,gBAAgB,EAAE,GAAK,CAAC;YAC7D,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;mBACG;aACJ;YACD,aAAa;YACb,QAAQ;QACV,CAAC;IACD,iBAAiB,OAAO,UAAU;QAChC,mDAAmD;QACnD,MAAM,aAAa,eAAe,CAAC,UAAU;IAC/C;AACF;AAGO,MAAM,eAA+B;IAC1C,UAAU;IACV,OAAO;IACP,SAAS,CAAC,SAAW,CAAC;YACpB,gBAAgB;QAClB,CAAC;IACD,eAAe,CAAC,QAAQ,WAAW,EAAE,EAAE,gBAAgB,EAAE,GAAK,CAAC;YAC7D,UAAU;gBACR;oBACE,OAAO;wBACL;4BAAE,MAAM;wBAAc;wBACtB;4BAAE,MAAM;wBAAO;qBAChB;gBACH;aACD;YACD,kBAAkB;gBAChB,aAAa;YACf;QACF,CAAC;IACD,iBAAiB,OAAO,UAAU;QAChC,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,SAAS;QACb,MAAM,UAAU,IAAI;QACpB,MAAM,UAAU,IAAI;QAEpB,IAAI;YACF,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;oBACR,QAAQ,GAAG,CAAC;oBACZ;gBACF;gBAEA,UAAU,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBAE/C,IAAI,kBAAkB;gBACtB,MAAO,kBAAkB,OAAO,MAAM,CAAE;oBACtC,MAAM,gBAAgB,OAAO,OAAO,CAAC,KAAK;oBAC1C,IAAI,kBAAkB,CAAC,GAAG;wBACxB;oBACF;oBAEA,IAAI,aAAa;oBACjB,IAAI,cAAc,gBAAgB;oBAClC,MAAO,cAAc,OAAO,MAAM,IAAI,aAAa,EAAG;wBACpD,IAAI,MAAM,CAAC,YAAY,KAAK,KAAK;4BAC/B;wBACF,OAAO,IAAI,MAAM,CAAC,YAAY,KAAK,KAAK;4BACtC;wBACF;wBACA;oBACF;oBAEA,IAAI,eAAe,GAAG;wBACpB,MAAM,aAAa,OAAO,SAAS,CAAC,eAAe;wBAEnD,IAAI;4BACF,MAAM,OAAO,KAAK,KAAK,CAAC;4BAExB,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,EAAE,EAAE,SAAS,OAAO;gCACzD,KAAK,MAAM,QAAQ,KAAK,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAE;oCACnD,IAAI,KAAK,IAAI,EAAE;wCACb,MAAM,gBAAgB,mBAAmB,KAAK,IAAI;wCAClD,WAAW,OAAO,CAAC,QAAQ,MAAM,CAAC;oCACpC;gCACF;4BACF;4BAEA,SAAS,OAAO,SAAS,CAAC;4BAC1B,kBAAkB;wBACpB,EAAE,OAAO,GAAG;4BACV,kBAAkB,gBAAgB;wBACpC;oBACF,OAAO;wBACL;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,WAAW,KAAK,CAAC;QACnB,SAAU;YACR,MAAM,aAAa,QAAQ,MAAM;YACjC,OAAO,WAAW;YAClB,QAAQ,GAAG,CAAC;QACd;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,UAAU;IACV,WAAW;IACX,QAAQ;IACR,QAAQ;AACV;AAGO,SAAS;IACd,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;IACxC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,SAAS,EAAE;QAC3C,2DAA2D;QAC3D,OAAO;IACT;IACA,OAAO;AACT;AAGO,SAAS,UAAU,QAAoB;IAC5C,MAAM,OAAO;QACX,UAAU,QAAQ,GAAG,CAAC,gBAAgB;QACtC,WAAW,QAAQ,GAAG,CAAC,iBAAiB;QACxC,QAAQ,QAAQ,GAAG,CAAC,cAAc;QAClC,QAAQ,QAAQ,GAAG,CAAC,cAAc;IACpC;IAEA,MAAM,SAAS,IAAI,CAAC,SAAS;IAC7B,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,UAAU;IACpE;IAEA,OAAO;AACT;AAGO,eAAe,oBAAoB,MAAc,EAAE,gBAAgB,EAAE,EAAE,cAAc,GAAG;IAC7F,MAAM,WAAW;IACjB,MAAM,SAAS,eAAe,CAAC,SAAS;IACxC,MAAM,SAAS,UAAU;IAEzB,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,UAAU;IAE3D,oDAAoD;IACpD,IAAI,WAAW,OAAO,QAAQ;IAC9B,IAAI;IAEJ,IAAI,aAAa,UAAU;QACzB,uDAAuD;QACvD,MAAM,UAAU,SAAS,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG;QAChD,MAAM,oBAAoB;QAC1B,WAAW,GAAG,UAAU,kBAAkB,qBAAqB,EAAE,QAAQ;QAEzE,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,kBAAkB,8BAA8B,CAAC;QAE5F,cAAc;YACZ,GAAG,OAAO,aAAa,CAAC,QAAQ,EAAE,EAAE,cAAc;YAClD,OAAO;QACT;IACF,OAAO;QACL,WAAW,SAAS,OAAO,CAAC,UAAU;QACtC,cAAc,OAAO,aAAa,CAAC,QAAQ,EAAE,EAAE;IACjD;IAEA,kDAAkD;IAClD,IAAI,YAAY,MAAM,KAAK,WAAW;QACpC,YAAY,MAAM,GAAG;IACvB;IAEA,IAAI,YAAY,WAAW,KAAK,WAAW;QACzC,YAAY,WAAW,GAAG;IAC5B;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS,OAAO,OAAO,CAAC;YACxB,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,MAAM,GAAG,SAAS,YAAY,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,YAAY;QACjH;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,oCAAoC;QACpC,IAAI,UAAU;QACd,IAAI,aAAa,UAAU;YACzB,UAAU,KAAK,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,EAAE,EAAE,QAAQ;QAC/D,OAAO,IAAI,aAAa,aAAa;YACnC,UAAU,KAAK,OAAO,EAAE,CAAC,EAAE,EAAE,QAAQ;QACvC,OAAO;YACL,6BAA6B;YAC7B,UAAU,KAAK,OAAO,EAAE,CAAC,EAAE,EAAE,SAAS,WAAW;QACnD;QAEA,oCAAoC;QACpC,UAAU,QAAQ,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI;QAExE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,oBAAoB,CAAC,EAAE;QAC/D,MAAM;IACR;AACF;AAMO,eAAe,cAAuB,MAAc,EAAE,gBAAgB,EAAE,EAAE,cAAc,GAAG;IAChG,MAAM,SAAS,MAAM,oBAAoB,QAAQ,eAAe;IAEhE,sDAAsD;IACtD,IAAI,cAAc;IAClB,IAAI,YAAY,QAAQ,CAAC,QAAQ;QAC/B,cAAc,YAAY,OAAO,CAAC,4BAA4B;IAChE;IAEA,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,OAAO;IACjE;AACF;AAMO,eAAe,gCACpB,WAAmB,EACnB,OAAyB;IAEzB,MAAM,WAAW;IACjB,MAAM,SAAS,eAAe,CAAC,SAAS;IACxC,MAAM,SAAS,UAAU;IAEzB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,UAAU;IAExD,yCAAyC;IACzC,MAAM,eAAe,2BAA2B;IAEhD,0CAA0C;IAC1C,MAAM,sBAAsB,0BAA0B,QAAQ,mBAAmB,IAAI,EAAE;IAEvF,uCAAuC;IACvC,MAAM,aAAa,sBACf,GAAG,oBAAoB,UAAU,EAAE,aAAa,GAChD;IAEJ,wDAAwD;IACxD,IAAI,WAAW,OAAO,QAAQ;IAC9B,IAAI,aAAa,UAAU;QACzB,WAAW,GAAG,SAAS,KAAK,EAAE,QAAQ;IACxC;IAEA,wCAAwC;IACxC,MAAM,cAAc,OAAO,aAAa,CAAC,YAAY,EAAE,EAAE;IAEzD,gBAAgB;IAChB,OAAO,IAAI,eAAe;QACxB,MAAM,OAAM,UAAU;YACpB,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,wBAAwB,CAAC;gBAErE,MAAM,MAAM,MAAM,MAAM,UAAU;oBAChC,QAAQ;oBACR,SAAS,OAAO,OAAO,CAAC;oBACxB,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,IAAI,EAAE,EAAE;oBACX,MAAM,YAAY,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;oBAClD,MAAM,eAAe,GAAG,SAAS,YAAY,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,YAAY;oBAC1G,QAAQ,KAAK,CAAC;oBACd,WAAW,KAAK,CAAC,IAAI,MAAM;oBAC3B;gBACF;gBAEA,mDAAmD;gBACnD,MAAM,OAAO,eAAe,CAAC,KAAK;YAEpC,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,SAAS,QAAQ,CAAC,EAAE;gBAC1D,WAAW,KAAK,CAAC;YACnB,SAAU;gBACR,WAAW,KAAK;YAClB;QACF;IACF;AACF;AAKO,eAAe,4BACpB,MAAc,EACd,OAAyB,EACzB,cAAc,GAAG;IAEjB,MAAM,eAAe,2BAA2B;IAChD,OAAO,oBAAoB,QAAQ,cAAc;AACnD;AAKO,eAAe,sBACpB,MAAc,EACd,OAAyB,EACzB,cAAc,GAAG;IAEjB,MAAM,eAAe,2BAA2B;IAChD,OAAO,cAAiB,QAAQ,cAAc;AAChD;AAEA;;CAEC,GACD,SAAS,2BAA2B,OAAyB;IAC3D,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG;IAE/D,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuBV,EAAE,cAAc,CAAC;QACT,EAAE,YAAY,SAAS,IAAI,OAAO;kBACxB,EAAE,YAAY,gBAAgB;qBAC3B,EAAE,iBAAiB,gBAAgB;SAC/C,EAAE,cAAc,KAAK,SAAS,gCAAgC;AACvE,CAAC,GAAG,2CAA2C;;;;;;;;;;;;;;;;;;;0HAmB2E,CAAC;AAC3H;AAEA;;CAEC,GACD,SAAS,0BAA0B,QAA4B;IAC7D,IAAI,CAAC,SAAS,MAAM,EAAE,OAAO;IAE7B,8CAA8C;IAC9C,MAAM,iBAAiB,SAAS,KAAK,CAAC,CAAC;IAEvC,OAAO,eACJ,GAAG,CAAC,CAAA;QACH,MAAM,OAAO,IAAI,IAAI,KAAK,SAAS,SAAS;QAC5C,OAAO,GAAG,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;IAClC,GACC,IAAI,CAAC;AACV;AAKO,SAAS,kBAAkB,OAAe;IAM/C,MAAM,eAAe,QAAQ,WAAW;IAExC,2BAA2B;IAC3B,MAAM,YAAY;QAAC;QAAa;QAAU;QAAW;QAAW;QAAY;KAAW;IACvF,MAAM,oBAAoB,UAAU,IAAI,CAAC,CAAA,WAAY,aAAa,QAAQ,CAAC;IAE3E,uBAAuB;IACvB,MAAM,eAAe;QAAC;QAAS;QAAQ;QAAQ;QAAS;QAAS;KAAW;IAC5E,MAAM,uBAAuB,aAAa,IAAI,CAAC,CAAA,OAAQ,aAAa,QAAQ,CAAC;IAE7E,mBAAmB;IACnB,IAAI,SAA0F;IAE9F,IAAI,aAAa,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,cAAc,aAAa,QAAQ,CAAC,cAAc;QAC3G,SAAS;IACX,OAAO,IAAI,aAAa,QAAQ,CAAC,QAAQ,aAAa,QAAQ,CAAC,UAAU,aAAa,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,QAAQ;QACtI,SAAS;IACX,OAAO,IAAI,aAAa,QAAQ,CAAC,aAAa,aAAa,QAAQ,CAAC,eAAe,aAAa,QAAQ,CAAC,gBAAgB;QACvH,SAAS;IACX,OAAO,IAAI,aAAa,QAAQ,CAAC,eAAe,aAAa,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,aAAa;QAClH,SAAS;IACX;IAEA,oBAAoB;IACpB,IAAI,UAAqC;IACzC,IAAI,aAAa,QAAQ,CAAC,aAAa,aAAa,QAAQ,CAAC,WAAW,aAAa,QAAQ,CAAC,YAAY;QACxG,UAAU;IACZ,OAAO,IAAI,aAAa,QAAQ,CAAC,uBAAuB,aAAa,QAAQ,CAAC,eAAe;QAC3F,UAAU;IACZ;IAEA,OAAO;QACL;QACA,UAAU;QACV,aAAa;QACb;IACF;AACF", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/lib/db/schema.ts"], "sourcesContent": ["import { pgTable, serial, text, timestamp, json, integer, boolean, date } from 'drizzle-orm/pg-core';\n\n// Users table (extends Clerk user data)\nexport const users = pgTable('users', {\n  id: text('id').primaryKey(), // Clerk user ID\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Profile Information\n  email: text('email').notNull(),\n  firstName: text('first_name'),\n  lastName: text('last_name'),\n  imageUrl: text('image_url'),\n  \n  // Social Media Goals\n  primaryGoal: text('primary_goal'), // follower_growth, engagement, monetization, brand_awareness\n  targetAudience: text('target_audience'),\n  currentFollowers: integer('current_followers').default(0),\n  targetFollowers: integer('target_followers'),\n  \n  // Platform Information\n  platforms: json('platforms').$type<{\n    platform: string;\n    username: string;\n    followers: number;\n    isActive: boolean;\n  }[]>().default([]),\n  \n  // Preferences\n  contentStyle: text('content_style'), // educational, entertaining, inspirational, promotional\n  postingFrequency: text('posting_frequency'), // daily, weekly, bi_weekly\n  timezone: text('timezone').default('UTC'),\n  language: text('language').default('en'),\n  \n  // Subscription\n  subscriptionStatus: text('subscription_status').default('free'), // free, pro, premium\n  subscriptionId: text('subscription_id'),\n  subscriptionEndsAt: timestamp('subscription_ends_at'),\n});\n\n// Conversations table\nexport const conversations = pgTable('conversations', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Conversation metadata\n  title: text('title'),\n  isActive: boolean('is_active').default(true),\n  \n  // Context and state\n  context: json('context').$type<{\n    currentGoal?: string;\n    roadmapId?: number;\n    lastArtifactId?: string;\n    userPreferences?: Record<string, any>;\n  }>().default({}),\n});\n\n// Messages table\nexport const messages = pgTable('messages', {\n  id: serial('id').primaryKey(),\n  conversationId: integer('conversation_id').references(() => conversations.id).notNull(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  \n  // Message content\n  role: text('role').notNull(), // user, assistant, system\n  content: text('content').notNull(),\n  \n  // AI function calling\n  functionCall: json('function_call').$type<{\n    name: string;\n    arguments: Record<string, any>;\n  }>(),\n  functionResult: json('function_result'),\n  \n  // Message metadata\n  metadata: json('metadata').$type<{\n    artifactId?: string;\n    tokens?: number;\n    model?: string;\n    processingTime?: number;\n  }>().default({}),\n});\n\n// Artifacts table\nexport const artifacts = pgTable('artifacts', {\n  id: text('id').primaryKey(), // UUID\n  conversationId: integer('conversation_id').references(() => conversations.id).notNull(),\n  userId: text('user_id').notNull().references(() => users.id),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Artifact information\n  type: text('type').notNull(), // goal_selector, roadmap_preview, progress_chart, etc.\n  title: text('title').notNull(),\n  description: text('description'),\n  \n  // Artifact data\n  data: json('data').notNull(),\n  \n  // State and interactions\n  isActive: boolean('is_active').default(true),\n  interactions: json('interactions').$type<{\n    timestamp: string;\n    action: string;\n    data: any;\n  }[]>().default([]),\n});\n\n// Roadmaps table\nexport const roadmaps = pgTable('roadmaps', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Roadmap information\n  title: text('title').notNull(),\n  description: text('description'),\n  goal: text('goal').notNull(), // follower_growth, engagement, etc.\n  \n  // Roadmap configuration\n  duration: integer('duration').notNull(), // days\n  difficulty: text('difficulty').notNull(), // beginner, intermediate, advanced\n  \n  // Progress tracking\n  status: text('status').default('active'), // active, completed, paused\n  progress: integer('progress').default(0), // percentage\n  \n  // Roadmap data\n  blocks: json('blocks').$type<{\n    id: string;\n    title: string;\n    description: string;\n    order: number;\n    estimatedTime: number;\n    dependencies: string[];\n  }[]>().notNull(),\n  \n  // Metadata\n  isCustom: boolean('is_custom').default(false),\n  templateId: text('template_id'),\n});\n\n// Content blocks table\nexport const contentBlocks = pgTable('content_blocks', {\n  id: serial('id').primaryKey(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  \n  // Block information\n  title: text('title').notNull(),\n  description: text('description').notNull(),\n  category: text('category').notNull(), // viral_format, strategy, analysis, etc.\n  \n  // Content structure\n  format: text('format').notNull(), // visual_metaphor, two_characters, untold_stories, challenge\n  difficulty: text('difficulty').notNull(), // beginner, intermediate, advanced\n  estimatedTime: integer('estimated_time').notNull(), // minutes\n  \n  // Block content\n  content: json('content').$type<{\n    sections: {\n      id: string;\n      title: string;\n      type: string; // text, checklist, template, example\n      content: string;\n      order: number;\n    }[];\n    examples: {\n      platform: string;\n      description: string;\n      content: string;\n    }[];\n    templates: {\n      name: string;\n      template: string;\n      variables: string[];\n    }[];\n  }>().notNull(),\n  \n  // Requirements and outcomes\n  prerequisites: json('prerequisites').$type<string[]>().default([]),\n  learningOutcomes: json('learning_outcomes').$type<string[]>().default([]),\n  \n  // Metadata\n  isActive: boolean('is_active').default(true),\n  sortOrder: integer('sort_order').default(0),\n  tags: json('tags').$type<string[]>().default([]),\n});\n\n// User block progress table\nexport const userBlockProgress = pgTable('user_block_progress', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  blockId: integer('block_id').references(() => contentBlocks.id).notNull(),\n  roadmapId: integer('roadmap_id').references(() => roadmaps.id),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Progress tracking\n  status: text('status').default('not_started'), // not_started, in_progress, completed\n  progress: integer('progress').default(0), // percentage\n  timeSpent: integer('time_spent').default(0), // minutes\n  \n  // Completion data\n  startedAt: timestamp('started_at'),\n  completedAt: timestamp('completed_at'),\n  \n  // User responses and work\n  responses: json('responses').$type<{\n    sectionId: string;\n    response: any;\n    timestamp: string;\n  }[]>().default([]),\n  \n  // Generated content\n  generatedContent: json('generated_content').$type<{\n    type: string;\n    content: string;\n    platform: string;\n    timestamp: string;\n  }[]>().default([]),\n  \n  // Feedback and notes\n  userNotes: text('user_notes'),\n  aifeedback: json('ai_feedback').$type<{\n    score: number;\n    feedback: string;\n    suggestions: string[];\n    timestamp: string;\n  }>(),\n});\n\n// Achievements table\nexport const achievements = pgTable('achievements', {\n  id: serial('id').primaryKey(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n\n  // Achievement Information\n  name: text('name').notNull(),\n  title: text('title').notNull(),\n  description: text('description').notNull(),\n  category: text('category').notNull(), // milestone, consistency, engagement, growth, content\n\n  // Achievement Criteria\n  criteria: json('criteria').$type<{\n    type: string; // follower_count, post_count, engagement_rate, streak_days, etc.\n    threshold: number;\n    timeframe?: number; // days\n    platform?: string;\n  }>().notNull(),\n\n  // Visual Elements\n  icon: text('icon').notNull(),\n  color: text('color').notNull(),\n  rarity: text('rarity').notNull(), // common, rare, epic, legendary\n\n  // Rewards\n  points: integer('points').default(0),\n  unlocks: json('unlocks').$type<string[]>(), // Features or content unlocked\n\n  // Metadata\n  isActive: boolean('is_active').default(true),\n  sortOrder: integer('sort_order').default(0),\n});\n\n// User Achievements table\nexport const userAchievements = pgTable('user_achievements', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  achievementId: integer('achievement_id').references(() => achievements.id).notNull(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n\n  // Achievement Data\n  unlockedAt: timestamp('unlocked_at').notNull(),\n  progress: integer('progress').default(100), // Percentage when unlocked\n\n  // Context\n  roadmapId: integer('roadmap_id').references(() => roadmaps.id),\n  triggerData: json('trigger_data'), // Data that triggered the achievement\n\n  // Celebration\n  wasNotified: boolean('was_notified').default(false),\n  wasCelebrated: boolean('was_celebrated').default(false),\n});\n\n// User Stats table\nexport const userStats = pgTable('user_stats', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n\n  // Overall Stats\n  totalPoints: integer('total_points').default(0),\n  level: integer('level').default(1),\n  experiencePoints: integer('experience_points').default(0),\n\n  // Achievement Stats\n  totalAchievements: integer('total_achievements').default(0),\n  commonAchievements: integer('common_achievements').default(0),\n  rareAchievements: integer('rare_achievements').default(0),\n  epicAchievements: integer('epic_achievements').default(0),\n  legendaryAchievements: integer('legendary_achievements').default(0),\n\n  // Activity Stats\n  streakDays: integer('streak_days').default(0),\n  longestStreak: integer('longest_streak').default(0),\n  lastActivityDate: timestamp('last_activity_date'),\n\n  // Content Stats\n  totalContentCreated: integer('total_content_created').default(0),\n  totalBlocksCompleted: integer('total_blocks_completed').default(0),\n  totalRoadmapsCompleted: integer('total_roadmaps_completed').default(0),\n});\n\n// Daily Activity table\nexport const dailyActivity = pgTable('daily_activity', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  date: date('date').notNull(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n\n  // Activity Data\n  activitiesCompleted: json('activities_completed').$type<{\n    type: string;\n    count: number;\n    details?: any;\n  }[]>().default([]),\n\n  // Points and Experience\n  pointsEarned: integer('points_earned').default(0),\n  experienceEarned: integer('experience_earned').default(0),\n\n  // Streaks\n  isStreakDay: boolean('is_streak_day').default(false),\n  streakCount: integer('streak_count').default(0),\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAGO,MAAM,QAAQ,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IACpC,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,sBAAsB;IACtB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAEf,qBAAqB;IACrB,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,gBAAgB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACrB,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IACvD,iBAAiB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE;IAEzB,uBAAuB;IACvB,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,aAAa,KAAK,GAK3B,OAAO,CAAC,EAAE;IAEjB,cAAc;IACd,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,kBAAkB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACvB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,CAAC;IACnC,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,CAAC;IAEnC,eAAe;IACf,oBAAoB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,uBAAuB,OAAO,CAAC;IACxD,gBAAgB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACrB,oBAAoB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;AAChC;AAGO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IACpD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,wBAAwB;IACxB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IAEvC,oBAAoB;IACpB,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,KAAK,GAKzB,OAAO,CAAC,CAAC;AAChB;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,gBAAgB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,UAAU,CAAC,IAAM,cAAc,EAAE,EAAE,OAAO;IACrF,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,kBAAkB;IAClB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAEhC,sBAAsB;IACtB,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,KAAK;IAIzC,gBAAgB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAErB,mBAAmB;IACnB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,KAAK,GAK3B,OAAO,CAAC,CAAC;AAChB;AAGO,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAC5C,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,gBAAgB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,UAAU,CAAC,IAAM,cAAc,EAAE,EAAE,OAAO;IACrF,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,uBAAuB;IACvB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAElB,gBAAgB;IAChB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAE1B,yBAAyB;IACzB,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,KAAK,GAIjC,OAAO,CAAC,EAAE;AACnB;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,sBAAsB;IACtB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAE1B,wBAAwB;IACxB,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO;IACrC,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IAEtC,oBAAoB;IACpB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO,CAAC;IAC/B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IAEtC,eAAe;IACf,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,KAAK,GAOrB,OAAO;IAEd,WAAW;IACX,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;AACnB;AAGO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IACrD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,oBAAoB;IACpB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACxC,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAElC,oBAAoB;IACpB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO;IAEhD,gBAAgB;IAChB,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,KAAK,GAkBzB,OAAO;IAEZ,4BAA4B;IAC5B,eAAe,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,KAAK,GAAa,OAAO,CAAC,EAAE;IACjE,kBAAkB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,KAAK,GAAa,OAAO,CAAC,EAAE;IAExE,WAAW;IACX,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,CAAC;IACzC,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,KAAK,GAAa,OAAO,CAAC,EAAE;AACjD;AAGO,MAAM,oBAAoB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB;IAC9D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,UAAU,CAAC,IAAM,cAAc,EAAE,EAAE,OAAO;IACvE,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC7D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,oBAAoB;IACpB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO,CAAC;IAC/B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IACtC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,CAAC;IAEzC,kBAAkB;IAClB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,aAAa,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAEvB,0BAA0B;IAC1B,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,aAAa,KAAK,GAI3B,OAAO,CAAC,EAAE;IAEjB,oBAAoB;IACpB,kBAAkB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,KAAK,GAK1C,OAAO,CAAC,EAAE;IAEjB,qBAAqB;IACrB,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,KAAK;AAMvC;AAGO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;IAClD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,0BAA0B;IAC1B,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACxC,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAElC,uBAAuB;IACvB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,KAAK,GAK3B,OAAO;IAEZ,kBAAkB;IAClB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAE9B,UAAU;IACV,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,CAAC;IAClC,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,KAAK;IAE9B,WAAW;IACX,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,CAAC;AAC3C;AAGO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;IAC3D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,UAAU,CAAC,IAAM,aAAa,EAAE,EAAE,OAAO;IAClF,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,mBAAmB;IACnB,YAAY,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,eAAe,OAAO;IAC5C,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IAEtC,UAAU;IACV,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC7D,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAElB,cAAc;IACd,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO,CAAC;IAC7C,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO,CAAC;AACnD;AAGO,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IAC7C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,gBAAgB;IAChB,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO,CAAC;IAC7C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,CAAC;IAChC,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IAEvD,oBAAoB;IACpB,mBAAmB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,OAAO,CAAC;IACzD,oBAAoB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB,OAAO,CAAC;IAC3D,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IACvD,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IACvD,uBAAuB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,0BAA0B,OAAO,CAAC;IAEjE,iBAAiB;IACjB,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO,CAAC;IAC3C,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO,CAAC;IACjD,kBAAkB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAE5B,gBAAgB;IAChB,qBAAqB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,yBAAyB,OAAO,CAAC;IAC9D,sBAAsB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,0BAA0B,OAAO,CAAC;IAChE,wBAAwB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,4BAA4B,OAAO,CAAC;AACtE;AAGO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IACrD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,gBAAgB;IAChB,qBAAqB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,wBAAwB,KAAK,GAIhD,OAAO,CAAC,EAAE;IAEjB,wBAAwB;IACxB,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO,CAAC;IAC/C,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IAEvD,UAAU;IACV,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO,CAAC;IAC9C,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO,CAAC;AAC/C", "debugId": null}}, {"offset": {"line": 864, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/lib/db/index.ts"], "sourcesContent": ["import { drizzle } from 'drizzle-orm/node-postgres';\nimport { Pool } from 'pg';\nimport * as schema from './schema';\n\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n});\n\nexport const db = drizzle(pool, { schema });\n\nexport * from './schema';\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;;;;;AAEA,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;AAC5C;AAEO,MAAM,KAAK,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAAE,QAAA;AAAO", "debugId": null}}, {"offset": {"line": 908, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/lib/services/conversation.ts"], "sourcesContent": ["import { db } from '@/lib/db';\nimport { conversations, messages } from '@/lib/db/schema';\nimport { eq, desc } from 'drizzle-orm';\n\nexport interface ConversationContext {\n  userId: string;\n  conversationId?: number;\n  userProfile?: any;\n  currentGoals?: string[];\n  recentActivity?: any[];\n}\n\nexport interface ConversationMessage {\n  role: 'system' | 'user' | 'assistant';\n  content: string;\n}\n\nexport class ConversationService {\n\n  /**\n   * Create a new conversation\n   */\n  async createConversation(userId: string, title?: string): Promise<number> {\n    const conversation = await db\n      .insert(conversations)\n      .values({\n        userId,\n        title: title || 'New Conversation',\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      })\n      .returning();\n\n    return conversation[0].id;\n  }\n\n  /**\n   * Get conversation history\n   */\n  async getConversationHistory(conversationId: number): Promise<ConversationMessage[]> {\n    const messageHistory = await db\n      .select()\n      .from(messages)\n      .where(eq(messages.conversationId, conversationId))\n      .orderBy(messages.createdAt);\n\n    return messageHistory.map(msg => ({\n      role: msg.role as 'system' | 'user' | 'assistant',\n      content: msg.content,\n    }));\n  }\n\n  /**\n   * Save message to conversation\n   */\n  async saveMessage(\n    conversationId: number,\n    role: 'user' | 'assistant',\n    content: string,\n    metadata?: any\n  ): Promise<void> {\n    await db.insert(messages).values({\n      conversationId,\n      role,\n      content,\n      metadata,\n      createdAt: new Date(),\n    });\n\n    // Update conversation timestamp\n    await db\n      .update(conversations)\n      .set({ updatedAt: new Date() })\n      .where(eq(conversations.id, conversationId));\n  }\n\n  /**\n   * Build system prompt with user context\n   * This method is used by the API routes for generating context-aware prompts\n   */\n  buildSystemPrompt(context: ConversationContext): string {\n    const { userProfile, currentGoals } = context;\n\n    let prompt = `You are Influtify, an AI social media growth mentor based on Brendan Kane's \"The Guide to Going Viral\" methodology.\n\nYour expertise includes:\n- The 4 viral content formats: Educational, Entertaining, Inspirational, Conversational\n- Hook-Retain-Reward framework for viral content\n- Platform-specific strategies for Instagram, TikTok, YouTube, Twitter, LinkedIn\n- Audience analysis and engagement optimization\n- Content planning and consistency strategies\n\n`;\n\n    if (userProfile) {\n      prompt += `User Profile:\n- Platform focus: ${userProfile.platform || 'Not specified'}\n- Current follower count: ${userProfile.followerCount || 'Not specified'}\n- Content niche: ${userProfile.niche || 'Not specified'}\n- Experience level: ${userProfile.experienceLevel || 'Beginner'}\n\n`;\n    }\n\n    if (currentGoals && currentGoals.length > 0) {\n      prompt += `Current Goals:\n${currentGoals.map(goal => `- ${goal}`).join('\\n')}\n\n`;\n    }\n\n    prompt += `Provide actionable, specific advice based on proven viral strategies. Always include practical next steps the user can implement immediately.`;\n\n    return prompt;\n  }\n\n\n\n  /**\n   * Get user's recent conversations\n   */\n  async getUserConversations(userId: string, limit: number = 10) {\n    return await db\n      .select()\n      .from(conversations)\n      .where(eq(conversations.userId, userId))\n      .orderBy(desc(conversations.updatedAt))\n      .limit(limit);\n  }\n\n  /**\n   * Update conversation title\n   */\n  async updateConversationTitle(conversationId: number, title: string): Promise<void> {\n    await db\n      .update(conversations)\n      .set({\n        title,\n        updatedAt: new Date()\n      })\n      .where(eq(conversations.id, conversationId));\n  }\n\n  /**\n   * Delete conversation and all its messages\n   */\n  async deleteConversation(conversationId: number): Promise<void> {\n    // Delete messages first (due to foreign key constraint)\n    await db.delete(messages).where(eq(messages.conversationId, conversationId));\n\n    // Delete conversation\n    await db.delete(conversations).where(eq(conversations.id, conversationId));\n  }\n\n  /**\n   * Get conversation by ID with basic info\n   */\n  async getConversation(conversationId: number) {\n    const conversation = await db\n      .select()\n      .from(conversations)\n      .where(eq(conversations.id, conversationId))\n      .limit(1);\n\n    return conversation[0] || null;\n  }\n}\n\nexport const conversationService = new ConversationService();\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AAAA;;;;;;;;;AAeO,MAAM;IAEX;;GAEC,GACD,MAAM,mBAAmB,MAAc,EAAE,KAAc,EAAmB;QACxE,MAAM,eAAe,MAAM,2IAAA,CAAA,KAAE,CAC1B,MAAM,CAAC,4HAAA,CAAA,gBAAa,EACpB,MAAM,CAAC;YACN;YACA,OAAO,SAAS;YAChB,WAAW,IAAI;YACf,WAAW,IAAI;QACjB,GACC,SAAS;QAEZ,OAAO,YAAY,CAAC,EAAE,CAAC,EAAE;IAC3B;IAEA;;GAEC,GACD,MAAM,uBAAuB,cAAsB,EAAkC;QACnF,MAAM,iBAAiB,MAAM,2IAAA,CAAA,KAAE,CAC5B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,WAAQ,EACb,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,WAAQ,CAAC,cAAc,EAAE,iBAClC,OAAO,CAAC,4HAAA,CAAA,WAAQ,CAAC,SAAS;QAE7B,OAAO,eAAe,GAAG,CAAC,CAAA,MAAO,CAAC;gBAChC,MAAM,IAAI,IAAI;gBACd,SAAS,IAAI,OAAO;YACtB,CAAC;IACH;IAEA;;GAEC,GACD,MAAM,YACJ,cAAsB,EACtB,IAA0B,EAC1B,OAAe,EACf,QAAc,EACC;QACf,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,WAAQ,EAAE,MAAM,CAAC;YAC/B;YACA;YACA;YACA;YACA,WAAW,IAAI;QACjB;QAEA,gCAAgC;QAChC,MAAM,2IAAA,CAAA,KAAE,CACL,MAAM,CAAC,4HAAA,CAAA,gBAAa,EACpB,GAAG,CAAC;YAAE,WAAW,IAAI;QAAO,GAC5B,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,gBAAa,CAAC,EAAE,EAAE;IAChC;IAEA;;;GAGC,GACD,kBAAkB,OAA4B,EAAU;QACtD,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG;QAEtC,IAAI,SAAS,CAAC;;;;;;;;;AASlB,CAAC;QAEG,IAAI,aAAa;YACf,UAAU,CAAC;kBACC,EAAE,YAAY,QAAQ,IAAI,gBAAgB;0BAClC,EAAE,YAAY,aAAa,IAAI,gBAAgB;iBACxD,EAAE,YAAY,KAAK,IAAI,gBAAgB;oBACpC,EAAE,YAAY,eAAe,IAAI,WAAW;;AAEhE,CAAC;QACG;QAEA,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;YAC3C,UAAU,CAAC;AACjB,EAAE,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM;;AAEnD,CAAC;QACG;QAEA,UAAU,CAAC,6IAA6I,CAAC;QAEzJ,OAAO;IACT;IAIA;;GAEC,GACD,MAAM,qBAAqB,MAAc,EAAE,QAAgB,EAAE,EAAE;QAC7D,OAAO,MAAM,2IAAA,CAAA,KAAE,CACZ,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,gBAAa,EAClB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,gBAAa,CAAC,MAAM,EAAE,SAC/B,OAAO,CAAC,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD,EAAE,4HAAA,CAAA,gBAAa,CAAC,SAAS,GACpC,KAAK,CAAC;IACX;IAEA;;GAEC,GACD,MAAM,wBAAwB,cAAsB,EAAE,KAAa,EAAiB;QAClF,MAAM,2IAAA,CAAA,KAAE,CACL,MAAM,CAAC,4HAAA,CAAA,gBAAa,EACpB,GAAG,CAAC;YACH;YACA,WAAW,IAAI;QACjB,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,gBAAa,CAAC,EAAE,EAAE;IAChC;IAEA;;GAEC,GACD,MAAM,mBAAmB,cAAsB,EAAiB;QAC9D,wDAAwD;QACxD,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,WAAQ,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,WAAQ,CAAC,cAAc,EAAE;QAE5D,sBAAsB;QACtB,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,gBAAa,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,gBAAa,CAAC,EAAE,EAAE;IAC5D;IAEA;;GAEC,GACD,MAAM,gBAAgB,cAAsB,EAAE;QAC5C,MAAM,eAAe,MAAM,2IAAA,CAAA,KAAE,CAC1B,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,gBAAa,EAClB,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,gBAAa,CAAC,EAAE,EAAE,iBAC3B,KAAK,CAAC;QAET,OAAO,YAAY,CAAC,EAAE,IAAI;IAC5B;AACF;AAEO,MAAM,sBAAsB,IAAI", "debugId": null}}, {"offset": {"line": 1030, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/lib/services/user.ts"], "sourcesContent": ["import { db } from '@/lib/db';\nimport { users, userStats, achievements, userAchievements } from '@/lib/db/schema';\nimport { eq, and } from 'drizzle-orm';\nimport { auth } from '@clerk/nextjs/server';\n\nexport interface UserProfile {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  imageUrl: string;\n  platforms: Record<string, any>;\n  goals: string[];\n  currentFollowers: number;\n  targetFollowers: number;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface UserStatsData {\n  totalPoints: number;\n  currentStreak: number;\n  longestStreak: number;\n  level: number;\n  completedBlocks: number;\n  totalSessions: number;\n  lastActiveAt: Date;\n}\n\nexport class UserService {\n  /**\n   * Get the current authenticated user's profile\n   */\n  static async getCurrentUser(): Promise<UserProfile | null> {\n    const { userId } = await auth();\n    if (!userId) return null;\n\n    const user = await db\n      .select()\n      .from(users)\n      .where(eq(users.id, userId))\n      .limit(1);\n\n    return user[0] || null;\n  }\n\n  /**\n   * Get user by Clerk ID\n   */\n  static async getUserByClerkId(clerkId: string): Promise<UserProfile | null> {\n    const user = await db\n      .select()\n      .from(users)\n      .where(eq(users.id, clerkId))\n      .limit(1);\n\n    return user[0] || null;\n  }\n\n  /**\n   * Create or update user profile\n   */\n  static async upsertUser(userData: Partial<UserProfile>): Promise<UserProfile> {\n    const existingUser = await this.getUserByClerkId(userData.id!);\n\n    if (existingUser) {\n      // Update existing user\n      const updated = await db\n        .update(users)\n        .set({\n          ...userData,\n          updatedAt: new Date(),\n        })\n        .where(eq(users.id, userData.id!))\n        .returning();\n\n      return updated[0];\n    } else {\n      // Create new user\n      const created = await db\n        .insert(users)\n        .values({\n          ...userData,\n          createdAt: new Date(),\n          updatedAt: new Date(),\n        } as any)\n        .returning();\n\n      // Initialize user stats\n      await this.initializeUserStats(created[0].id);\n\n      return created[0];\n    }\n  }\n\n  /**\n   * Update user profile\n   */\n  static async updateProfile(\n    clerkId: string,\n    updates: Partial<Pick<UserProfile, 'platforms' | 'goals' | 'currentFollowers' | 'targetFollowers'>>\n  ): Promise<UserProfile | null> {\n    const updated = await db\n      .update(users)\n      .set({\n        ...updates,\n        updatedAt: new Date(),\n      })\n      .where(eq(users.clerkId, clerkId))\n      .returning();\n\n    return updated[0] || null;\n  }\n\n  /**\n   * Get user statistics\n   */\n  static async getUserStats(userId: string): Promise<UserStatsData | null> {\n    const stats = await db\n      .select()\n      .from(userStats)\n      .where(eq(userStats.userId, userId))\n      .limit(1);\n\n    return stats[0] || null;\n  }\n\n  /**\n   * Initialize user statistics for new user\n   */\n  static async initializeUserStats(userId: string): Promise<void> {\n    await db.insert(userStats).values({\n      userId,\n      totalPoints: 0,\n      currentStreak: 0,\n      longestStreak: 0,\n      level: 1,\n      completedBlocks: 0,\n      totalSessions: 0,\n      lastActiveAt: new Date(),\n    });\n  }\n\n  /**\n   * Update user statistics\n   */\n  static async updateStats(\n    userId: string,\n    updates: Partial<UserStatsData>\n  ): Promise<UserStatsData | null> {\n    const updated = await db\n      .update(userStats)\n      .set(updates)\n      .where(eq(userStats.userId, userId))\n      .returning();\n\n    return updated[0] || null;\n  }\n\n  /**\n   * Award achievement to user\n   */\n  static async awardAchievement(userId: string, achievementId: string): Promise<void> {\n    // Check if user already has this achievement\n    const existing = await db\n      .select()\n      .from(userAchievements)\n      .where(\n        and(\n          eq(userAchievements.userId, userId),\n          eq(userAchievements.achievementId, achievementId)\n        )\n      )\n      .limit(1);\n\n    if (existing.length === 0) {\n      await db.insert(userAchievements).values({\n        userId,\n        achievementId,\n        unlockedAt: new Date(),\n      });\n    }\n  }\n\n  /**\n   * Get user achievements\n   */\n  static async getUserAchievements(userId: string) {\n    return await db\n      .select({\n        achievement: achievements,\n        unlockedAt: userAchievements.unlockedAt,\n      })\n      .from(userAchievements)\n      .innerJoin(achievements, eq(achievements.id, userAchievements.achievementId))\n      .where(eq(userAchievements.userId, userId));\n  }\n\n  /**\n   * Add points to user and check for level up\n   */\n  static async addPoints(userId: string, points: number): Promise<{ leveledUp: boolean; newLevel?: number }> {\n    const currentStats = await this.getUserStats(userId);\n    if (!currentStats) throw new Error('User stats not found');\n\n    const newPoints = currentStats.totalPoints + points;\n    const newLevel = Math.floor(newPoints / 1000) + 1; // 1000 points per level\n    const leveledUp = newLevel > currentStats.level;\n\n    await this.updateStats(userId, {\n      totalPoints: newPoints,\n      level: newLevel,\n      lastActiveAt: new Date(),\n    });\n\n    return { leveledUp, newLevel: leveledUp ? newLevel : undefined };\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;;;AA0BO,MAAM;IACX;;GAEC,GACD,aAAa,iBAA8C;QACzD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,qLAAA,CAAA,OAAI,AAAD;QAC5B,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,OAAO,MAAM,2IAAA,CAAA,KAAE,CAClB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,QAAK,EACV,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,EAAE,EAAE,SACnB,KAAK,CAAC;QAET,OAAO,IAAI,CAAC,EAAE,IAAI;IACpB;IAEA;;GAEC,GACD,aAAa,iBAAiB,OAAe,EAA+B;QAC1E,MAAM,OAAO,MAAM,2IAAA,CAAA,KAAE,CAClB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,QAAK,EACV,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,EAAE,EAAE,UACnB,KAAK,CAAC;QAET,OAAO,IAAI,CAAC,EAAE,IAAI;IACpB;IAEA;;GAEC,GACD,aAAa,WAAW,QAA8B,EAAwB;QAC5E,MAAM,eAAe,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE;QAE5D,IAAI,cAAc;YAChB,uBAAuB;YACvB,MAAM,UAAU,MAAM,2IAAA,CAAA,KAAE,CACrB,MAAM,CAAC,4HAAA,CAAA,QAAK,EACZ,GAAG,CAAC;gBACH,GAAG,QAAQ;gBACX,WAAW,IAAI;YACjB,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,EAAE,EAAE,SAAS,EAAE,GAC9B,SAAS;YAEZ,OAAO,OAAO,CAAC,EAAE;QACnB,OAAO;YACL,kBAAkB;YAClB,MAAM,UAAU,MAAM,2IAAA,CAAA,KAAE,CACrB,MAAM,CAAC,4HAAA,CAAA,QAAK,EACZ,MAAM,CAAC;gBACN,GAAG,QAAQ;gBACX,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB,GACC,SAAS;YAEZ,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YAE5C,OAAO,OAAO,CAAC,EAAE;QACnB;IACF;IAEA;;GAEC,GACD,aAAa,cACX,OAAe,EACf,OAAmG,EACtE;QAC7B,MAAM,UAAU,MAAM,2IAAA,CAAA,KAAE,CACrB,MAAM,CAAC,4HAAA,CAAA,QAAK,EACZ,GAAG,CAAC;YACH,GAAG,OAAO;YACV,WAAW,IAAI;QACjB,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,OAAO,EAAE,UACxB,SAAS;QAEZ,OAAO,OAAO,CAAC,EAAE,IAAI;IACvB;IAEA;;GAEC,GACD,aAAa,aAAa,MAAc,EAAiC;QACvE,MAAM,QAAQ,MAAM,2IAAA,CAAA,KAAE,CACnB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,YAAS,EACd,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,YAAS,CAAC,MAAM,EAAE,SAC3B,KAAK,CAAC;QAET,OAAO,KAAK,CAAC,EAAE,IAAI;IACrB;IAEA;;GAEC,GACD,aAAa,oBAAoB,MAAc,EAAiB;QAC9D,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,YAAS,EAAE,MAAM,CAAC;YAChC;YACA,aAAa;YACb,eAAe;YACf,eAAe;YACf,OAAO;YACP,iBAAiB;YACjB,eAAe;YACf,cAAc,IAAI;QACpB;IACF;IAEA;;GAEC,GACD,aAAa,YACX,MAAc,EACd,OAA+B,EACA;QAC/B,MAAM,UAAU,MAAM,2IAAA,CAAA,KAAE,CACrB,MAAM,CAAC,4HAAA,CAAA,YAAS,EAChB,GAAG,CAAC,SACJ,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,YAAS,CAAC,MAAM,EAAE,SAC3B,SAAS;QAEZ,OAAO,OAAO,CAAC,EAAE,IAAI;IACvB;IAEA;;GAEC,GACD,aAAa,iBAAiB,MAAc,EAAE,aAAqB,EAAiB;QAClF,6CAA6C;QAC7C,MAAM,WAAW,MAAM,2IAAA,CAAA,KAAE,CACtB,MAAM,GACN,IAAI,CAAC,4HAAA,CAAA,mBAAgB,EACrB,KAAK,CACJ,CAAA,GAAA,oKAAA,CAAA,MAAG,AAAD,EACA,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,mBAAgB,CAAC,MAAM,EAAE,SAC5B,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,mBAAgB,CAAC,aAAa,EAAE,iBAGtC,KAAK,CAAC;QAET,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC;gBACvC;gBACA;gBACA,YAAY,IAAI;YAClB;QACF;IACF;IAEA;;GAEC,GACD,aAAa,oBAAoB,MAAc,EAAE;QAC/C,OAAO,MAAM,2IAAA,CAAA,KAAE,CACZ,MAAM,CAAC;YACN,aAAa,4HAAA,CAAA,eAAY;YACzB,YAAY,4HAAA,CAAA,mBAAgB,CAAC,UAAU;QACzC,GACC,IAAI,CAAC,4HAAA,CAAA,mBAAgB,EACrB,SAAS,CAAC,4HAAA,CAAA,eAAY,EAAE,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,eAAY,CAAC,EAAE,EAAE,4HAAA,CAAA,mBAAgB,CAAC,aAAa,GAC1E,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,mBAAgB,CAAC,MAAM,EAAE;IACvC;IAEA;;GAEC,GACD,aAAa,UAAU,MAAc,EAAE,MAAc,EAAsD;QACzG,MAAM,eAAe,MAAM,IAAI,CAAC,YAAY,CAAC;QAC7C,IAAI,CAAC,cAAc,MAAM,IAAI,MAAM;QAEnC,MAAM,YAAY,aAAa,WAAW,GAAG;QAC7C,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY,QAAQ,GAAG,wBAAwB;QAC3E,MAAM,YAAY,WAAW,aAAa,KAAK;QAE/C,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ;YAC7B,aAAa;YACb,OAAO;YACP,cAAc,IAAI;QACpB;QAEA,OAAO;YAAE;YAAW,UAAU,YAAY,WAAW;QAAU;IACjE;AACF", "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/app/api/ai/stream/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { auth } from '@clerk/nextjs/server';\nimport { generateInflutifyMentorResponse } from '../../../../lib/services/ai-provider';\nimport { conversationService } from '../../../../lib/services/conversation';\nimport { UserService } from '../../../../lib/services/user';\nimport { z } from 'zod';\n\nconst chatRequestSchema = z.object({\n  message: z.string().min(1, 'Message cannot be empty'),\n  conversationId: z.coerce.number().optional(),\n});\n\nexport async function GET() {\n  return NextResponse.json({ message: 'AI stream API is working' });\n}\n\nexport async function POST(req: NextRequest) {\n  try {\n    const { userId } = await auth();\n\n    if (!userId) {\n      return NextResponse.json(\n        { error: 'Unauthorized' },\n        { status: 401 }\n      );\n    }\n\n    const body = await req.json();\n    const { message, conversationId } = chatRequestSchema.parse(body);\n\n    // Get user profile for context\n    const userProfile = await UserService.getCurrentUser();\n\n    // Get conversation history for context\n    let conversationHistory: any[] = [];\n    if (conversationId) {\n      conversationHistory = await conversationService.getConversationHistory(conversationId);\n    }\n\n    // Build conversation context\n    const context = {\n      userId,\n      conversationId,\n      userProfile,\n      currentGoals: userProfile?.goals || [],\n      conversationHistory,\n    };\n\n    // Create or get conversation ID\n    let finalConversationId = conversationId;\n    if (!finalConversationId) {\n      finalConversationId = await conversationService.createConversation(userId, 'Chat Session');\n    }\n\n    // Save user message\n    await conversationService.saveMessage(finalConversationId, 'user', message);\n\n    // Generate AI response stream\n    const aiResponseStream = await generateInflutifyMentorResponse(message, context);\n\n    // Create a new stream that handles the AI response and saves it\n    const stream = new ReadableStream({\n      async start(controller) {\n        try {\n          const reader = aiResponseStream.getReader();\n          let fullResponse = '';\n\n          // Send conversation ID first\n          controller.enqueue(\n            new TextEncoder().encode(JSON.stringify({\n              type: 'conversation_id',\n              conversationId: finalConversationId\n            }) + '\\n')\n          );\n\n          // Stream the AI response\n          while (true) {\n            const { done, value } = await reader.read();\n            if (done) break;\n\n            const chunk = new TextDecoder().decode(value);\n            fullResponse += chunk;\n\n            // Forward the chunk to the client\n            controller.enqueue(\n              new TextEncoder().encode(JSON.stringify({\n                type: 'content',\n                content: chunk\n              }) + '\\n')\n            );\n          }\n\n          // Save assistant message\n          await conversationService.saveMessage(finalConversationId, 'assistant', fullResponse);\n\n          controller.close();\n\n        } catch (error) {\n          console.error('AI streaming error:', error);\n          const errorResponse = {\n            type: 'error',\n            error: 'Failed to generate response',\n            details: error instanceof Error ? error.message : 'Unknown error'\n          };\n          controller.enqueue(\n            new TextEncoder().encode(JSON.stringify(errorResponse) + '\\n')\n          );\n          controller.close();\n        }\n      }\n    });\n\n    return new Response(stream, {\n      headers: {\n        'Content-Type': 'application/json; charset=utf-8',\n        'Cache-Control': 'no-cache',\n        'Connection': 'keep-alive',\n        'X-Conversation-Id': finalConversationId.toString(),\n      },\n    });\n\n  } catch (error) {\n    console.error('AI stream API error:', error);\n\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Invalid request data', details: error.errors },\n        { status: 400 }\n      );\n    }\n\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEA,MAAM,oBAAoB,iKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,SAAS,iKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,gBAAgB,iKAAA,CAAA,IAAC,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ;AAC5C;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAA2B;AACjE;AAEO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,qLAAA,CAAA,OAAI,AAAD;QAE5B,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAe,GACxB;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,kBAAkB,KAAK,CAAC;QAE5D,+BAA+B;QAC/B,MAAM,cAAc,MAAM,gIAAA,CAAA,cAAW,CAAC,cAAc;QAEpD,uCAAuC;QACvC,IAAI,sBAA6B,EAAE;QACnC,IAAI,gBAAgB;YAClB,sBAAsB,MAAM,wIAAA,CAAA,sBAAmB,CAAC,sBAAsB,CAAC;QACzE;QAEA,6BAA6B;QAC7B,MAAM,UAAU;YACd;YACA;YACA;YACA,cAAc,aAAa,SAAS,EAAE;YACtC;QACF;QAEA,gCAAgC;QAChC,IAAI,sBAAsB;QAC1B,IAAI,CAAC,qBAAqB;YACxB,sBAAsB,MAAM,wIAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC,QAAQ;QAC7E;QAEA,oBAAoB;QACpB,MAAM,wIAAA,CAAA,sBAAmB,CAAC,WAAW,CAAC,qBAAqB,QAAQ;QAEnE,8BAA8B;QAC9B,MAAM,mBAAmB,MAAM,CAAA,GAAA,0IAAA,CAAA,kCAA+B,AAAD,EAAE,SAAS;QAExE,gEAAgE;QAChE,MAAM,SAAS,IAAI,eAAe;YAChC,MAAM,OAAM,UAAU;gBACpB,IAAI;oBACF,MAAM,SAAS,iBAAiB,SAAS;oBACzC,IAAI,eAAe;oBAEnB,6BAA6B;oBAC7B,WAAW,OAAO,CAChB,IAAI,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC;wBACtC,MAAM;wBACN,gBAAgB;oBAClB,KAAK;oBAGP,yBAAyB;oBACzB,MAAO,KAAM;wBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;wBACzC,IAAI,MAAM;wBAEV,MAAM,QAAQ,IAAI,cAAc,MAAM,CAAC;wBACvC,gBAAgB;wBAEhB,kCAAkC;wBAClC,WAAW,OAAO,CAChB,IAAI,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC;4BACtC,MAAM;4BACN,SAAS;wBACX,KAAK;oBAET;oBAEA,yBAAyB;oBACzB,MAAM,wIAAA,CAAA,sBAAmB,CAAC,WAAW,CAAC,qBAAqB,aAAa;oBAExE,WAAW,KAAK;gBAElB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,MAAM,gBAAgB;wBACpB,MAAM;wBACN,OAAO;wBACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBACpD;oBACA,WAAW,OAAO,CAChB,IAAI,cAAc,MAAM,CAAC,KAAK,SAAS,CAAC,iBAAiB;oBAE3D,WAAW,KAAK;gBAClB;YACF;QACF;QAEA,OAAO,IAAI,SAAS,QAAQ;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;gBACjB,cAAc;gBACd,qBAAqB,oBAAoB,QAAQ;YACnD;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QAEtC,IAAI,iBAAiB,iKAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAwB,SAAS,MAAM,MAAM;YAAC,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}