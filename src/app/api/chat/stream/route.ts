import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { conversationService } from '@/lib/services/conversation';
import { UserService } from '@/lib/services/user';
import {
  generateInflutifyMentorResponse,
  getCurrentProvider,
  analyzeUserIntent,
  type InflutifyContext,
  type InflutifyMessage
} from '@/lib/services/ai-provider';
import { z } from 'zod';

const chatStreamRequestSchema = z.object({
  message: z.string().min(1, 'Message cannot be empty'),
  conversationId: z.string().optional(),
  platform: z.string().optional(),
  followerCount: z.number().optional(),
});

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { message, conversationId, platform, followerCount } = chatStreamRequestSchema.parse(body);

    // Analyze user intent for better response tailoring
    const userIntent = analyzeUserIntent(message);
    console.log('[Influtify] User intent analysis:', userIntent);

    // Get user profile for context
    const userProfile = await UserService.getCurrentUser();

    // Get or create conversation
    let finalConversationId = conversationId;
    if (!finalConversationId) {
      finalConversationId = await conversationService.createConversation(
        userId,
        generateConversationTitle(message)
      );
    }

    // Get conversation history for context
    const history = await conversationService.getConversationHistory(finalConversationId);

    // Convert to Influtify message format
    const conversationHistory: InflutifyMessage[] = history.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
      timestamp: msg.createdAt
    }));

    // Build Influtify context
    const influtifyContext: InflutifyContext = {
      userId,
      userProfile,
      conversationHistory,
      currentGoals: userProfile?.goals || [],
      platform: platform || userProfile?.preferredPlatform,
      followerCount: followerCount || userProfile?.followerCount,
    };

    // Save user message before generating response
    await conversationService.saveMessage(finalConversationId, 'user', message);

    // Generate streaming AI response using Influtify-specific logic
    const stream = await generateInflutifyMentorResponse(message, influtifyContext);
    
    // Create a transform stream to collect the response for saving
    let fullResponse = '';
    const transformStream = new TransformStream({
      transform(chunk, controller) {
        const text = new TextDecoder().decode(chunk);
        fullResponse += text;
        controller.enqueue(chunk);
      },
      flush() {
        // Save the complete AI response after streaming is done
        if (fullResponse.trim()) {
          conversationService.saveMessage(finalConversationId!, 'assistant', fullResponse.trim())
            .catch(error => console.error('[Influtify] Error saving AI response:', error));
        }
      }
    });

    // Return streaming response with Influtify-specific headers
    return new Response(stream.pipeThrough(transformStream), {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'X-Conversation-Id': finalConversationId,
        'X-AI-Provider': getCurrentProvider(),
        'X-User-Intent': userIntent.intent,
        'X-Platform': userIntent.platform || 'unknown',
        'X-Urgency': userIntent.urgency,
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });

  } catch (error) {
    console.error('[Influtify] Chat stream API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * Generate a meaningful conversation title from the first message
 */
function generateConversationTitle(message: string): string {
  // Extract key topics or goals from the message
  const lowerMessage = message.toLowerCase();

  // Platform-specific titles
  if (lowerMessage.includes('instagram')) return 'Instagram Growth Strategy';
  if (lowerMessage.includes('tiktok')) return 'TikTok Viral Content';
  if (lowerMessage.includes('youtube')) return 'YouTube Channel Growth';
  if (lowerMessage.includes('linkedin')) return 'LinkedIn Professional Growth';
  if (lowerMessage.includes('twitter')) return 'Twitter Engagement Strategy';

  // Goal-specific titles
  if (lowerMessage.includes('viral') || lowerMessage.includes('go viral')) return 'Viral Content Strategy';
  if (lowerMessage.includes('followers') || lowerMessage.includes('grow')) return 'Follower Growth Plan';
  if (lowerMessage.includes('engagement')) return 'Engagement Optimization';
  if (lowerMessage.includes('content')) return 'Content Strategy';
  if (lowerMessage.includes('brand')) return 'Brand Building';

  // Content-specific titles
  if (lowerMessage.includes('video') || lowerMessage.includes('reel')) return 'Video Content Strategy';
  if (lowerMessage.includes('post')) return 'Post Optimization';
  if (lowerMessage.includes('story') || lowerMessage.includes('stories')) return 'Story Strategy';

  // Default titles based on intent
  if (lowerMessage.includes('help') || lowerMessage.includes('how')) return 'Social Media Guidance';
  if (lowerMessage.includes('start')) return 'Getting Started';

  // Fallback to a generic but encouraging title
  return 'Social Media Growth Journey';
}

/**
 * Health check endpoint for the streaming chat API
 */
export async function GET() {
  try {
    const provider = getCurrentProvider();

    return NextResponse.json({
      status: 'healthy',
      provider,
      timestamp: new Date().toISOString(),
      service: 'Influtify Chat Stream API'
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
