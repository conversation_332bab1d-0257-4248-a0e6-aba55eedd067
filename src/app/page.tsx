import { SignIn<PERSON><PERSON>on, SignU<PERSON><PERSON><PERSON>on, User<PERSON><PERSON>on } from '@clerk/nextjs';
import { auth } from '@clerk/nextjs/server';
import Link from 'next/link';

export default async function Home() {
  const { userId } = await auth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5">
      {/* Navigation */}
      <div className="navbar bg-base-100/80 backdrop-blur-md shadow-lg border-b border-base-300">
        <div className="navbar-start">
          <Link href="/" className="btn btn-ghost text-xl font-bold text-primary hover:text-primary-focus">
            🚀 Influtify
          </Link>
        </div>
        <div className="navbar-end">
          {userId ? (
            <div className="flex items-center gap-3">
              <Link href="/dashboard" className="btn btn-primary btn-sm">
                Dashboard
              </Link>
              <UserButton />
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <SignInButton>
                <button className="btn btn-ghost btn-sm">Sign In</button>
              </SignInButton>
              <SignUpButton>
                <button className="btn btn-primary btn-sm">Get Started</button>
              </SignUpButton>
            </div>
          )}
        </div>
      </div>

      {/* Hero Section */}
      <div className="hero min-h-[calc(100vh-5rem)] py-12">
        <div className="hero-content text-center">
          <div className="max-w-5xl">
            <div className="mb-8">
              <div className="badge badge-primary badge-lg mb-4">✨ AI-Powered Growth</div>
              <h1 className="text-6xl font-extrabold mb-6 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                Grow Your Social Media with AI-Powered Guidance
              </h1>
              <p className="text-xl mb-8 text-base-content/70 max-w-3xl mx-auto leading-relaxed">
                Master the art of going viral with personalized strategies based on Brendan Kane's proven methods.
                Get AI mentoring, interactive roadmaps, and content creation tools all in one place.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              {userId ? (
                <Link href="/dashboard" className="btn btn-primary btn-lg gap-2 shadow-lg">
                  <span>Continue Your Journey</span>
                  🚀
                </Link>
              ) : (
                <>
                  <SignUpButton>
                    <button className="btn btn-primary btn-lg gap-2 shadow-lg hover:shadow-xl transition-all">
                      <span>Start Growing Today</span>
                      🌟
                    </button>
                  </SignUpButton>
                  <SignInButton>
                    <button className="btn btn-outline btn-lg gap-2 hover:shadow-lg transition-all">
                      Sign In
                    </button>
                  </SignInButton>
                </>
              )}
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="card bg-base-100 shadow-2xl hover:shadow-3xl transition-all duration-300 border border-base-200 hover:border-primary/20">
                <div className="card-body text-center p-8">
                  <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center text-3xl">
                    🤖
                  </div>
                  <h3 className="card-title justify-center text-xl mb-3 text-primary">AI Mentor</h3>
                  <p className="text-base-content/70 leading-relaxed">
                    Get personalized guidance through natural conversation with your AI social media mentor.
                  </p>
                </div>
              </div>

              <div className="card bg-base-100 shadow-2xl hover:shadow-3xl transition-all duration-300 border border-base-200 hover:border-secondary/20">
                <div className="card-body text-center p-8">
                  <div className="w-16 h-16 mx-auto mb-4 bg-secondary/10 rounded-full flex items-center justify-center text-3xl">
                    🗺️
                  </div>
                  <h3 className="card-title justify-center text-xl mb-3 text-secondary">Interactive Roadmaps</h3>
                  <p className="text-base-content/70 leading-relaxed">
                    Follow step-by-step roadmaps tailored to your goals and current follower count.
                  </p>
                </div>
              </div>

              <div className="card bg-base-100 shadow-2xl hover:shadow-3xl transition-all duration-300 border border-base-200 hover:border-accent/20">
                <div className="card-body text-center p-8">
                  <div className="w-16 h-16 mx-auto mb-4 bg-accent/10 rounded-full flex items-center justify-center text-3xl">
                    🎨
                  </div>
                  <h3 className="card-title justify-center text-xl mb-3 text-accent">Viral Content Formats</h3>
                  <p className="text-base-content/70 leading-relaxed">
                    Master the 4 proven viral formats with templates and real-world examples.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
