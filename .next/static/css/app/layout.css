/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: 'Inter Fallback';src: local("Arial");ascent-override: 90.44%;descent-override: 22.52%;line-gap-override: 0.00%;size-adjust: 107.12%
}.__className_e8ce0c {font-family: 'Inter', 'Inter Fallback';font-style: normal
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
  color-scheme: light;
  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(98% 0 0);
  --color-base-300: oklch(95% 0 0);
  --color-base-content: oklch(21% 0.006 285.885);
  --color-primary: oklch(45% 0.24 277.023);
  --color-primary-content: oklch(93% 0.034 272.788);
  --color-secondary: oklch(65% 0.241 354.308);
  --color-secondary-content: oklch(94% 0.028 342.258);
  --color-accent: oklch(77% 0.152 181.912);
  --color-accent-content: oklch(38% 0.063 188.416);
  --color-neutral: oklch(14% 0.005 285.823);
  --color-neutral-content: oklch(92% 0.004 286.32);
  --color-info: oklch(74% 0.16 232.661);
  --color-info-content: oklch(29% 0.066 243.157);
  --color-success: oklch(76% 0.177 163.223);
  --color-success-content: oklch(37% 0.077 168.94);
  --color-warning: oklch(82% 0.189 84.429);
  --color-warning-content: oklch(41% 0.112 45.904);
  --color-error: oklch(71% 0.194 13.428);
  --color-error-content: oklch(27% 0.105 12.094);
  --radius-selector: 0.5rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

@media (prefers-color-scheme: dark) {

  :root {
    color-scheme: dark;
    --color-base-100: oklch(25.33% 0.016 252.42);
    --color-base-200: oklch(23.26% 0.014 253.1);
    --color-base-300: oklch(21.15% 0.012 254.09);
    --color-base-content: oklch(97.807% 0.029 256.847);
    --color-primary: oklch(58% 0.233 277.117);
    --color-primary-content: oklch(96% 0.018 272.314);
    --color-secondary: oklch(65% 0.241 354.308);
    --color-secondary-content: oklch(94% 0.028 342.258);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(74% 0.16 232.661);
    --color-info-content: oklch(29% 0.066 243.157);
    --color-success: oklch(76% 0.177 163.223);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(71% 0.194 13.428);
    --color-error-content: oklch(27% 0.105 12.094);
    --radius-selector: 0.5rem;
    --radius-field: 0.25rem;
    --radius-box: 0.5rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 1;
    --noise: 0;
  }
}

:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {
  color-scheme: light;
  --color-base-100: oklch(100% 0 0);
  --color-base-200: oklch(98% 0 0);
  --color-base-300: oklch(95% 0 0);
  --color-base-content: oklch(21% 0.006 285.885);
  --color-primary: oklch(45% 0.24 277.023);
  --color-primary-content: oklch(93% 0.034 272.788);
  --color-secondary: oklch(65% 0.241 354.308);
  --color-secondary-content: oklch(94% 0.028 342.258);
  --color-accent: oklch(77% 0.152 181.912);
  --color-accent-content: oklch(38% 0.063 188.416);
  --color-neutral: oklch(14% 0.005 285.823);
  --color-neutral-content: oklch(92% 0.004 286.32);
  --color-info: oklch(74% 0.16 232.661);
  --color-info-content: oklch(29% 0.066 243.157);
  --color-success: oklch(76% 0.177 163.223);
  --color-success-content: oklch(37% 0.077 168.94);
  --color-warning: oklch(82% 0.189 84.429);
  --color-warning-content: oklch(41% 0.112 45.904);
  --color-error: oklch(71% 0.194 13.428);
  --color-error-content: oklch(27% 0.105 12.094);
  --radius-selector: 0.5rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

:root:has(input.theme-controller[value=dark]:checked),[data-theme=dark] {
  color-scheme: dark;
  --color-base-100: oklch(25.33% 0.016 252.42);
  --color-base-200: oklch(23.26% 0.014 253.1);
  --color-base-300: oklch(21.15% 0.012 254.09);
  --color-base-content: oklch(97.807% 0.029 256.847);
  --color-primary: oklch(58% 0.233 277.117);
  --color-primary-content: oklch(96% 0.018 272.314);
  --color-secondary: oklch(65% 0.241 354.308);
  --color-secondary-content: oklch(94% 0.028 342.258);
  --color-accent: oklch(77% 0.152 181.912);
  --color-accent-content: oklch(38% 0.063 188.416);
  --color-neutral: oklch(14% 0.005 285.823);
  --color-neutral-content: oklch(92% 0.004 286.32);
  --color-info: oklch(74% 0.16 232.661);
  --color-info-content: oklch(29% 0.066 243.157);
  --color-success: oklch(76% 0.177 163.223);
  --color-success-content: oklch(37% 0.077 168.94);
  --color-warning: oklch(82% 0.189 84.429);
  --color-warning-content: oklch(41% 0.112 45.904);
  --color-error: oklch(71% 0.194 13.428);
  --color-error-content: oklch(27% 0.105 12.094);
  --radius-selector: 0.5rem;
  --radius-field: 0.25rem;
  --radius-box: 0.5rem;
  --size-selector: 0.25rem;
  --size-field: 0.25rem;
  --border: 1px;
  --depth: 1;
  --noise: 0;
}

@property --radialprogress {
  syntax: "<percentage>";
  inherits: true;
  initial-value: 0%;
}

:root {
  scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;
  --fx-noise: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E");
}

:root, [data-theme] {
  background-color: var(--root-bg, var(--color-base-100));
  color: var(--color-base-content);
}
  html {
  scroll-behavior: smooth;
}

  body {
  background-color: var(--color-base-100);
  color: var(--color-base-content);
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
@keyframes radio {

  0% {
    padding: 5px;
  }

  50% {
    padding: 3px;
  }
}
.tabs {
  display: flex;
  flex-wrap: wrap;
  --tabs-height: auto;
  --tabs-direction: row;
  --tab-height: calc(var(--size-field, 0.25rem) * 10);
  height: var(--tabs-height);
  flex-direction: var(--tabs-direction);
}
.tab {
  position: relative;
  display: inline-flex;
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  text-align: center;
  webkit-user-select: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
@media (hover: hover) {

  .tab:hover {
    color: var(--color-base-content);
  }
}
.tab {
  --tab-p: 1rem;
  --tab-bg: var(--color-base-100);
  --tab-border-color: var(--color-base-300);
  --tab-radius-ss: 0;
  --tab-radius-se: 0;
  --tab-radius-es: 0;
  --tab-radius-ee: 0;
  --tab-order: 0;
  --tab-radius-min: calc(0.75rem - var(--border));
  border-color: #0000;
  order: var(--tab-order);
  height: var(--tab-height);
  font-size: 0.875rem;
  padding-inline-start: var(--tab-p);
  padding-inline-end: var(--tab-p);
}
.tab:is(input[type="radio"]) {
  min-width: -moz-fit-content;
  min-width: fit-content;
}
.tab:is(input[type="radio"]):after {
  content: attr(aria-label);
}
.tab:is(label) {
  position: relative;
}
.tab:is(label) input {
  position: absolute;
  inset: calc(0.25rem * 0);
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  opacity: 0%;
}
.tab:checked + .tab-content, .tab:is(label:has(:checked)) + .tab-content, .tab:is(.tab-active, [aria-selected="true"]) + .tab-content {
  display: block;
  height: calc(100% - var(--tab-height) + var(--border));
}
.tab:not(:checked, label:has(:checked), :hover, .tab-active, [aria-selected="true"]) {
  color: color-mix(in oklab, var(--color-base-content) 50%, transparent);
}
.tab:not(input):empty {
  flex-grow: 1;
  cursor: default;
}
.tab:focus {
  --tw-outline-style: none;
  outline-style: none;
}
@media (forced-colors: active) {

  .tab:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }
}
.tab:focus-visible, .tab:is(label:has(:checked:focus-visible)) {
  outline: 2px solid currentColor;
  outline-offset: -5px;
}
.tab[disabled] {
  pointer-events: none;
  opacity: 40%;
}
.tabs-border .tab {
  --tab-border-color: #0000 #0000 var(--tab-border-color) #0000;
  position: relative;
  border-radius: var(--radius-field);
}
.tabs-border .tab:before {
  --tw-content: "";
  content: var(--tw-content);
  background-color: var(--tab-border-color);
  transition: background-color 0.2s ease;
  width: 80%;
  height: 3px;
  border-radius: var(--radius-field);
  bottom: 0;
  left: 10%;
  position: absolute;
}
.tabs-border .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):before, .tabs-border .tab:is(input:checked):before, .tabs-border .tab:is(label:has(:checked)):before {
  --tab-border-color: currentColor;
  border-top: 3px solid;
}
.tabs-lift > .tab {
  --tab-border: 0 0 var(--border) 0;
  --tab-radius-ss: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-se: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-es: 0;
  --tab-radius-ee: 0;
  --tab-paddings: var(--border) var(--tab-p) 0 var(--tab-p);
  --tab-border-colors: #0000 #0000 var(--tab-border-color) #0000;
  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
  --tab-corner-position: top left, top right;
  border-width: var(--tab-border);
  border-start-start-radius: var(--tab-radius-ss);
  border-start-end-radius: var(--tab-radius-se);
  border-end-start-radius: var(--tab-radius-es);
  border-end-end-radius: var(--tab-radius-ee);
  padding: var(--tab-paddings);
  border-color: var(--tab-border-colors);
}
.tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-lift > .tab:is(input:checked, label:has(:checked)) {
  --tab-border: var(--border) var(--border) 0 var(--border);
  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000
        var(--tab-border-color);
  --tab-paddings: 0 calc(var(--tab-p) - var(--border)) var(--border)
        calc(var(--tab-p) - var(--border));
  --tab-inset: auto auto 0 auto;
  --tab-grad: calc(69% - var(--border));
  --radius-start: radial-gradient(
        circle at top left,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
  --radius-end: radial-gradient(
        circle at top right,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
  background-color: var(--tab-bg);
}
.tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):before {
  z-index: 1;
  content: "";
  display: block;
  position: absolute;
  width: var(--tab-corner-width);
  height: var(--tab-corner-height);
  background-position: var(--tab-corner-position);
  background-image: var(--radius-start), var(--radius-end);
  background-size: min(var(--radius-field), var(--tab-radius-min)) min(var(--radius-field), var(--tab-radius-min));
  background-repeat: no-repeat;
  inset: var(--tab-inset);
}
.tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):first-child:before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):first-child:before {
  --radius-start: none;
}
[dir="rtl"] .tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):first-child:before, [dir="rtl"] .tabs-lift > .tab:is(input:checked, label:has(:checked)):first-child:before {
  transform: rotateY(180deg);
}
.tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):last-child:before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):last-child:before {
  --radius-end: none;
}
[dir="rtl"] .tabs-lift > .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]):last-child:before, [dir="rtl"] .tabs-lift > .tab:is(input:checked, label:has(:checked)):last-child:before {
  transform: rotateY(180deg);
}
.tabs-lift:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected="true"]) {
  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000
          var(--tab-border-color);
}
.tabs-lift :checked + .tab-content:nth-child(1), .tabs-lift :checked + .tab-content:nth-child(n + 3), .tabs-lift label:has(:checked) + .tab-content:nth-child(1), .tabs-lift label:has(:checked) + .tab-content:nth-child(n + 3), .tabs-lift :is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(1), .tabs-lift :is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(n + 3) {
  --tabcontent-radius-ss: var(--radius-box);
}
.tabs-top .tab {
  --tab-order: 0;
  --tab-border: 0 0 var(--border) 0;
  --tab-radius-ss: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-se: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-es: 0;
  --tab-radius-ee: 0;
  --tab-paddings: var(--border) var(--tab-p) 0 var(--tab-p);
  --tab-border-colors: #0000 #0000 var(--tab-border-color) #0000;
  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
  --tab-corner-position: top left, top right;
}
.tabs-top .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-top .tab:is(input:checked), .tabs-top .tab:is(label:has(:checked)) {
  --tab-border: var(--border) var(--border) 0 var(--border);
  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000
        var(--tab-border-color);
  --tab-paddings: 0 calc(var(--tab-p) - var(--border)) var(--border)
        calc(var(--tab-p) - var(--border));
  --tab-inset: auto auto 0 auto;
  --radius-start: radial-gradient(
        circle at top left,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
  --radius-end: radial-gradient(
        circle at top right,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
}
.tabs-top:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected="true"]) {
  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000
          var(--tab-border-color);
}
.tabs-top :checked + .tab-content:nth-child(1), .tabs-top :checked + .tab-content:nth-child(n + 3), .tabs-top label:has(:checked) + .tab-content:nth-child(1), .tabs-top label:has(:checked) + .tab-content:nth-child(n + 3), .tabs-top :is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(1), .tabs-top :is(.tab-active, [aria-selected="true"]) + .tab-content:nth-child(n + 3) {
  --tabcontent-radius-ss: var(--radius-box);
}
.tabs-bottom .tab {
  --tab-order: 1;
  --tab-border: var(--border) 0 0 0;
  --tab-radius-ss: 0;
  --tab-radius-se: 0;
  --tab-radius-es: min(var(--radius-field), var(--tab-radius-min));
  --tab-radius-ee: min(var(--radius-field), var(--tab-radius-min));
  --tab-border-colors: var(--tab-border-color) #0000 #0000 #0000;
  --tab-paddings: 0 var(--tab-p) var(--border) var(--tab-p);
  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);
  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));
  --tab-corner-position: top left, top right;
}
.tabs-bottom .tab:is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-bottom .tab:is(input:checked), .tabs-bottom .tab:is(label:has(:checked)) {
  --tab-border: 0 var(--border) var(--border) var(--border);
  --tab-border-colors: #0000 var(--tab-border-color) var(--tab-border-color)
        var(--tab-border-color);
  --tab-paddings: var(--border) calc(var(--tab-p) - var(--border)) 0
        calc(var(--tab-p) - var(--border));
  --tab-inset: 0 auto auto auto;
  --radius-start: radial-gradient(
        circle at bottom left,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
  --radius-end: radial-gradient(
        circle at bottom right,
        #0000 var(--tab-grad),
        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),
        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),
        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)
      );
}
.tabs-bottom:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected="true"]) {
  --tab-border-colors: #0000 var(--tab-border-color) var(--tab-border-color)
          var(--tab-border-color);
}
.tabs-bottom > :checked + .tab-content:not(:nth-child(2)), .tabs-bottom > :is(label:has(:checked)) + .tab-content:not(:nth-child(2)), .tabs-bottom > :is(.tab-active, [aria-selected="true"]) + .tab-content:not(:nth-child(2)) {
  --tabcontent-radius-es: var(--radius-box);
}
.tabs-box .tab {
  border-radius: var(--radius-field);
  border-style: none;
}
.tabs-box .tab:focus-visible, .tabs-box .tab:is(label:has(:checked:focus-visible)) {
  outline-offset: 2px;
}
.tabs-box > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-box > :is(input:checked), .tabs-box > :is(label:has(:checked)) {
  background-color: var(--tab-bg, var(--color-base-100));
  box-shadow: 0 1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px 1px -1px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 50%), #0000), 0 1px 6px -4px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 100%), #0000);
}
@media (forced-colors: active) {

  .tabs-box > :is(.tab-active, [aria-selected="true"]):not(.tab-disabled, [disabled]), .tabs-box > :is(input:checked), .tabs-box > :is(label:has(:checked)) {
    border: 1px solid;
  }
}
.tabs-xs :where(.tab) {
  font-size: 0.75rem;
  --tab-p: 0.375rem;
  --tab-radius-min: calc(0.5rem - var(--border));
}
.tabs-sm :where(.tab) {
  font-size: 0.875rem;
  --tab-p: 0.5rem;
  --tab-radius-min: calc(0.5rem - var(--border));
}
.tabs-md :where(.tab) {
  font-size: 0.875rem;
  --tab-p: 0.75rem;
  --tab-radius-min: calc(0.75rem - var(--border));
}
.tabs-lg :where(.tab) {
  font-size: 1.125rem;
  --tab-p: 1rem;
  --tab-radius-min: calc(1.5rem - var(--border));
}
.tabs-xl :where(.tab) {
  font-size: 1.125rem;
  --tab-p: 1.25rem;
  --tab-radius-min: calc(2rem - var(--border));
}
.hero {
  display: grid;
  width: 100%;
  place-items: center;
  background-size: cover;
  background-position: center;
}
.hero > * {
  grid-column-start: 1;
  grid-row-start: 1;
}
.hero-content {
  isolation: isolate;
  display: flex;
  max-width: 80rem;
  align-items: center;
  justify-content: center;
  gap: calc(0.25rem * 4);
  padding: calc(0.25rem * 4);
}
.avatar-group :where(.avatar) {
  overflow: hidden;
  border-radius: calc(infinity * 1px);
  border: 4px solid var(--color-base-100);
}
.avatar {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.avatar > div {
  display: block;
  aspect-ratio: 1 / 1;
  overflow: hidden;
}
.avatar img {
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
@keyframes skeleton {

  0% {
    background-position: 150%;
  }

  100% {
    background-position: -50%;
  }
}
.progress {
  position: relative;
  height: calc(0.25rem * 2);
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  overflow: hidden;
  border-radius: var(--radius-box);
  background-color: color-mix(in oklab, currentColor 20%, transparent);
  color: var(--color-base-content);
}
.progress:indeterminate {
  background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
  background-size: 200%;
  background-position-x: 15%;
  animation: progress 5s ease-in-out infinite;
}
@supports (-moz-appearance: none) {

  .progress:indeterminate::-moz-progress-bar {
    background-color: transparent;
    background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );
    background-size: 200%;
    background-position-x: 15%;
    animation: progress 5s ease-in-out infinite;
  }

  .progress::-moz-progress-bar {
    border-radius: var(--radius-box);
    background-color: currentColor;
  }
}
@supports (-webkit-appearance: none) {

  .progress::-webkit-progress-bar {
    border-radius: var(--radius-box);
    background-color: transparent;
  }

  .progress::-webkit-progress-value {
    border-radius: var(--radius-box);
    background-color: currentColor;
  }
}
@keyframes progress {

  50% {
    background-position-x: -115%;
  }
}
.menu :where(li) .badge {
  justify-self: flex-end;
}
:where(.btn) {
  width: unset;
}
.btn {
  display: inline-flex;
  flex-shrink: 0;
  cursor: pointer;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  gap: calc(0.25rem * 1.5);
  text-align: center;
  vertical-align: middle;
  outline-offset: 2px;
  webkit-user-select: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  padding-inline: var(--btn-p);
  color: var(--btn-fg);
  --tw-prose-links: var(--btn-fg);
  height: var(--size);
  font-size: var(--fontsize, 0.875rem);
  font-weight: 600;
  outline-color: var(--btn-color, var(--color-base-content));
  transition-property: color, background-color, border-color, box-shadow;
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 0.2s;
  border-start-start-radius: var(--join-ss, var(--radius-field));
  border-start-end-radius: var(--join-se, var(--radius-field));
  border-end-start-radius: var(--join-es, var(--radius-field));
  border-end-end-radius: var(--join-ee, var(--radius-field));
  background-color: var(--btn-bg);
  background-size: auto, calc(var(--noise) * 100%);
  background-image: none, var(--btn-noise);
  border-width: var(--border);
  border-style: solid;
  border-color: var(--btn-border);
  text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));
  touch-action: manipulation;
  box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);
  --size: calc(var(--size-field, 0.25rem) * 10);
  --btn-bg: var(--btn-color, var(--color-base-200));
  --btn-fg: var(--color-base-content);
  --btn-p: 1rem;
  --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));
  --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),
    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);
  --btn-noise: var(--fx-noise);
}
.prose .btn {
  text-decoration-line: none;
}
@media (hover: hover) {

  .btn:hover {
    --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
  }
}
.btn:focus-visible {
  outline-width: 2px;
  outline-style: solid;
  isolation: isolate;
}
.btn:active:not(.btn-active) {
  translate: 0 0.5px;
  --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);
  --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);
  --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);
}
.btn:is(:disabled, [disabled], .btn-disabled):not(.btn-link, .btn-ghost) {
  background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);
  box-shadow: none;
}
.btn:is(:disabled, [disabled], .btn-disabled) {
  pointer-events: none;
  --btn-border: #0000;
  --btn-noise: none;
  --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
}
@media (hover: hover) {

  .btn:is(:disabled, [disabled], .btn-disabled):hover {
    pointer-events: none;
    background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);
    --btn-border: #0000;
    --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);
  }
}
.btn:is(input[type="checkbox"], input[type="radio"]) {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.btn:is(input[type="checkbox"], input[type="radio"])::after {
  content: attr(aria-label);
}
.btn:where(input:checked:not(.filter .btn)) {
  --btn-color: var(--color-primary);
  --btn-fg: var(--color-primary-content);
  isolation: isolate;
}
.btn-primary {
  --btn-color: var(--color-primary);
  --btn-fg: var(--color-primary-content);
}
.btn-secondary {
  --btn-color: var(--color-secondary);
  --btn-fg: var(--color-secondary-content);
}
.btn-accent {
  --btn-color: var(--color-accent);
  --btn-fg: var(--color-accent-content);
}
.btn-error {
  --btn-color: var(--color-error);
  --btn-fg: var(--color-error-content);
}
.btn-ghost:not(.btn-active, :hover, :active:focus, :focus-visible) {
  --btn-shadow: "";
  --btn-bg: #0000;
  --btn-border: #0000;
  --btn-noise: none;
}
.btn-ghost:not(.btn-active, :hover, :active:focus, :focus-visible):not(:disabled, [disabled], .btn-disabled) {
  outline-color: currentColor;
  --btn-fg: currentColor;
}
@media (hover: none) {

  .btn-ghost:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {
    --btn-shadow: "";
    --btn-bg: #0000;
    --btn-border: #0000;
    --btn-noise: none;
    --btn-fg: currentColor;
  }
}
.btn-outline:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
  --btn-shadow: "";
  --btn-bg: #0000;
  --btn-fg: var(--btn-color);
  --btn-border: var(--btn-color);
  --btn-noise: none;
}
@media (hover: none) {

  .btn-outline:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {
    --btn-shadow: "";
    --btn-bg: #0000;
    --btn-fg: var(--btn-color);
    --btn-border: var(--btn-color);
    --btn-noise: none;
  }
}
.btn-sm {
  --fontsize: 0.75rem;
  --btn-p: 0.75rem;
  --size: calc(var(--size-field, 0.25rem) * 8);
}
.btn-lg {
  --fontsize: 1.125rem;
  --btn-p: 1.25rem;
  --size: calc(var(--size-field, 0.25rem) * 12);
}
.tooltip {
  position: relative;
  display: inline-block;
  --tt-bg: var(--color-neutral);
  --tt-off: calc(100% + 0.5rem);
  --tt-tail: calc(100% + 1px + 0.25rem);
}
.tooltip > :where(.tooltip-content), .tooltip:where([data-tip]):before {
  position: absolute;
  max-width: 20rem;
  border-radius: var(--radius-field);
  padding-inline: calc(0.25rem * 2);
  padding-block: calc(0.25rem * 1);
  text-align: center;
  white-space: normal;
  color: var(--color-neutral-content);
  opacity: 0%;
  font-size: 0.875rem;
  line-height: 1.25;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;
  background-color: var(--tt-bg);
  width: -moz-max-content;
  width: max-content;
  pointer-events: none;
  z-index: 2;
  --tw-content: attr(data-tip);
  content: var(--tw-content);
}
.tooltip:after {
  position: absolute;
  opacity: 0%;
  background-color: var(--tt-bg);
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;
  content: "";
  pointer-events: none;
  width: 0.625rem;
  height: 0.25rem;
  display: block;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  -webkit-mask-position: -1px 0;
          mask-position: -1px 0;
  --mask-tooltip: url("data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A");
  -webkit-mask-image: var(--mask-tooltip);
          mask-image: var(--mask-tooltip);
}
.tooltip.tooltip-open > .tooltip-content, .tooltip.tooltip-open[data-tip]:before, .tooltip.tooltip-open:after, .tooltip[data-tip]:not([data-tip=""]):hover > .tooltip-content, .tooltip[data-tip]:not([data-tip=""]):hover[data-tip]:before, .tooltip[data-tip]:not([data-tip=""]):hover:after, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover > .tooltip-content, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover[data-tip]:before, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover:after, .tooltip:has(:focus-visible) > .tooltip-content, .tooltip:has(:focus-visible)[data-tip]:before, .tooltip:has(:focus-visible):after {
  opacity: 100%;
  --tt-pos: 0rem;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0ms;
}
.tooltip > .tooltip-content, .tooltip[data-tip]:before, .tooltip-top > .tooltip-content, .tooltip-top[data-tip]:before {
  transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));
  inset: auto auto var(--tt-off) 50%;
}
.tooltip:after, .tooltip-top:after {
  transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));
  inset: auto auto var(--tt-tail) 50%;
}
.label:is(.\!input > *, .select > *) {
  display: flex !important;
  height: calc(100% - 0.5rem) !important;
  align-items: center !important;
  padding-inline: calc(0.25rem * 3) !important;
  white-space: nowrap !important;
  font-size: inherit !important;
}
.label:is(.input > *, .select > *) {
  display: flex;
  height: calc(100% - 0.5rem);
  align-items: center;
  padding-inline: calc(0.25rem * 3);
  white-space: nowrap;
  font-size: inherit;
}
.label:is(.\!input > *, .select > *):first-child {
  margin-inline-start: calc(0.25rem * -3) !important;
  margin-inline-end: calc(0.25rem * 3) !important;
  border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000) !important;
}
.label:is(.input > *, .select > *):first-child {
  margin-inline-start: calc(0.25rem * -3);
  margin-inline-end: calc(0.25rem * 3);
  border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
}
.label:is(.\!input > *, .select > *):last-child {
  margin-inline-start: calc(0.25rem * 3) !important;
  margin-inline-end: calc(0.25rem * -3) !important;
  border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000) !important;
}
.label:is(.input > *, .select > *):last-child {
  margin-inline-start: calc(0.25rem * 3);
  margin-inline-end: calc(0.25rem * -3);
  border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);
}
.navbar {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 0.5rem;
  min-height: 4rem;
}
.navbar-start {
  display: inline-flex;
  align-items: center;
  width: 50%;
  justify-content: flex-start;
}
.navbar-center {
  display: inline-flex;
  align-items: center;
  flex-shrink: 0;
}
.navbar-end {
  display: inline-flex;
  align-items: center;
  width: 50%;
  justify-content: flex-end;
}
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: var(--radius-box);
  outline-width: 2px;
  transition: outline 0.2s ease-in-out;
  outline: 0 solid #0000;
  outline-offset: 2px;
}
.card:focus {
  --tw-outline-style: none;
  outline-style: none;
}
@media (forced-colors: active) {

  .card:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }
}
.card:focus-visible {
  outline-color: currentColor;
}
.card :where(figure:first-child) {
  overflow: hidden;
  border-start-start-radius: inherit;
  border-start-end-radius: inherit;
  border-end-start-radius: unset;
  border-end-end-radius: unset;
}
.card :where(figure:last-child) {
  overflow: hidden;
  border-start-start-radius: unset;
  border-start-end-radius: unset;
  border-end-start-radius: inherit;
  border-end-end-radius: inherit;
}
.card:where(.card-border) {
  border: var(--border) solid var(--color-base-200);
}
.card:where(.card-dash) {
  border: var(--border) dashed var(--color-base-200);
}
.card.image-full {
  display: grid;
}
.card.image-full > * {
  grid-column-start: 1;
  grid-row-start: 1;
}
.card.image-full > .card-body {
  position: relative;
  color: var(--color-neutral-content);
}
.card.image-full :where(figure) {
  overflow: hidden;
  border-radius: inherit;
}
.card.image-full > figure img {
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  filter: brightness(28%);
}
.card figure {
  display: flex;
  align-items: center;
  justify-content: center;
}
.card:has(> input:is(input[type="checkbox"], input[type="radio"])) {
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.card:has(> :checked) {
  outline: 2px solid currentColor;
}
.card-title {
  display: flex;
  align-items: center;
  gap: calc(0.25rem * 2);
  font-size: var(--cardtitle-fs, 1.125rem);
  font-weight: 600;
}
.card-body {
  display: flex;
  flex: auto;
  flex-direction: column;
  gap: calc(0.25rem * 2);
  padding: var(--card-p, 1.5rem);
  font-size: var(--card-fs, 0.875rem);
}
.card-body :where(p) {
  flex-grow: 1;
}
.card-xs .card-body {
  --card-p: 0.5rem;
  --card-fs: 0.6875rem;
}
.card-xs .card-title {
  --cardtitle-fs: 0.875rem;
}
.card-sm .card-body {
  --card-p: 1rem;
  --card-fs: 0.75rem;
}
.card-sm .card-title {
  --cardtitle-fs: 1rem;
}
.card-md .card-body {
  --card-p: 1.5rem;
  --card-fs: 0.875rem;
}
.card-md .card-title {
  --cardtitle-fs: 1.125rem;
}
.card-lg .card-body {
  --card-p: 2rem;
  --card-fs: 1rem;
}
.card-lg .card-title {
  --cardtitle-fs: 1.25rem;
}
.card-xl .card-body {
  --card-p: 2.5rem;
  --card-fs: 1.125rem;
}
.card-xl .card-title {
  --cardtitle-fs: 1.375rem;
}
@keyframes toast {

  0% {
    scale: 0.9;
    opacity: 0;
  }

  100% {
    scale: 1;
    opacity: 1;
  }
}
.status {
  display: inline-block;
  aspect-ratio: 1 / 1;
  width: calc(0.25rem * 2);
  height: calc(0.25rem * 2);
  border-radius: var(--radius-selector);
  background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: middle;
  color: color-mix(in srgb, #000 30%, transparent);
}
@supports (color: color-mix(in lab, red, red)) {

  .status {
    color: color-mix(in oklab, var(--color-black) 30%, transparent);
  }
}
.status {
  background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 );
  box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);
}
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: calc(0.25rem * 2);
  border-radius: var(--radius-selector);
  vertical-align: middle;
  color: var(--badge-fg);
  border: var(--border) solid var(--badge-color, var(--color-base-200));
  font-size: 0.875rem;
  width: -moz-fit-content;
  width: fit-content;
  padding-inline: calc(0.25rem * 3 - var(--border));
  background-size: auto, calc(var(--noise) * 100%);
  background-image: none, var(--fx-noise);
  background-color: var(--badge-bg);
  --badge-bg: var(--badge-color, var(--color-base-100));
  --badge-fg: var(--color-base-content);
  --size: calc(var(--size-selector, 0.25rem) * 6);
  height: var(--size);
}
.badge-primary {
  --badge-color: var(--color-primary);
  --badge-fg: var(--color-primary-content);
}
.badge-ghost {
  border-color: var(--color-base-200);
  background-color: var(--color-base-200);
  color: var(--color-base-content);
  background-image: none;
}
.badge-sm {
  --size: calc(var(--size-selector, 0.25rem) * 5);
  font-size: 0.75rem;
  padding-inline: calc(0.25rem * 2.5 - var(--border));
}
.badge-lg {
  --size: calc(var(--size-selector, 0.25rem) * 7);
  font-size: 1rem;
  padding-inline: calc(0.25rem * 3.5 - var(--border));
}
.mockup-browser .mockup-browser-toolbar .\!input {
  margin-inline: auto !important;
  display: flex !important;
  height: 100% !important;
  align-items: center !important;
  gap: calc(0.25rem * 2) !important;
  overflow: hidden !important;
  background-color: var(--color-base-200) !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  font-size: 0.75rem !important;
  direction: ltr !important;
}
.mockup-browser .mockup-browser-toolbar .input {
  margin-inline: auto;
  display: flex;
  height: 100%;
  align-items: center;
  gap: calc(0.25rem * 2);
  overflow: hidden;
  background-color: var(--color-base-200);
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.75rem;
  direction: ltr;
}
.mockup-browser .mockup-browser-toolbar .\!input:before {
  content: "" !important;
  width: calc(0.25rem * 4) !important;
  height: calc(0.25rem * 4) !important;
  opacity: 30% !important;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='currentColor' class='size-4'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' clip-rule='evenodd' /%3E%3C/svg%3E%0A") !important;
}
.mockup-browser .mockup-browser-toolbar .input:before {
  content: "";
  width: calc(0.25rem * 4);
  height: calc(0.25rem * 4);
  opacity: 30%;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='currentColor' class='size-4'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' clip-rule='evenodd' /%3E%3C/svg%3E%0A");
}
@keyframes dropdown {

  0% {
    opacity: 0;
  }
}
.\!input {
  cursor: text !important;
  border: var(--border) solid #0000 !important;
  position: relative !important;
  display: inline-flex !important;
  flex-shrink: 1 !important;
  -webkit-appearance: none !important;
     -moz-appearance: none !important;
          appearance: none !important;
  align-items: center !important;
  gap: calc(0.25rem * 2) !important;
  background-color: var(--color-base-100) !important;
  padding-inline: calc(0.25rem * 3) !important;
  vertical-align: middle !important;
  white-space: nowrap !important;
  width: clamp(3rem, 20rem, 100%) !important;
  height: var(--size) !important;
  font-size: 0.875rem !important;
  touch-action: manipulation !important;
  border-start-start-radius: var(--join-ss, var(--radius-field)) !important;
  border-start-end-radius: var(--join-se, var(--radius-field)) !important;
  border-end-start-radius: var(--join-es, var(--radius-field)) !important;
  border-end-end-radius: var(--join-ee, var(--radius-field)) !important;
  border-color: var(--input-color) !important;
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset !important;
  --size: calc(var(--size-field, 0.25rem) * 10) !important;
  --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000) !important;
}
.input {
  cursor: text;
  border: var(--border) solid #0000;
  position: relative;
  display: inline-flex;
  flex-shrink: 1;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  align-items: center;
  gap: calc(0.25rem * 2);
  background-color: var(--color-base-100);
  padding-inline: calc(0.25rem * 3);
  vertical-align: middle;
  white-space: nowrap;
  width: clamp(3rem, 20rem, 100%);
  height: var(--size);
  font-size: 0.875rem;
  touch-action: manipulation;
  border-start-start-radius: var(--join-ss, var(--radius-field));
  border-start-end-radius: var(--join-se, var(--radius-field));
  border-end-start-radius: var(--join-es, var(--radius-field));
  border-end-end-radius: var(--join-ee, var(--radius-field));
  border-color: var(--input-color);
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;
  --size: calc(var(--size-field, 0.25rem) * 10);
  --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);
}
.\!input:where(input) {
  display: inline-flex !important;
}
.input:where(input) {
  display: inline-flex;
}
.\!input :where(input) {
  display: inline-flex !important;
  height: 100% !important;
  width: 100% !important;
  -webkit-appearance: none !important;
     -moz-appearance: none !important;
          appearance: none !important;
  background-color: transparent !important;
  border: none !important;
}
.input :where(input) {
  display: inline-flex;
  height: 100%;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: transparent;
  border: none;
}
.\!input :where(input):focus, .\!input :where(input):focus-within {
  --tw-outline-style: none !important;
  outline-style: none !important;
}
.input :where(input):focus, .input :where(input):focus-within {
  --tw-outline-style: none;
  outline-style: none;
}
.\!input :where(input):focus, .\!input :where(input):focus-within {
  --tw-outline-style: none !important;
  outline-style: none !important;
}
@media (forced-colors: active) {

  .\!input :where(input):focus, .\!input :where(input):focus-within {
    outline: 2px solid transparent !important;
    outline-offset: 2px !important;
  }

  .input :where(input):focus, .input :where(input):focus-within {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  .\!input :where(input):focus, .\!input :where(input):focus-within {
    outline: 2px solid transparent !important;
    outline-offset: 2px !important;
  }
}
.\!input :where(input[type="url"]), .\!input :where(input[type="email"]) {
  direction: ltr !important;
}
.input :where(input[type="url"]), .input :where(input[type="email"]) {
  direction: ltr;
}
.\!input :where(input[type="url"]), .\!input :where(input[type="email"]) {
  direction: ltr !important;
}
.\!input :where(input[type="date"]) {
  display: inline-block !important;
}
.input :where(input[type="date"]) {
  display: inline-block;
}
.\!input:focus, .\!input:focus-within {
  --input-color: var(--color-base-content) !important;
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) !important;
  outline: 2px solid var(--input-color) !important;
  outline-offset: 2px !important;
  isolation: isolate !important;
  z-index: 1 !important;
}
.input:focus, .input:focus-within {
  --input-color: var(--color-base-content);
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);
  outline: 2px solid var(--input-color);
  outline-offset: 2px;
  isolation: isolate;
  z-index: 1;
}
.\!input:focus, .\!input:focus-within {
  --input-color: var(--color-base-content) !important;
  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) !important;
  outline: 2px solid var(--input-color) !important;
  outline-offset: 2px !important;
  isolation: isolate !important;
  z-index: 1 !important;
}
.\!input:has(> input[disabled]), .\!input:is(:disabled, [disabled]) {
  cursor: not-allowed !important;
  border-color: var(--color-base-200) !important;
  background-color: var(--color-base-200) !important;
  color: color-mix(in oklab, var(--color-base-content) 40%, transparent) !important;
}
.input:has(> input[disabled]), .input:is(:disabled, [disabled]) {
  cursor: not-allowed;
  border-color: var(--color-base-200);
  background-color: var(--color-base-200);
  color: color-mix(in oklab, var(--color-base-content) 40%, transparent);
}
.\!input:has(> input[disabled]), .\!input:is(:disabled, [disabled]) {
  cursor: not-allowed !important;
  border-color: var(--color-base-200) !important;
  background-color: var(--color-base-200) !important;
  color: color-mix(in oklab, var(--color-base-content) 40%, transparent) !important;
}
.\!input:has(> input[disabled])::-moz-placeholder, .\!input:is(:disabled, [disabled])::-moz-placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
}
.\!input:has(> input[disabled])::placeholder, .\!input:is(:disabled, [disabled])::placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
}
.input:has(> input[disabled])::-moz-placeholder, .input:is(:disabled, [disabled])::-moz-placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
}
.input:has(> input[disabled])::placeholder, .input:is(:disabled, [disabled])::placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent);
}
.\!input:has(> input[disabled])::-moz-placeholder, .\!input:is(:disabled, [disabled])::-moz-placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
}
.\!input:has(> input[disabled])::placeholder, .\!input:is(:disabled, [disabled])::placeholder {
  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;
}
.\!input:has(> input[disabled]), .\!input:is(:disabled, [disabled]) {
  box-shadow: none !important;
}
.input:has(> input[disabled]), .input:is(:disabled, [disabled]) {
  box-shadow: none;
}
.\!input:has(> input[disabled]), .\!input:is(:disabled, [disabled]) {
  box-shadow: none !important;
}
.\!input:has(> input[disabled]) > input[disabled] {
  cursor: not-allowed !important;
}
.input:has(> input[disabled]) > input[disabled] {
  cursor: not-allowed;
}
.\!input::-webkit-date-and-time-value {
  text-align: inherit !important;
}
.input::-webkit-date-and-time-value {
  text-align: inherit;
}
.\!input[type="number"]::-webkit-inner-spin-button {
  margin-block: calc(0.25rem * -3) !important;
  margin-inline-end: calc(0.25rem * -3) !important;
}
.input[type="number"]::-webkit-inner-spin-button {
  margin-block: calc(0.25rem * -3);
  margin-inline-end: calc(0.25rem * -3);
}
.\!input::-webkit-calendar-picker-indicator {
  position: absolute !important;
  inset-inline-end: 0.75em !important;
}
.input::-webkit-calendar-picker-indicator {
  position: absolute;
  inset-inline-end: 0.75em;
}
.chat {
  display: grid;
  -moz-column-gap: calc(0.25rem * 3);
       column-gap: calc(0.25rem * 3);
  padding-block: calc(0.25rem * 1);
  --mask-chat: url("data:image/svg+xml,%3csvg width='13' height='13' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 11.5004C0 13.0004 2 13.0004 2 13.0004H12H13V0.00036329L12.5 0C12.5 0 11.977 2.09572 11.8581 2.50033C11.6075 3.35237 10.9149 4.22374 9 5.50036C6 7.50036 0 10.0004 0 11.5004Z'/%3e%3c/svg%3e");
}
.chat-bubble {
  position: relative;
  display: block;
  width: -moz-fit-content;
  width: fit-content;
  border-radius: var(--radius-field);
  background-color: var(--color-base-300);
  padding-inline: calc(0.25rem * 4);
  padding-block: calc(0.25rem * 2);
  color: var(--color-base-content);
  grid-row-end: 3;
  min-height: 2rem;
  min-width: 2.5rem;
  max-width: 90%;
}
.chat-bubble:before {
  position: absolute;
  bottom: calc(0.25rem * 0);
  height: calc(0.25rem * 3);
  width: calc(0.25rem * 3);
  background-color: inherit;
  content: "";
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  -webkit-mask-image: var(--mask-chat);
          mask-image: var(--mask-chat);
  -webkit-mask-position: 0px -1px;
          mask-position: 0px -1px;
  -webkit-mask-size: 13px;
          mask-size: 13px;
}
.chat-bubble-primary {
  background-color: var(--color-primary);
  color: var(--color-primary-content);
}
.chat-bubble-accent {
  background-color: var(--color-accent);
  color: var(--color-accent-content);
}
.chat-image {
  grid-row: span 2 / span 2;
  align-self: flex-end;
}
.chat-footer {
  grid-row-start: 3;
  display: flex;
  gap: calc(0.25rem * 1);
  font-size: 0.6875rem;
}
.chat-start {
  place-items: start;
  grid-template-columns: auto 1fr;
}
.chat-start .chat-header {
  grid-column-start: 2;
}
.chat-start .chat-footer {
  grid-column-start: 2;
}
.chat-start .chat-image {
  grid-column-start: 1;
}
.chat-start .chat-bubble {
  grid-column-start: 2;
  border-end-start-radius: 0;
}
.chat-start .chat-bubble:before {
  transform: rotateY(0deg);
  inset-inline-start: -0.75rem;
}
[dir="rtl"] .chat-start .chat-bubble:before {
  transform: rotateY(180deg);
}
.chat-end {
  place-items: end;
  grid-template-columns: 1fr auto;
}
.chat-end .chat-header {
  grid-column-start: 1;
}
.chat-end .chat-footer {
  grid-column-start: 1;
}
.chat-end .chat-image {
  grid-column-start: 2;
}
.chat-end .chat-bubble {
  grid-column-start: 1;
  border-end-end-radius: 0;
}
.chat-end .chat-bubble:before {
  transform: rotateY(180deg);
  inset-inline-start: 100%;
}
[dir="rtl"] .chat-end .chat-bubble:before {
  transform: rotateY(0deg);
}
@keyframes rating {

  0%, 40% {
    scale: 1.1;
    filter: brightness(1.05) contrast(1.05);
  }
}
.stats {
  position: relative;
  display: inline-grid;
  grid-auto-flow: column;
  overflow-x: auto;
  border-radius: var(--radius-box);
}
.stat {
  display: inline-grid;
  width: 100%;
  -moz-column-gap: calc(0.25rem * 4);
       column-gap: calc(0.25rem * 4);
  padding-inline: calc(0.25rem * 6);
  padding-block: calc(0.25rem * 4);
  grid-template-columns: repeat(1, 1fr);
}
.stat:not(:last-child) {
  border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
  border-block-end: none;
}
.stat-figure {
  grid-column-start: 2;
  grid-row: span 3 / span 3;
  grid-row-start: 1;
  place-self: center;
  justify-self: flex-end;
}
.stat-title {
  grid-column-start: 1;
  white-space: nowrap;
  color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
  font-size: 0.75rem;
}
.stat-value {
  grid-column-start: 1;
  white-space: nowrap;
  font-size: 2rem;
  font-weight: 800;
}
.stat-desc {
  grid-column-start: 1;
  white-space: nowrap;
  color: color-mix(in oklab, var(--color-base-content) 60%, transparent);
  font-size: 0.75rem;
}
.stats-horizontal .stat:not(:last-child) {
  border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
  border-block-end: none;
}
.stats-vertical {
  grid-auto-flow: row;
  overflow-y: auto;
}
.stats-vertical .stat:not(:last-child) {
  border-inline-end: none;
  border-block-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.grid {
  display: grid;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-16 {
  height: 4rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-8 {
  height: 2rem;
}
.h-\[calc\(100vh-5rem\)\] {
  height: calc(100vh - 5rem);
}
.h-full {
  height: 100%;
}
.min-h-\[calc\(100vh-5rem\)\] {
  min-height: calc(100vh - 5rem);
}
.min-h-screen {
  min-height: 100vh;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-4 {
  width: 1rem;
}
.w-8 {
  width: 2rem;
}
.w-96 {
  width: 24rem;
}
.w-full {
  width: 100%;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-lg {
  max-width: 32rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.grow {
  flex-grow: 1;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.cursor-pointer {
  cursor: pointer;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-8 {
  gap: 2rem;
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.overflow-y-auto {
  overflow-y: auto;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-t {
  border-top-width: 1px;
}
.border-base-200 {
  border-color: var(--color-base-200);
}
.border-base-300 {
  border-color: var(--color-base-300);
}
.border-current {
  border-color: currentColor;
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-base-100 {
  background-color: var(--color-base-100);
}
.bg-base-200 {
  background-color: var(--color-base-200);
}
.bg-current {
  background-color: currentColor;
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.from-accent {
  --tw-gradient-from: var(--color-accent) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-base-100 {
  --tw-gradient-from: var(--color-base-100) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-base-200 {
  --tw-gradient-from: var(--color-base-200) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary {
  --tw-gradient-from: var(--color-primary) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.to-base-100 {
  --tw-gradient-to: var(--color-base-100) var(--tw-gradient-to-position);
}
.to-base-200 {
  --tw-gradient-to: var(--color-base-200) var(--tw-gradient-to-position);
}
.to-base-300 {
  --tw-gradient-to: var(--color-base-300) var(--tw-gradient-to-position);
}
.to-secondary {
  --tw-gradient-to: var(--color-secondary) var(--tw-gradient-to-position);
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.p-3 {
  padding: 0.75rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.text-center {
  text-align: center;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-medium {
  font-weight: 500;
}
.leading-relaxed {
  line-height: 1.625;
}
.text-accent {
  color: var(--color-accent);
}
.text-primary {
  color: var(--color-primary);
}
.text-secondary {
  color: var(--color-secondary);
}
.text-transparent {
  color: transparent;
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.opacity-50 {
  opacity: 0.5;
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner {
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-300 {
  transition-duration: 300ms;
}

/* Custom styles for Influtify */
.chat-container {
  height: calc(100vh - 4rem);
}

.artifacts-panel {
  height: calc(100vh - 4rem);
  overflow-y: auto;
}

.message-bubble {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.typing-indicator {
  animation: pulse 1.5s ease-in-out infinite;
}

.achievement-celebration {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.hover\:btn-primary:hover {
  --btn-color: var(--color-primary);
  --btn-fg: var(--color-primary-content);
}

.hover\:btn-secondary:hover {
  --btn-color: var(--color-secondary);
  --btn-fg: var(--color-secondary-content);
}

.hover\:btn-accent:hover {
  --btn-color: var(--color-accent);
  --btn-fg: var(--color-accent-content);
}

.hover\:badge-primary:hover {
  --badge-color: var(--color-primary);
  --badge-fg: var(--color-primary-content);
}

.hover\:badge-secondary:hover {
  --badge-color: var(--color-secondary);
  --badge-fg: var(--color-secondary-content);
}

.hover\:badge-accent:hover {
  --badge-color: var(--color-accent);
  --badge-fg: var(--color-accent-content);
}

.hover\:text-accent:hover {
  color: var(--color-accent);
}

.hover\:text-secondary:hover {
  color: var(--color-secondary);
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:shadow-md:focus {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (min-width: 640px) {

  .sm\:flex-row {
    flex-direction: row;
  }
}

@media (min-width: 768px) {

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

