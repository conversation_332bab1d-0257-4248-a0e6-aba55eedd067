const CHUNK_PUBLIC_PATH = "server/app/api/webhooks/clerk/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_576766bb._.js");
runtime.loadChunk("server/chunks/node_modules_svix_dist_4a4d1a5c._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_drizzle-orm_a24f12f3._.js");
runtime.loadChunk("server/chunks/node_modules_36c3b71e._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__05dc4662._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/webhooks/clerk/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/webhooks/clerk/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/webhooks/clerk/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
