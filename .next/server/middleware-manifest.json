{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_351a010a._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_466025e5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z+L0XyhGbxGIFnFFqhDzP8pp2/xpDkdK34gpGGdrve4=", "__NEXT_PREVIEW_MODE_ID": "cafac64b6136fbb1b349ca3689df8e60", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f085c8decb605dd8ef5d273132330d3fd2a554ecb5aa8a419332c228fabb333b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "eefbf36b6d751eccc0ae14de7165cd62b74404bd63428e3385b760c44705dccd"}}}, "sortedMiddleware": ["/"], "functions": {}}