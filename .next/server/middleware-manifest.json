{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_351a010a._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_466025e5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "yWXTVRnPejRJaZdiuSfKs/mOHwU6vBt0/hisfirdddo=", "__NEXT_PREVIEW_MODE_ID": "a58c1ab0e230307f6fdfdc462c1c8127", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9b2cd5ab83efda00ab089cca71f2b2f3a995879fc75dc89a1f6b803850112c80", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b07d788f7a0b30864f50daafd0f4cfe049b26f0f33d2ffa84a62298b847f23d1"}}}, "sortedMiddleware": ["/"], "functions": {}}