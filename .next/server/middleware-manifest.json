{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_351a010a._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_466025e5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z+L0XyhGbxGIFnFFqhDzP8pp2/xpDkdK34gpGGdrve4=", "__NEXT_PREVIEW_MODE_ID": "9b2b93c9d4b7c7cfef7538c479b7a19a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fa893ad7e7206fb948e20f923833a1070696e517d0650b6e49a0e277251a2749", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cf986888a54b0bfdb7c1d4082b0cd179011944954399a50eadde5ff94cbd2582"}}}, "sortedMiddleware": ["/"], "functions": {}}