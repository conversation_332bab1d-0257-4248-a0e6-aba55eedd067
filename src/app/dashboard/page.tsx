import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { UserButton } from '@clerk/nextjs';
import Link from 'next/link';

export default async function DashboardPage() {
  const { userId } = await auth();
  
  if (!userId) {
    redirect('/');
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-base-200 to-base-300">
      {/* Navigation */}
      <div className="navbar bg-base-100/90 backdrop-blur-md shadow-lg border-b border-base-300">
        <div className="navbar-start">
          <Link href="/" className="btn btn-ghost text-xl font-bold text-primary hover:text-primary-focus">
            🚀 Influtify
          </Link>
        </div>
        <div className="navbar-center">
          <div className="tabs tabs-boxed bg-base-200 shadow-inner">
            <a className="tab tab-active text-primary font-medium">💬 Chat</a>
            <a className="tab hover:text-secondary">🗺️ Roadmap</a>
            <a className="tab hover:text-accent">📊 Progress</a>
          </div>
        </div>
        <div className="navbar-end">
          <UserButton />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-5rem)]">
        {/* Chat Panel */}
        <div className="flex-1 flex flex-col bg-base-100 shadow-lg">
          <div className="bg-gradient-to-r from-primary/5 to-secondary/5 p-6 border-b border-base-200">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center text-2xl">
                🤖
              </div>
              <div>
                <h2 className="text-2xl font-bold text-primary">AI Mentor Chat</h2>
                <p className="text-sm text-base-content/60">Ask me anything about growing your social media presence</p>
              </div>
            </div>
          </div>

          {/* Chat Messages Area */}
          <div className="flex-1 p-6 overflow-y-auto bg-gradient-to-b from-base-50 to-base-100">
            <div className="space-y-6 max-w-4xl">
              {/* Welcome Message */}
              <div className="chat chat-start">
                <div className="chat-image avatar">
                  <div className="w-12 rounded-full bg-gradient-to-br from-primary to-secondary text-white flex items-center justify-center shadow-lg">
                    🤖
                  </div>
                </div>
                <div className="chat-bubble chat-bubble-primary shadow-lg max-w-lg">
                  <div className="font-medium mb-2">Welcome to Influtify! 🚀</div>
                  I'm your AI social media growth mentor. I'll help you build your presence using proven viral strategies from Brendan Kane's methods.
                  <br /><br />
                  <div className="bg-primary/10 rounded-lg p-3 mt-3">
                    <div className="font-medium text-primary-content/90 mb-2">To get started, tell me:</div>
                    <div className="space-y-1 text-sm">
                      <div>• What platform do you want to focus on?</div>
                      <div>• What's your current follower count?</div>
                      <div>• What type of content do you create?</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Chat Input */}
          <div className="bg-base-100 p-6 border-t border-base-200">
            <div className="flex gap-3">
              <input
                type="text"
                placeholder="Type your message..."
                className="input input-bordered flex-1 shadow-sm focus:shadow-md transition-shadow"
                disabled
              />
              <button className="btn btn-primary shadow-lg hover:shadow-xl transition-all gap-2" disabled>
                <span>Send</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </div>
            <div className="flex items-center gap-2 mt-3">
              <div className="badge badge-warning badge-sm">⚠️</div>
              <p className="text-xs text-base-content/50">Chat functionality coming soon...</p>
            </div>
          </div>
        </div>

        {/* Artifacts Panel */}
        <div className="w-96 bg-base-100 border-l border-base-200 shadow-xl">
          <div className="bg-gradient-to-r from-secondary/5 to-accent/5 p-6 border-b border-base-200">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-secondary/10 rounded-full flex items-center justify-center text-xl">
                ⚡
              </div>
              <div>
                <h3 className="text-xl font-bold text-secondary">Interactive Tools</h3>
                <p className="text-sm text-base-content/60">Roadmaps, progress tracking, and more</p>
              </div>
            </div>
          </div>

          <div className="p-6 space-y-6 overflow-y-auto h-full">
            {/* Quick Stats */}
            <div className="stats stats-vertical shadow-lg w-full bg-gradient-to-br from-base-100 to-base-200">
              <div className="stat bg-gradient-to-br from-primary/5 to-primary/10">
                <div className="stat-figure text-primary">
                  <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
                    🏆
                  </div>
                </div>
                <div className="stat-title text-primary/70">Level</div>
                <div className="stat-value text-primary text-2xl">1</div>
                <div className="stat-desc text-primary/60">Beginner</div>
              </div>

              <div className="stat bg-gradient-to-br from-secondary/5 to-secondary/10">
                <div className="stat-figure text-secondary">
                  <div className="w-8 h-8 rounded-full bg-secondary/20 flex items-center justify-center">
                    ⭐
                  </div>
                </div>
                <div className="stat-title text-secondary/70">XP Points</div>
                <div className="stat-value text-secondary text-2xl">0</div>
                <div className="stat-desc text-secondary/60">Start your journey!</div>
              </div>

              <div className="stat bg-gradient-to-br from-accent/5 to-accent/10">
                <div className="stat-figure text-accent">
                  <div className="w-8 h-8 rounded-full bg-accent/20 flex items-center justify-center">
                    🔥
                  </div>
                </div>
                <div className="stat-title text-accent/70">Streak</div>
                <div className="stat-value text-accent text-2xl">0</div>
                <div className="stat-desc text-accent/60">Keep going!</div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="card bg-gradient-to-br from-base-200 to-base-300 shadow-lg border border-base-300">
              <div className="card-body p-5">
                <h4 className="card-title text-base text-primary mb-4 flex items-center gap-2">
                  <span>🚀</span>
                  Quick Start
                </h4>
                <div className="space-y-3">
                  <button className="btn btn-sm btn-outline btn-primary w-full hover:btn-primary hover:text-white transition-all gap-2">
                    <span>📊</span>
                    Set Goals
                  </button>
                  <button className="btn btn-sm btn-outline btn-secondary w-full hover:btn-secondary hover:text-white transition-all gap-2">
                    <span>🗺️</span>
                    View Roadmap
                  </button>
                  <button className="btn btn-sm btn-outline btn-accent w-full hover:btn-accent hover:text-white transition-all gap-2">
                    <span>📚</span>
                    Browse Content Blocks
                  </button>
                </div>
              </div>
            </div>

            {/* Recent Achievements */}
            <div className="card bg-gradient-to-br from-base-200 to-base-300 shadow-lg border border-base-300">
              <div className="card-body p-5">
                <h4 className="card-title text-base text-secondary mb-3 flex items-center gap-2">
                  <span>🏆</span>
                  Achievements
                </h4>
                <p className="text-xs text-base-content/60 mb-4">Complete actions to unlock badges!</p>
                <div className="grid grid-cols-3 gap-3">
                  <div className="tooltip" data-tip="First Steps">
                    <div className="badge badge-ghost p-3 hover:badge-primary transition-all cursor-pointer">🏆</div>
                  </div>
                  <div className="tooltip" data-tip="Rising Star">
                    <div className="badge badge-ghost p-3 hover:badge-secondary transition-all cursor-pointer">⭐</div>
                  </div>
                  <div className="tooltip" data-tip="Growth Rocket">
                    <div className="badge badge-ghost p-3 hover:badge-accent transition-all cursor-pointer">🚀</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
