# Influtify AI Chat Interface System Prompt

## Feature Overview
Create a conversational AI chat interface that replaces traditional onboarding with natural conversation. The system should extract user information progressively, render interactive artifacts, and provide personalized guidance through chat interactions. This is the core feature that makes Influtify unique - a chat-first approach to social media growth mentoring.

## Technical Requirements
- Implement using NextJS with the sliced-feature architecture
- Use Zod for data validation
- Use Drizzle ORM for database operations
- Implement UI with DaisyUI components
- Support multilanguage with next-intl
- Integrate with the hybrid AI provider system
- Real-time chat updates and typing indicators

## Database Schema
Extend the Drizzle schema with the following tables:

```typescript
// Conversations Schema
export const conversations = pgTable('conversations', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  
  // Conversation Information
  title: text('title'),
  status: text('status').notNull().default('active'), // active, completed, archived
  
  // Extracted Information
  extractedData: json('extracted_data').$type<{
    platform?: string;
    currentFollowers?: number;
    targetFollowers?: number;
    timeframe?: number;
    niche?: string;
    goals?: string[];
    experienceLevel?: string;
    contentTypes?: string[];
    [key: string]: any;
  }>(),
  
  // Conversation State
  currentStep: text('current_step'), // information_gathering, goal_setting, roadmap_creation, mentoring
  completionPercentage: integer('completion_percentage').default(0),
});

// Messages Schema
export const messages = pgTable('messages', {
  id: serial('id').primaryKey(),
  conversationId: integer('conversation_id').references(() => conversations.id).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Message Information
  role: text('role').notNull(), // user, assistant, system
  content: text('content').notNull(),
  
  // AI Function Calls
  functionCall: json('function_call').$type<{
    name: string;
    arguments: any;
  }>(),
  
  // Artifacts
  artifacts: json('artifacts').$type<{
    id: string;
    type: string;
    data: any;
  }[]>(),
  
  // Metadata
  metadata: json('metadata'),
});

// Artifacts Schema
export const artifacts = pgTable('artifacts', {
  id: serial('id').primaryKey(),
  conversationId: integer('conversation_id').references(() => conversations.id).notNull(),
  messageId: integer('message_id').references(() => messages.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  
  // Artifact Information
  type: text('type').notNull(), // goal_selector, progress_chart, roadmap_preview, content_creator
  title: text('title').notNull(),
  
  // Artifact Data
  data: json('data').notNull(),
  
  // State
  isActive: boolean('is_active').default(true),
  userInteractions: json('user_interactions').$type<any[]>(),
});
```

## AI Chat Service
Implement the core AI chat service:

```typescript
export class AIChatService {
  private aiProvider: AIProviderConfig;
  
  constructor() {
    this.aiProvider = getAIProvider();
  }
  
  // Process user message and generate response
  async processMessage(
    conversationId: number,
    userMessage: string
  ): Promise<{
    response: string;
    artifacts?: ArtifactData[];
    extractedData?: any;
  }> {
    // Get conversation context
    const conversation = await this.getConversation(conversationId);
    const messageHistory = await this.getMessageHistory(conversationId);
    
    // Create AI prompt with context
    const prompt = this.createContextualPrompt(
      conversation,
      messageHistory,
      userMessage
    );
    
    // Get AI response with function calling
    const aiResponse = await this.callAI(prompt);
    
    // Process function calls for data extraction
    const extractedData = await this.processFunctionCalls(aiResponse.functionCalls);
    
    // Generate artifacts if needed
    const artifacts = await this.generateArtifacts(
      conversation,
      extractedData,
      aiResponse.content
    );
    
    // Save message and update conversation
    await this.saveMessage(conversationId, 'user', userMessage);
    await this.saveMessage(conversationId, 'assistant', aiResponse.content, artifacts);
    await this.updateConversationData(conversationId, extractedData);
    
    return {
      response: aiResponse.content,
      artifacts,
      extractedData,
    };
  }
  
  // Create contextual prompt for AI
  private createContextualPrompt(
    conversation: Conversation,
    messageHistory: Message[],
    userMessage: string
  ): string {
    const extractedData = conversation.extractedData || {};
    
    return `
      You are Influtify, an AI mentor that helps users grow their social media presence using Brendan Kane's viral content principles.
      
      CURRENT USER DATA:
      ${JSON.stringify(extractedData, null, 2)}
      
      CONVERSATION HISTORY:
      ${messageHistory.map(m => `${m.role}: ${m.content}`).join('\n')}
      
      USER MESSAGE: ${userMessage}
      
      INSTRUCTIONS:
      1. Extract any new information about the user's goals, current situation, or preferences
      2. Ask follow-up questions if you need more information to create a roadmap
      3. When you have enough information, suggest creating a personalized roadmap
      4. Use function calls to structure extracted data
      5. Be conversational, encouraging, and helpful
      6. Reference Brendan Kane's viral formats when appropriate
      
      REQUIRED INFORMATION FOR ROADMAP:
      - Platform (Instagram, TikTok, YouTube, etc.)
      - Current follower count
      - Target follower count
      - Timeframe for goal
      - Niche/content area
      - Content creation experience level
      
      AVAILABLE FUNCTIONS:
      - extract_user_data: Extract structured data from conversation
      - create_goal_selector: Create interactive goal selection artifact
      - create_roadmap_preview: Create roadmap preview artifact
      - create_progress_chart: Create progress visualization artifact
      - create_content_assistant: Create content creation helper artifact
      
      VIRAL FORMATS TO REFERENCE:
      1. Visual Metaphor Format (Short Form) - Using visual comparisons to explain complex ideas
      2. Two Characters, One Lightbulb Format (Short Form) - One character teaches another
      3. Untold Stories Format (Short Form) - Behind-the-scenes narratives
      4. 30-Day-Challenge Format (Long Form) - Documenting a month-long journey
    `;
  }
  
  // Call AI with function calling support
  private async callAI(prompt: string): Promise<{
    content: string;
    functionCalls?: any[];
  }> {
    const functions = this.getAvailableFunctions();
    
    try {
      let response;
      
      switch (this.aiProvider.provider) {
        case 'openai':
          response = await openaiClient.chat.completions.create({
            model: this.aiProvider.modelName,
            messages: [
              { role: 'system', content: prompt },
            ],
            functions,
            temperature: 0.7,
          });
          
          return {
            content: response.choices[0].message.content || '',
            functionCalls: response.choices[0].message.function_call ? [response.choices[0].message.function_call] : [],
          };
          
        case 'anthropic':
          // Implement Anthropic function calling
          response = await anthropicClient.messages.create({
            model: this.aiProvider.modelName,
            system: prompt,
            messages: [{ role: 'user', content: 'Process this conversation.' }],
            max_tokens: 4000,
          });
          
          return {
            content: response.content[0].text || '',
            functionCalls: [],
          };
          
        case 'gemini':
          // Implement Gemini function calling
          response = await geminiClient.generateContent({
            contents: [{ role: 'user', parts: [{ text: prompt }] }],
            generationConfig: {
              temperature: 0.7,
              maxOutputTokens: 4000,
            },
          });
          
          return {
            content: response.response.text() || '',
            functionCalls: [],
          };
          
        default:
          throw new Error(`Unsupported AI provider: ${this.aiProvider.provider}`);
      }
    } catch (error) {
      console.error('Error calling AI:', error);
      throw new Error('Failed to get AI response. Please try again.');
    }
  }
  
  // Get available functions for AI
  private getAvailableFunctions(): any[] {
    return [
      {
        name: "extract_user_data",
        description: "Extract and structure user information from the conversation",
        parameters: {
          type: "object",
          properties: {
            platform: {
              type: "string",
              enum: ["instagram", "tiktok", "youtube", "linkedin", "twitter", "facebook"],
              description: "Primary social media platform"
            },
            currentFollowers: {
              type: "number",
              description: "Current follower count"
            },
            targetFollowers: {
              type: "number",
              description: "Target follower count"
            },
            timeframe: {
              type: "number",
              description: "Timeframe to achieve goal in days"
            },
            niche: {
              type: "string",
              description: "Content niche or industry"
            },
            experienceLevel: {
              type: "string",
              enum: ["beginner", "intermediate", "advanced"],
              description: "Content creation experience level"
            },
            goals: {
              type: "array",
              items: { type: "string" },
              description: "List of specific goals"
            }
          }
        }
      },
      {
        name: "create_goal_selector",
        description: "Create an interactive goal selection artifact",
        parameters: {
          type: "object",
          properties: {
            title: {
              type: "string",
              description: "Title for the goal selector"
            },
            options: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                  description: { type: "string" },
                  icon: { type: "string" },
                  metrics: {
                    type: "array",
                    items: { type: "string" }
                  }
                }
              }
            }
          }
        }
      },
      {
        name: "create_roadmap_preview",
        description: "Create a roadmap preview artifact based on user goals",
        parameters: {
          type: "object",
          properties: {
            title: { type: "string" },
            goal: { type: "string" },
            timeline: { type: "string" },
            viralFormat: { type: "string" },
            blocks: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  title: { type: "string" },
                  description: { type: "string" },
                  category: { type: "string" },
                  estimatedTime: { type: "string" },
                  tasks: { type: "number" }
                }
              }
            }
          }
        }
      }
    ];
  }
}
```

## Chat Interface Components
Create the main chat interface components:

```typescript
// Main chat interface component
export function ChatInterface({ conversationId }: { conversationId: number }) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [artifacts, setArtifacts] = useState<ArtifactData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');
  
  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;
    
    setIsLoading(true);
    setInputValue('');
    
    try {
      const chatService = new AIChatService();
      const response = await chatService.processMessage(conversationId, message);
      
      // Update messages and artifacts
      await refreshMessages();
      if (response.artifacts) {
        setArtifacts(prev => [...prev, ...response.artifacts]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      // Show error toast
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="flex h-screen bg-base-100">
      {/* Chat Area */}
      <div className="flex-1 flex flex-col max-w-2xl">
        <ChatHeader />
        <ChatMessages messages={messages} isLoading={isLoading} />
        <ChatInput 
          value={inputValue}
          onChange={setInputValue}
          onSendMessage={handleSendMessage} 
          isLoading={isLoading} 
        />
      </div>
      
      {/* Artifacts Area */}
      <div className="flex-1 border-l border-base-300 bg-base-50">
        <ArtifactsRenderer 
          artifacts={artifacts} 
          onArtifactInteraction={handleArtifactInteraction}
        />
      </div>
    </div>
  );
}

// Chat header component
export function ChatHeader() {
  return (
    <div className="navbar bg-base-200 border-b border-base-300">
      <div className="flex-1">
        <div className="flex items-center gap-3">
          <div className="avatar">
            <div className="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
              🤖
            </div>
          </div>
          <div>
            <h1 className="text-lg font-semibold">Influtify AI Mentor</h1>
            <p className="text-sm opacity-70">Your social media growth assistant</p>
          </div>
        </div>
      </div>
      <div className="flex-none">
        <div className="dropdown dropdown-end">
          <label tabIndex={0} className="btn btn-ghost btn-circle">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </label>
          <ul tabIndex={0} className="menu menu-compact dropdown-content mt-3 p-2 shadow bg-base-100 rounded-box w-52">
            <li><a>New Conversation</a></li>
            <li><a>Export Chat</a></li>
            <li><a>Settings</a></li>
          </ul>
        </div>
      </div>
    </div>
  );
}

// Chat messages component
export function ChatMessages({ messages, isLoading }: { messages: Message[]; isLoading: boolean }) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);
  
  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">👋</div>
          <h2 className="text-2xl font-bold mb-2">Welcome to Influtify!</h2>
          <p className="text-base-content/70 max-w-md mx-auto">
            I'm your AI mentor for social media growth. Tell me about your goals and I'll help you create a personalized strategy using proven viral content principles.
          </p>
          <div className="mt-6 space-y-2">
            <p className="text-sm font-medium">Try saying:</p>
            <div className="flex flex-wrap gap-2 justify-center">
              <span className="badge badge-outline">"I want to grow my YouTube channel"</span>
              <span className="badge badge-outline">"Help me get 10k Instagram followers"</span>
              <span className="badge badge-outline">"I'm new to TikTok"</span>
            </div>
          </div>
        </div>
      )}
      
      {messages.map(message => (
        <ChatMessage key={message.id} message={message} />
      ))}
      
      {isLoading && (
        <div className="chat chat-start">
          <div className="chat-image avatar">
            <div className="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
              🤖
            </div>
          </div>
          <div className="chat-bubble">
            <div className="flex items-center gap-2">
              <span className="loading loading-dots loading-sm"></span>
              <span>Thinking...</span>
            </div>
          </div>
        </div>
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
}

// Individual chat message component
export function ChatMessage({ message }: { message: Message }) {
  return (
    <div className={`chat ${message.role === 'user' ? 'chat-end' : 'chat-start'}`}>
      <div className="chat-image avatar">
        <div className="w-10 rounded-full">
          {message.role === 'user' ? (
            <div className="bg-secondary text-secondary-content flex items-center justify-center w-full h-full">
              👤
            </div>
          ) : (
            <div className="bg-primary text-primary-content flex items-center justify-center w-full h-full">
              🤖
            </div>
          )}
        </div>
      </div>
      <div className="chat-header">
        {message.role === 'user' ? 'You' : 'Influtify'}
        <time className="text-xs opacity-50 ml-1">
          {formatTime(message.createdAt)}
        </time>
      </div>
      <div className={`chat-bubble ${message.role === 'user' ? 'chat-bubble-secondary' : 'chat-bubble-primary'}`}>
        <div className="whitespace-pre-wrap">{message.content}</div>
      </div>
    </div>
  );
}

// Chat input component
export function ChatInput({ 
  value, 
  onChange, 
  onSendMessage, 
  isLoading 
}: { 
  value: string;
  onChange: (value: string) => void;
  onSendMessage: (message: string) => void;
  isLoading: boolean;
}) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (value.trim() && !isLoading) {
      onSendMessage(value);
    }
  };
  
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };
  
  return (
    <div className="border-t border-base-300 p-4">
      <form onSubmit={handleSubmit} className="flex gap-2">
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Ask me about growing your social media presence..."
          className="textarea textarea-bordered flex-1 resize-none"
          rows={1}
          disabled={isLoading}
        />
        <button
          type="submit"
          disabled={!value.trim() || isLoading}
          className="btn btn-primary"
        >
          {isLoading ? (
            <span className="loading loading-spinner loading-sm"></span>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          )}
        </button>
      </form>
    </div>
  );
}

// Artifacts renderer component
export function ArtifactsRenderer({ 
  artifacts, 
  onArtifactInteraction 
}: {
  artifacts: ArtifactData[];
  onArtifactInteraction: (artifactId: string, action: string, data: any) => void;
}) {
  if (!artifacts.length) {
    return (
      <div className="p-6 text-center text-base-content/60 h-full flex flex-col items-center justify-center">
        <div className="text-6xl mb-4">🎯</div>
        <h3 className="text-xl font-semibold mb-2">Interactive Tools</h3>
        <p className="max-w-sm">
          As we chat, I'll create interactive tools and visualizations here to help you achieve your social media goals.
        </p>
      </div>
    );
  }
  
  return (
    <div className="p-4 space-y-4 h-full overflow-y-auto">
      <div className="sticky top-0 bg-base-50 pb-2 border-b border-base-300">
        <h2 className="text-lg font-semibold">Interactive Tools</h2>
      </div>
      {artifacts.map(artifact => (
        <ArtifactComponent
          key={artifact.id}
          artifact={artifact}
          onInteraction={onArtifactInteraction}
        />
      ))}
    </div>
  );
}
```

## Multilanguage Support
Implement translations for the chat interface:

```typescript
// Example translation keys
export const chatTranslations = {
  en: {
    "chat.welcome.title": "Welcome to Influtify!",
    "chat.welcome.description": "I'm your AI mentor for social media growth. Tell me about your goals and I'll help you create a personalized strategy using proven viral content principles.",
    "chat.input.placeholder": "Ask me about growing your social media presence...",
    "chat.thinking": "Thinking...",
    "chat.you": "You",
    "chat.influtify": "Influtify",
    // Additional translations...
  },
  es: {
    "chat.welcome.title": "¡Bienvenido a Influtify!",
    "chat.welcome.description": "Soy tu mentor de IA para el crecimiento en redes sociales. Cuéntame sobre tus objetivos y te ayudaré a crear una estrategia personalizada usando principios probados de contenido viral.",
    "chat.input.placeholder": "Pregúntame sobre hacer crecer tu presencia en redes sociales...",
    "chat.thinking": "Pensando...",
    "chat.you": "Tú",
    "chat.influtify": "Influtify",
    // Additional translations...
  }
};
```

## API Routes
Create the necessary API routes for the chat system:

```typescript
// Chat message processing route
app.post('/api/chat/message', async (req, res) => {
  const { conversationId, message } = req.body;
  const { userId } = auth();
  
  try {
    const chatService = new AIChatService();
    const response = await chatService.processMessage(conversationId, message);
    
    res.json(response);
  } catch (error) {
    console.error('Error processing message:', error);
    res.status(500).json({ error: 'Failed to process message' });
  }
});

// Get conversation history
app.get('/api/chat/conversation/:id', async (req, res) => {
  const { id } = req.params;
  const { userId } = auth();
  
  try {
    const conversation = await db.query.conversations.findFirst({
      where: and(
        eq(conversations.id, parseInt(id)),
        eq(conversations.userId, userId)
      ),
    });
    
    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }
    
    const messages = await db.query.messages.findMany({
      where: eq(messages.conversationId, parseInt(id)),
      orderBy: asc(messages.createdAt),
    });
    
    const artifacts = await db.query.artifacts.findMany({
      where: and(
        eq(artifacts.conversationId, parseInt(id)),
        eq(artifacts.isActive, true)
      ),
      orderBy: desc(artifacts.createdAt),
    });
    
    res.json({ conversation, messages, artifacts });
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({ error: 'Failed to fetch conversation' });
  }
});

// Create new conversation
app.post('/api/chat/conversation', async (req, res) => {
  const { userId } = auth();
  
  try {
    const conversation = await db.insert(conversations).values({
      userId,
      title: 'New Conversation',
      status: 'active',
      extractedData: {},
      currentStep: 'information_gathering',
      completionPercentage: 0,
    }).returning().then(rows => rows[0]);
    
    res.json(conversation);
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({ error: 'Failed to create conversation' });
  }
});
```

## Additional Requirements
- Implement real-time updates using WebSockets or Server-Sent Events
- Create conversation persistence and history
- Add typing indicators for AI responses
- Implement message retry functionality
- Create conversation export functionality
- Add conversation search and filtering
- Implement responsive design for mobile chat experience
- Create smooth animations for message appearance
- Add support for rich text formatting in messages
- Implement conversation archiving and deletion

