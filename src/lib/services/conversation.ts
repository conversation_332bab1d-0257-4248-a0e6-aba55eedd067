import { db } from '@/lib/db';
import { conversations, messages } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

export interface ConversationContext {
  userId: string;
  conversationId?: number;
  userProfile?: any;
  currentGoals?: string[];
  recentActivity?: any[];
}

export interface ConversationMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class ConversationService {

  /**
   * Create a new conversation
   */
  async createConversation(userId: string, title?: string): Promise<number> {
    const conversation = await db
      .insert(conversations)
      .values({
        userId,
        title: title || 'New Conversation',
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return conversation[0].id;
  }

  /**
   * Get conversation history
   */
  async getConversationHistory(conversationId: number): Promise<ConversationMessage[]> {
    const messageHistory = await db
      .select()
      .from(messages)
      .where(eq(messages.conversationId, conversationId))
      .orderBy(messages.createdAt);

    return messageHistory.map(msg => ({
      role: msg.role as 'system' | 'user' | 'assistant',
      content: msg.content,
    }));
  }

  /**
   * Save message to conversation
   */
  async saveMessage(
    conversationId: number,
    role: 'user' | 'assistant',
    content: string,
    metadata?: any
  ): Promise<void> {
    await db.insert(messages).values({
      conversationId,
      role,
      content,
      metadata,
      createdAt: new Date(),
    });

    // Update conversation timestamp
    await db
      .update(conversations)
      .set({ updatedAt: new Date() })
      .where(eq(conversations.id, conversationId));
  }

  /**
   * Build system prompt with user context
   * This method is used by the API routes for generating context-aware prompts
   */
  buildSystemPrompt(context: ConversationContext): string {
    const { userProfile, currentGoals } = context;

    let prompt = `You are Influtify, an AI social media growth mentor based on Brendan Kane's "The Guide to Going Viral" methodology.

Your expertise includes:
- The 4 viral content formats: Educational, Entertaining, Inspirational, Conversational
- Hook-Retain-Reward framework for viral content
- Platform-specific strategies for Instagram, TikTok, YouTube, Twitter, LinkedIn
- Audience analysis and engagement optimization
- Content planning and consistency strategies

`;

    if (userProfile) {
      prompt += `User Profile:
- Platform focus: ${userProfile.platform || 'Not specified'}
- Current follower count: ${userProfile.followerCount || 'Not specified'}
- Content niche: ${userProfile.niche || 'Not specified'}
- Experience level: ${userProfile.experienceLevel || 'Beginner'}

`;
    }

    if (currentGoals && currentGoals.length > 0) {
      prompt += `Current Goals:
${currentGoals.map(goal => `- ${goal}`).join('\n')}

`;
    }

    prompt += `Provide actionable, specific advice based on proven viral strategies. Always include practical next steps the user can implement immediately.`;

    return prompt;
  }



  /**
   * Get user's recent conversations
   */
  async getUserConversations(userId: string, limit: number = 10) {
    return await db
      .select()
      .from(conversations)
      .where(eq(conversations.userId, userId))
      .orderBy(desc(conversations.updatedAt))
      .limit(limit);
  }

  /**
   * Update conversation title
   */
  async updateConversationTitle(conversationId: number, title: string): Promise<void> {
    await db
      .update(conversations)
      .set({
        title,
        updatedAt: new Date()
      })
      .where(eq(conversations.id, conversationId));
  }

  /**
   * Delete conversation and all its messages
   */
  async deleteConversation(conversationId: number): Promise<void> {
    // Delete messages first (due to foreign key constraint)
    await db.delete(messages).where(eq(messages.conversationId, conversationId));

    // Delete conversation
    await db.delete(conversations).where(eq(conversations.id, conversationId));
  }

  /**
   * Get conversation by ID with basic info
   */
  async getConversation(conversationId: number) {
    const conversation = await db
      .select()
      .from(conversations)
      .where(eq(conversations.id, conversationId))
      .limit(1);

    return conversation[0] || null;
  }
}

export const conversationService = new ConversationService();
