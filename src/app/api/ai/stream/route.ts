import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { generateInflutifyMentorResponse } from '../../../../lib/services/ai-provider';
import { conversationService } from '../../../../lib/services/conversation';
import { UserService } from '../../../../lib/services/user';
import { z } from 'zod';

const chatRequestSchema = z.object({
  message: z.string().min(1, 'Message cannot be empty'),
  conversationId: z.coerce.number().optional(),
});

export async function GET() {
  return NextResponse.json({ message: 'AI stream API is working' });
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { message, conversationId } = chatRequestSchema.parse(body);

    // Get user profile for context
    const userProfile = await UserService.getCurrentUser();

    // Build conversation context
    const context = {
      userId,
      conversationId,
      userProfile,
      currentGoals: userProfile?.goals || [],
    };

    // Get conversation history for context
    let conversationHistory: any[] = [];
    if (conversationId) {
      conversationHistory = await conversationService.getConversationHistory(conversationId.toString());
    }

    // Create streaming response
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // Generate AI response with streaming
          const response = await generateInflutifyMentorResponse(
            message,
            context,
            conversationHistory
          );

          // Save the conversation
          const { conversationId: finalConversationId } =
            await conversationService.generateMentorResponse(message, context);

          // Send the response with conversation ID
          const responseData = {
            response,
            conversationId: finalConversationId,
          };

          controller.enqueue(
            new TextEncoder().encode(JSON.stringify(responseData))
          );
          controller.close();

        } catch (error) {
          console.error('AI streaming error:', error);
          const errorResponse = {
            error: 'Failed to generate response',
            details: error instanceof Error ? error.message : 'Unknown error'
          };
          controller.enqueue(
            new TextEncoder().encode(JSON.stringify(errorResponse))
          );
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Conversation-Id': conversationId?.toString() || 'new',
      },
    });

  } catch (error) {
    console.error('AI stream API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
