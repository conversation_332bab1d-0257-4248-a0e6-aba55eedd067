import { <PERSON>Provider, AI<PERSON>essage, AIFunction, AIResponse, AIFunctionCall } from '../types';

export class AnthropicProvider implements AIProvider {
  name = 'anthropic';
  private apiKey: string;
  private baseUrl = 'https://api.anthropic.com/v1';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async generateResponse(
    messages: AIMessage[],
    functions?: AIFunction[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      model?: string;
    }
  ): Promise<AIResponse> {
    // Separate system messages from user/assistant messages
    const systemMessages = messages.filter(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');
    
    // Combine system messages into a single system prompt
    const systemPrompt = systemMessages.map(msg => msg.content).join('\n\n');

    const requestBody: any = {
      model: options?.model || 'claude-3-haiku-20240307',
      max_tokens: options?.maxTokens || 2000,
      temperature: options?.temperature || 0.7,
      system: systemPrompt,
      messages: conversationMessages.map(msg => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content,
      })),
    };

    // Note: Anthropic's function calling is different from OpenAI
    // For now, we'll implement basic function support
    if (functions && functions.length > 0) {
      // Add function definitions to system prompt for now
      const functionDescriptions = functions.map(func => 
        `Function: ${func.name}\nDescription: ${func.description}\nParameters: ${JSON.stringify(func.parameters)}`
      ).join('\n\n');
      
      requestBody.system = `${systemPrompt}\n\nAvailable functions:\n${functionDescriptions}`;
    }

    const response = await fetch(`${this.baseUrl}/messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
        'x-api-key': this.apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText} ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    
    // Extract content from Anthropic response format
    let content = '';
    if (data.content && Array.isArray(data.content)) {
      content = data.content
        .filter((item: any) => item.type === 'text')
        .map((item: any) => item.text)
        .join('');
    }

    // Basic function call detection (simplified)
    let functionCall: AIFunctionCall | undefined;
    if (functions && functions.length > 0) {
      // Look for function call patterns in the response
      const functionCallMatch = content.match(/Function:\s*(\w+)\s*Arguments:\s*({.*?})/s);
      if (functionCallMatch) {
        try {
          functionCall = {
            name: functionCallMatch[1],
            arguments: JSON.parse(functionCallMatch[2]),
          };
        } catch (e) {
          // Ignore parsing errors
        }
      }
    }

    return {
      content,
      functionCall,
      usage: {
        promptTokens: data.usage?.input_tokens || 0,
        completionTokens: data.usage?.output_tokens || 0,
        totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0),
      },
      model: data.model,
      finishReason: data.stop_reason,
    };
  }
}
