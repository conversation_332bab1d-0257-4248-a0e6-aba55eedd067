# Influtify Achievement & Progress Tracking System Prompt

## Feature Overview
Create a comprehensive achievement and progress tracking system that gamifies the user's social media growth journey. This system integrates with the chat interface to celebrate milestones, award badges, track progress, and motivate users through their roadmap completion. The system provides visual progress indicators, achievement notifications, and motivational elements to keep users engaged.

## Technical Requirements
- Implement using NextJS with the sliced-feature architecture
- Use Zod for data validation
- Use Drizzle ORM for database operations
- Implement UI with DaisyUI components
- Support multilanguage with next-intl
- Integrate with the AI chat system
- Create animated achievement celebrations
- Implement progress visualization components

## Database Schema
Extend the Drizzle schema with achievement tracking tables:

```typescript
// Achievements Schema
export const achievements = pgTable('achievements', {
  id: serial('id').primaryKey(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Achievement Information
  name: text('name').notNull(),
  title: text('title').notNull(),
  description: text('description').notNull(),
  category: text('category').notNull(), // milestone, consistency, engagement, growth, content
  
  // Achievement Criteria
  criteria: json('criteria').$type<{
    type: string; // follower_count, post_count, engagement_rate, streak_days, etc.
    threshold: number;
    timeframe?: number; // days
    platform?: string;
  }>().notNull(),
  
  // Visual Elements
  icon: text('icon').notNull(),
  color: text('color').notNull(),
  rarity: text('rarity').notNull(), // common, rare, epic, legendary
  
  // Rewards
  points: integer('points').default(0),
  unlocks: json('unlocks').$type<string[]>(), // Features or content unlocked
  
  // Metadata
  isActive: boolean('is_active').default(true),
  sortOrder: integer('sort_order').default(0),
});

// User Achievements Schema
export const userAchievements = pgTable('user_achievements', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  achievementId: integer('achievement_id').references(() => achievements.id).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Achievement Data
  unlockedAt: timestamp('unlocked_at').notNull(),
  progress: integer('progress').default(100), // Percentage when unlocked
  
  // Context
  roadmapId: integer('roadmap_id').references(() => roadmaps.id),
  triggerData: json('trigger_data'), // Data that triggered the achievement
  
  // Celebration
  wasNotified: boolean('was_notified').default(false),
  wasCelebrated: boolean('was_celebrated').default(false),
});

// Progress Milestones Schema
export const progressMilestones = pgTable('progress_milestones', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  roadmapId: integer('roadmap_id').references(() => roadmaps.id).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Milestone Information
  type: text('type').notNull(), // follower_milestone, engagement_milestone, content_milestone
  title: text('title').notNull(),
  description: text('description'),
  
  // Milestone Data
  targetValue: integer('target_value').notNull(),
  currentValue: integer('current_value').notNull(),
  achievedAt: timestamp('achieved_at'),
  
  // Progress
  progressPercentage: integer('progress_percentage').default(0),
  isCompleted: boolean('is_completed').default(false),
  
  // Celebration
  wasNotified: boolean('was_notified').default(false),
});

// User Stats Schema
export const userStats = pgTable('user_stats', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  
  // Overall Stats
  totalPoints: integer('total_points').default(0),
  level: integer('level').default(1),
  experiencePoints: integer('experience_points').default(0),
  
  // Achievement Stats
  totalAchievements: integer('total_achievements').default(0),
  commonAchievements: integer('common_achievements').default(0),
  rareAchievements: integer('rare_achievements').default(0),
  epicAchievements: integer('epic_achievements').default(0),
  legendaryAchievements: integer('legendary_achievements').default(0),
  
  // Activity Stats
  streakDays: integer('streak_days').default(0),
  longestStreak: integer('longest_streak').default(0),
  lastActivityDate: timestamp('last_activity_date'),
  
  // Content Stats
  totalContentCreated: integer('total_content_created').default(0),
  totalBlocksCompleted: integer('total_blocks_completed').default(0),
  totalRoadmapsCompleted: integer('total_roadmaps_completed').default(0),
});

// Daily Activity Schema
export const dailyActivity = pgTable('daily_activity', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  date: date('date').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Activity Data
  activitiesCompleted: json('activities_completed').$type<{
    type: string;
    count: number;
    details?: any;
  }[]>().default([]),
  
  // Points and Experience
  pointsEarned: integer('points_earned').default(0),
  experienceEarned: integer('experience_earned').default(0),
  
  // Streaks
  isStreakDay: boolean('is_streak_day').default(false),
  streakCount: integer('streak_count').default(0),
});
```

## Achievement Service
Implement the core achievement service:

```typescript
export class AchievementService {
  // Check and award achievements for a user action
  async checkAchievements(
    userId: string,
    actionType: string,
    actionData: any,
    roadmapId?: number
  ): Promise<UserAchievement[]> {
    // Get all active achievements
    const allAchievements = await db.query.achievements.findMany({
      where: eq(achievements.isActive, true),
    });
    
    // Get user's existing achievements
    const userAchievements = await db.query.userAchievements.findMany({
      where: eq(userAchievements.userId, userId),
    });
    
    const existingAchievementIds = userAchievements.map(ua => ua.achievementId);
    const newAchievements: UserAchievement[] = [];
    
    // Check each achievement
    for (const achievement of allAchievements) {
      if (existingAchievementIds.includes(achievement.id)) {
        continue; // Already unlocked
      }
      
      const isUnlocked = await this.checkAchievementCriteria(
        achievement,
        userId,
        actionType,
        actionData,
        roadmapId
      );
      
      if (isUnlocked) {
        const userAchievement = await this.awardAchievement(
          userId,
          achievement.id,
          roadmapId,
          actionData
        );
        newAchievements.push(userAchievement);
      }
    }
    
    return newAchievements;
  }
  
  // Check if achievement criteria is met
  private async checkAchievementCriteria(
    achievement: Achievement,
    userId: string,
    actionType: string,
    actionData: any,
    roadmapId?: number
  ): Promise<boolean> {
    const criteria = achievement.criteria;
    
    switch (criteria.type) {
      case 'follower_count':
        return actionType === 'follower_update' && 
               actionData.followers >= criteria.threshold;
      
      case 'post_count':
        const postCount = await this.getUserPostCount(userId, criteria.timeframe);
        return postCount >= criteria.threshold;
      
      case 'engagement_rate':
        return actionType === 'engagement_update' && 
               actionData.engagementRate >= criteria.threshold;
      
      case 'streak_days':
        const streakDays = await this.getUserStreakDays(userId);
        return streakDays >= criteria.threshold;
      
      case 'blocks_completed':
        const blocksCompleted = await this.getUserBlocksCompleted(userId, roadmapId);
        return blocksCompleted >= criteria.threshold;
      
      case 'roadmap_completed':
        return actionType === 'roadmap_completed';
      
      case 'first_content':
        return actionType === 'content_created' && 
               await this.isFirstContent(userId);
      
      default:
        return false;
    }
  }
  
  // Award achievement to user
  private async awardAchievement(
    userId: string,
    achievementId: number,
    roadmapId?: number,
    triggerData?: any
  ): Promise<UserAchievement> {
    // Create user achievement record
    const userAchievement = await db.insert(userAchievements).values({
      userId,
      achievementId,
      unlockedAt: new Date(),
      roadmapId,
      triggerData,
    }).returning().then(rows => rows[0]);
    
    // Get achievement details for points
    const achievement = await db.query.achievements.findFirst({
      where: eq(achievements.id, achievementId),
    });
    
    if (achievement) {
      // Update user stats
      await this.updateUserStats(userId, achievement);
      
      // Record daily activity
      await this.recordDailyActivity(userId, 'achievement_unlocked', {
        achievementId,
        points: achievement.points,
      });
    }
    
    return userAchievement;
  }
  
  // Update user statistics
  private async updateUserStats(userId: string, achievement: Achievement): Promise<void> {
    const stats = await db.query.userStats.findFirst({
      where: eq(userStats.userId, userId),
    });
    
    if (!stats) {
      // Create initial stats
      await db.insert(userStats).values({
        userId,
        totalPoints: achievement.points,
        experiencePoints: achievement.points,
        totalAchievements: 1,
        [`${achievement.rarity}Achievements`]: 1,
      });
    } else {
      // Update existing stats
      const newTotalPoints = stats.totalPoints + achievement.points;
      const newExperience = stats.experiencePoints + achievement.points;
      const newLevel = this.calculateLevel(newExperience);
      
      await db.update(userStats)
        .set({
          totalPoints: newTotalPoints,
          experiencePoints: newExperience,
          level: newLevel,
          totalAchievements: stats.totalAchievements + 1,
          [`${achievement.rarity}Achievements`]: stats[`${achievement.rarity}Achievements` as keyof typeof stats] + 1,
          updatedAt: new Date(),
        })
        .where(eq(userStats.userId, userId));
    }
  }
  
  // Calculate user level based on experience points
  private calculateLevel(experiencePoints: number): number {
    // Level formula: level = floor(sqrt(experience / 100)) + 1
    return Math.floor(Math.sqrt(experiencePoints / 100)) + 1;
  }
  
  // Record daily activity
  async recordDailyActivity(
    userId: string,
    activityType: string,
    activityData: any
  ): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    
    // Get or create today's activity record
    let activity = await db.query.dailyActivity.findFirst({
      where: and(
        eq(dailyActivity.userId, userId),
        eq(dailyActivity.date, today)
      ),
    });
    
    if (!activity) {
      activity = await db.insert(dailyActivity).values({
        userId,
        date: today,
        activitiesCompleted: [],
        pointsEarned: 0,
        experienceEarned: 0,
      }).returning().then(rows => rows[0]);
    }
    
    // Update activity
    const updatedActivities = [
      ...activity.activitiesCompleted,
      {
        type: activityType,
        count: 1,
        details: activityData,
      },
    ];
    
    const pointsEarned = activityData.points || 0;
    
    await db.update(dailyActivity)
      .set({
        activitiesCompleted: updatedActivities,
        pointsEarned: activity.pointsEarned + pointsEarned,
        experienceEarned: activity.experienceEarned + pointsEarned,
      })
      .where(eq(dailyActivity.id, activity.id));
    
    // Update streak
    await this.updateUserStreak(userId);
  }
  
  // Update user activity streak
  private async updateUserStreak(userId: string): Promise<void> {
    const stats = await db.query.userStats.findFirst({
      where: eq(userStats.userId, userId),
    });
    
    if (!stats) return;
    
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const lastActivity = stats.lastActivityDate;
    
    let newStreakDays = 1;
    
    if (lastActivity) {
      const lastActivityDate = new Date(lastActivity);
      const daysDiff = Math.floor((today.getTime() - lastActivityDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysDiff === 1) {
        // Consecutive day
        newStreakDays = stats.streakDays + 1;
      } else if (daysDiff === 0) {
        // Same day
        newStreakDays = stats.streakDays;
      } else {
        // Streak broken
        newStreakDays = 1;
      }
    }
    
    await db.update(userStats)
      .set({
        streakDays: newStreakDays,
        longestStreak: Math.max(stats.longestStreak, newStreakDays),
        lastActivityDate: today,
        updatedAt: new Date(),
      })
      .where(eq(userStats.userId, userId));
  }
  
  // Get user progress overview
  async getUserProgress(userId: string, roadmapId?: number): Promise<UserProgress> {
    const stats = await db.query.userStats.findFirst({
      where: eq(userStats.userId, userId),
    });
    
    const recentAchievements = await db.query.userAchievements.findMany({
      where: eq(userAchievements.userId, userId),
      with: {
        achievement: true,
      },
      orderBy: desc(userAchievements.unlockedAt),
      limit: 5,
    });
    
    const milestones = roadmapId ? await db.query.progressMilestones.findMany({
      where: and(
        eq(progressMilestones.userId, userId),
        eq(progressMilestones.roadmapId, roadmapId)
      ),
      orderBy: asc(progressMilestones.targetValue),
    }) : [];
    
    return {
      stats: stats || this.getDefaultStats(),
      recentAchievements,
      milestones,
      nextLevelProgress: this.calculateNextLevelProgress(stats?.experiencePoints || 0),
    };
  }
  
  private getDefaultStats(): any {
    return {
      totalPoints: 0,
      level: 1,
      experiencePoints: 0,
      totalAchievements: 0,
      streakDays: 0,
      longestStreak: 0,
    };
  }
  
  private calculateNextLevelProgress(experiencePoints: number): {
    currentLevel: number;
    nextLevel: number;
    currentLevelXP: number;
    nextLevelXP: number;
    progress: number;
  } {
    const currentLevel = this.calculateLevel(experiencePoints);
    const nextLevel = currentLevel + 1;
    
    const currentLevelXP = Math.pow(currentLevel - 1, 2) * 100;
    const nextLevelXP = Math.pow(currentLevel, 2) * 100;
    
    const progress = ((experiencePoints - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100;
    
    return {
      currentLevel,
      nextLevel,
      currentLevelXP,
      nextLevelXP,
      progress: Math.max(0, Math.min(100, progress)),
    };
  }
  
  // Helper methods
  private async getUserPostCount(userId: string, timeframeDays?: number): Promise<number> {
    // Implementation would count user's posts in the timeframe
    return 0; // Placeholder
  }
  
  private async getUserStreakDays(userId: string): Promise<number> {
    const stats = await db.query.userStats.findFirst({
      where: eq(userStats.userId, userId),
    });
    return stats?.streakDays || 0;
  }
  
  private async getUserBlocksCompleted(userId: string, roadmapId?: number): Promise<number> {
    const query = roadmapId 
      ? and(
          eq(userBlockProgress.userId, userId),
          eq(userBlockProgress.roadmapId, roadmapId),
          eq(userBlockProgress.status, 'completed')
        )
      : and(
          eq(userBlockProgress.userId, userId),
          eq(userBlockProgress.status, 'completed')
        );
    
    const completed = await db.query.userBlockProgress.findMany({
      where: query,
    });
    
    return completed.length;
  }
  
  private async isFirstContent(userId: string): Promise<boolean> {
    const contentCount = await this.getUserPostCount(userId);
    return contentCount === 1;
  }
}
```

## Achievement Celebration Artifact
Create the achievement celebration artifact:

```typescript
interface AchievementCelebrationArtifact {
  type: 'achievement_celebration';
  data: {
    achievement: {
      id: string;
      title: string;
      description: string;
      icon: string;
      color: string;
      rarity: string;
      points: number;
    };
    userStats: {
      level: number;
      totalPoints: number;
      totalAchievements: number;
    };
    levelUp?: {
      oldLevel: number;
      newLevel: number;
    };
    unlocks?: string[];
  };
}

export function AchievementCelebrationArtifact({ artifact, onInteraction }: ArtifactProps) {
  const { data } = artifact;
  const [isAnimating, setIsAnimating] = useState(true);
  
  const rarityColors = {
    common: 'text-gray-500',
    rare: 'text-blue-500',
    epic: 'text-purple-500',
    legendary: 'text-yellow-500',
  };
  
  const rarityBadges = {
    common: 'badge-neutral',
    rare: 'badge-info',
    epic: 'badge-secondary',
    legendary: 'badge-warning',
  };
  
  useEffect(() => {
    const timer = setTimeout(() => setIsAnimating(false), 3000);
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <div className="card bg-gradient-to-br from-primary/20 to-secondary/20 shadow-xl border-2 border-primary/30">
      <div className="card-body text-center">
        {/* Achievement Icon with Animation */}
        <div className={`text-8xl mb-4 ${isAnimating ? 'animate-bounce' : ''}`}>
          {data.achievement.icon}
        </div>
        
        {/* Achievement Title */}
        <h2 className="card-title text-2xl justify-center mb-2">
          🎉 Achievement Unlocked!
        </h2>
        
        <h3 className={`text-xl font-bold mb-2 ${rarityColors[data.achievement.rarity as keyof typeof rarityColors]}`}>
          {data.achievement.title}
        </h3>
        
        <p className="text-base-content/70 mb-4">
          {data.achievement.description}
        </p>
        
        {/* Achievement Details */}
        <div className="flex justify-center gap-2 mb-4">
          <span className={`badge ${rarityBadges[data.achievement.rarity as keyof typeof rarityBadges]}`}>
            {data.achievement.rarity}
          </span>
          <span className="badge badge-accent">
            +{data.achievement.points} points
          </span>
        </div>
        
        {/* Level Up Notification */}
        {data.levelUp && (
          <div className="alert alert-success mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>
              <strong>Level Up!</strong> You've reached level {data.levelUp.newLevel}!
            </span>
          </div>
        )}
        
        {/* Unlocks */}
        {data.unlocks && data.unlocks.length > 0 && (
          <div className="bg-base-200 p-4 rounded-lg mb-4">
            <h4 className="font-semibold mb-2">🔓 Unlocked:</h4>
            <ul className="text-sm space-y-1">
              {data.unlocks.map((unlock, index) => (
                <li key={index} className="flex items-center gap-2">
                  <span className="text-success">✓</span>
                  {unlock}
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {/* User Stats */}
        <div className="stats stats-horizontal shadow mb-4">
          <div className="stat">
            <div className="stat-title">Level</div>
            <div className="stat-value text-primary">{data.userStats.level}</div>
          </div>
          <div className="stat">
            <div className="stat-title">Total Points</div>
            <div className="stat-value text-secondary">{data.userStats.totalPoints.toLocaleString()}</div>
          </div>
          <div className="stat">
            <div className="stat-title">Achievements</div>
            <div className="stat-value text-accent">{data.userStats.totalAchievements}</div>
          </div>
        </div>
        
        <div className="card-actions justify-center">
          <button 
            className="btn btn-primary"
            onClick={() => onInteraction(artifact.id, 'continue_journey', {})}
          >
            Continue Your Journey! 🚀
          </button>
        </div>
      </div>
    </div>
  );
}
```

## Progress Dashboard Artifact
Create a comprehensive progress dashboard:

```typescript
interface ProgressDashboardArtifact {
  type: 'progress_dashboard';
  data: {
    userStats: {
      level: number;
      experiencePoints: number;
      totalPoints: number;
      totalAchievements: number;
      streakDays: number;
      longestStreak: number;
    };
    nextLevelProgress: {
      currentLevel: number;
      nextLevel: number;
      progress: number;
      pointsNeeded: number;
    };
    recentAchievements: {
      id: string;
      title: string;
      icon: string;
      rarity: string;
      unlockedAt: string;
    }[];
    milestones: {
      id: string;
      title: string;
      targetValue: number;
      currentValue: number;
      progressPercentage: number;
      isCompleted: boolean;
    }[];
    weeklyActivity: {
      date: string;
      activitiesCount: number;
      pointsEarned: number;
    }[];
  };
}

export function ProgressDashboardArtifact({ artifact, onInteraction }: ArtifactProps) {
  const { data } = artifact;
  
  const rarityColors = {
    common: 'text-gray-500',
    rare: 'text-blue-500',
    epic: 'text-purple-500',
    legendary: 'text-yellow-500',
  };
  
  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-primary mb-4">📊 Your Progress Dashboard</h2>
        
        {/* Level and Experience */}
        <div className="bg-gradient-to-r from-primary/10 to-secondary/10 p-4 rounded-lg mb-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold">Level {data.userStats.level}</h3>
            <span className="text-sm opacity-70">
              {data.userStats.experiencePoints.toLocaleString()} XP
            </span>
          </div>
          <progress 
            className="progress progress-primary w-full mb-2" 
            value={data.nextLevelProgress.progress} 
            max="100"
          ></progress>
          <div className="text-sm opacity-70 text-center">
            {data.nextLevelProgress.pointsNeeded} XP to level {data.nextLevelProgress.nextLevel}
          </div>
        </div>
        
        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div className="stat bg-base-200 rounded-lg p-3">
            <div className="stat-figure text-primary">🏆</div>
            <div className="stat-title text-xs">Achievements</div>
            <div className="stat-value text-lg">{data.userStats.totalAchievements}</div>
          </div>
          <div className="stat bg-base-200 rounded-lg p-3">
            <div className="stat-figure text-secondary">💎</div>
            <div className="stat-title text-xs">Total Points</div>
            <div className="stat-value text-lg">{data.userStats.totalPoints.toLocaleString()}</div>
          </div>
          <div className="stat bg-base-200 rounded-lg p-3">
            <div className="stat-figure text-accent">🔥</div>
            <div className="stat-title text-xs">Current Streak</div>
            <div className="stat-value text-lg">{data.userStats.streakDays}</div>
          </div>
          <div className="stat bg-base-200 rounded-lg p-3">
            <div className="stat-figure text-warning">⭐</div>
            <div className="stat-title text-xs">Best Streak</div>
            <div className="stat-value text-lg">{data.userStats.longestStreak}</div>
          </div>
        </div>
        
        {/* Recent Achievements */}
        {data.recentAchievements.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2">🏆 Recent Achievements</h3>
            <div className="space-y-2">
              {data.recentAchievements.map(achievement => (
                <div key={achievement.id} className="flex items-center gap-3 bg-base-200 p-2 rounded">
                  <span className="text-2xl">{achievement.icon}</span>
                  <div className="flex-1">
                    <h4 className={`font-medium ${rarityColors[achievement.rarity as keyof typeof rarityColors]}`}>
                      {achievement.title}
                    </h4>
                    <p className="text-xs opacity-70">
                      {new Date(achievement.unlockedAt).toLocaleDateString()}
                    </p>
                  </div>
                  <span className="badge badge-sm">{achievement.rarity}</span>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Milestones */}
        {data.milestones.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2">🎯 Current Milestones</h3>
            <div className="space-y-3">
              {data.milestones.map(milestone => (
                <div key={milestone.id} className="bg-base-200 p-3 rounded">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">{milestone.title}</h4>
                    <span className="text-sm">
                      {milestone.currentValue.toLocaleString()} / {milestone.targetValue.toLocaleString()}
                    </span>
                  </div>
                  <progress 
                    className={`progress w-full ${milestone.isCompleted ? 'progress-success' : 'progress-primary'}`}
                    value={milestone.progressPercentage} 
                    max="100"
                  ></progress>
                  <div className="text-xs opacity-70 mt-1">
                    {milestone.progressPercentage.toFixed(1)}% complete
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Weekly Activity */}
        {data.weeklyActivity.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2">📈 Weekly Activity</h3>
            <div className="grid grid-cols-7 gap-1">
              {data.weeklyActivity.map((day, index) => (
                <div key={index} className="text-center">
                  <div className="text-xs opacity-70 mb-1">
                    {new Date(day.date).toLocaleDateString('en', { weekday: 'short' })}
                  </div>
                  <div 
                    className={`w-full h-8 rounded flex items-center justify-center text-xs ${
                      day.activitiesCount > 0 
                        ? 'bg-primary text-primary-content' 
                        : 'bg-base-300'
                    }`}
                  >
                    {day.activitiesCount}
                  </div>
                  <div className="text-xs opacity-50 mt-1">
                    {day.pointsEarned}pt
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        <div className="card-actions justify-end">
          <button 
            className="btn btn-outline btn-sm"
            onClick={() => onInteraction(artifact.id, 'view_all_achievements', {})}
          >
            View All Achievements
          </button>
          <button 
            className="btn btn-primary btn-sm"
            onClick={() => onInteraction(artifact.id, 'continue_progress', {})}
          >
            Keep Going! 💪
          </button>
        </div>
      </div>
    </div>
  );
}
```

## Chat Integration for Achievements
Integrate achievements with the AI chat system:

```typescript
// AI function for celebrating achievements
const celebrateAchievementFunction = {
  name: "celebrate_achievement",
  description: "Create an achievement celebration artifact",
  parameters: {
    type: "object",
    properties: {
      achievementId: {
        type: "string",
        description: "ID of the unlocked achievement"
      },
      message: {
        type: "string",
        description: "Congratulatory message"
      }
    }
  }
};

// AI function for showing progress dashboard
const showProgressDashboardFunction = {
  name: "show_progress_dashboard",
  description: "Display user's progress dashboard",
  parameters: {
    type: "object",
    properties: {
      includeWeeklyActivity: {
        type: "boolean",
        description: "Whether to include weekly activity chart"
      }
    }
  }
};

// Enhanced AI prompt for achievement integration
const achievementPrompt = `
  You can help users with their progress and achievements by:
  1. Celebrating achievements when they're unlocked
  2. Showing progress dashboards and statistics
  3. Motivating users to continue their journey
  4. Explaining achievement criteria and how to unlock them
  5. Encouraging consistent activity and streaks
  
  ACHIEVEMENT CATEGORIES:
  - Milestone: Follower count, engagement milestones
  - Consistency: Daily streaks, regular posting
  - Engagement: High engagement rates, viral content
  - Growth: Rapid follower growth, reach milestones
  - Content: Content creation, block completion
  
  MOTIVATION STRATEGIES:
  - Celebrate every achievement, no matter how small
  - Highlight progress and improvements
  - Encourage streak maintenance
  - Set achievable next goals
  - Share success stories and examples
`;
```

## API Routes for Achievement System
Create API routes for achievement operations:

```typescript
// Get user achievements
app.get('/api/achievements/user', async (req, res) => {
  const { userId } = auth();
  
  try {
    const achievementService = new AchievementService();
    const progress = await achievementService.getUserProgress(userId);
    
    res.json(progress);
  } catch (error) {
    console.error('Error fetching user achievements:', error);
    res.status(500).json({ error: 'Failed to fetch achievements' });
  }
});

// Check achievements for action
app.post('/api/achievements/check', async (req, res) => {
  const { actionType, actionData, roadmapId } = req.body;
  const { userId } = auth();
  
  try {
    const achievementService = new AchievementService();
    const newAchievements = await achievementService.checkAchievements(
      userId,
      actionType,
      actionData,
      roadmapId
    );
    
    res.json({ newAchievements });
  } catch (error) {
    console.error('Error checking achievements:', error);
    res.status(500).json({ error: 'Failed to check achievements' });
  }
});

// Record daily activity
app.post('/api/achievements/activity', async (req, res) => {
  const { activityType, activityData } = req.body;
  const { userId } = auth();
  
  try {
    const achievementService = new AchievementService();
    await achievementService.recordDailyActivity(userId, activityType, activityData);
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error recording activity:', error);
    res.status(500).json({ error: 'Failed to record activity' });
  }
});
```

## Default Achievements Data
Create a set of default achievements:

```typescript
export const defaultAchievements = [
  {
    name: 'first_steps',
    title: 'First Steps',
    description: 'Complete your first content block',
    category: 'content',
    criteria: { type: 'blocks_completed', threshold: 1 },
    icon: '👶',
    color: '#10b981',
    rarity: 'common',
    points: 50,
  },
  {
    name: 'content_creator',
    title: 'Content Creator',
    description: 'Create your first piece of content',
    category: 'content',
    criteria: { type: 'first_content', threshold: 1 },
    icon: '🎨',
    color: '#3b82f6',
    rarity: 'common',
    points: 100,
  },
  {
    name: 'consistent_creator',
    title: 'Consistent Creator',
    description: 'Maintain a 7-day activity streak',
    category: 'consistency',
    criteria: { type: 'streak_days', threshold: 7 },
    icon: '🔥',
    color: '#f59e0b',
    rarity: 'rare',
    points: 200,
  },
  {
    name: 'thousand_followers',
    title: 'Rising Star',
    description: 'Reach 1,000 followers',
    category: 'milestone',
    criteria: { type: 'follower_count', threshold: 1000 },
    icon: '⭐',
    color: '#8b5cf6',
    rarity: 'epic',
    points: 500,
  },
  {
    name: 'viral_content',
    title: 'Viral Sensation',
    description: 'Achieve 10% engagement rate',
    category: 'engagement',
    criteria: { type: 'engagement_rate', threshold: 10 },
    icon: '🚀',
    color: '#ef4444',
    rarity: 'legendary',
    points: 1000,
  },
  // Add more achievements...
];
```

## Additional Requirements
- Implement achievement notification system
- Create achievement sharing functionality
- Add achievement leaderboards
- Implement seasonal/limited-time achievements
- Create achievement recommendation system
- Add achievement progress tracking
- Implement achievement categories and filters
- Create achievement export functionality
- Add achievement analytics and insights
- Implement achievement customization options

