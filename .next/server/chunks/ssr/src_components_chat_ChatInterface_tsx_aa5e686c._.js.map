{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/components/chat/ChatInterface.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useUser } from '@clerk/nextjs';\n\ninterface Message {\n  id: string;\n  role: 'user' | 'assistant';\n  content: string;\n  timestamp: Date;\n  isStreaming?: boolean;\n}\n\ninterface ChatInterfaceProps {\n  conversationId?: string;\n  onConversationIdChange?: (id: string) => void;\n}\n\nexport default function ChatInterface({ conversationId, onConversationIdChange }: ChatInterfaceProps) {\n  const { user } = useUser();\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [input, setInput] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [currentStreamingId, setCurrentStreamingId] = useState<string | null>(null);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n  const abortControllerRef = useRef<AbortController | null>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      role: 'user',\n      content: input.trim(),\n      timestamp: new Date(),\n    };\n\n    const assistantMessageId = (Date.now() + 1).toString();\n    const assistantMessage: Message = {\n      id: assistantMessageId,\n      role: 'assistant',\n      content: '',\n      timestamp: new Date(),\n      isStreaming: true,\n    };\n\n    setMessages(prev => [...prev, userMessage, assistantMessage]);\n    setInput('');\n    setIsLoading(true);\n    setCurrentStreamingId(assistantMessageId);\n\n    // Create abort controller for this request\n    abortControllerRef.current = new AbortController();\n\n    try {\n      const response = await fetch('/api/chat/stream', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: userMessage.content,\n          conversationId,\n          platform: 'instagram', // TODO: Get from user profile\n          followerCount: 1000, // TODO: Get from user profile\n        }),\n        signal: abortControllerRef.current.signal,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      // Get conversation ID from headers\n      const newConversationId = response.headers.get('X-Conversation-Id');\n      if (newConversationId && newConversationId !== conversationId) {\n        onConversationIdChange?.(newConversationId);\n      }\n\n      // Get additional metadata from headers\n      const aiProvider = response.headers.get('X-AI-Provider');\n      const userIntent = response.headers.get('X-User-Intent');\n      const platform = response.headers.get('X-Platform');\n      const urgency = response.headers.get('X-Urgency');\n\n      console.log('[Influtify] Response metadata:', {\n        provider: aiProvider,\n        intent: userIntent,\n        platform,\n        urgency,\n      });\n\n      // Process streaming response\n      const reader = response.body?.getReader();\n      if (!reader) {\n        throw new Error('No response body reader available');\n      }\n\n      const decoder = new TextDecoder();\n      let accumulatedContent = '';\n\n      while (true) {\n        const { done, value } = await reader.read();\n        if (done) break;\n\n        const chunk = decoder.decode(value, { stream: true });\n        accumulatedContent += chunk;\n\n        // Update the streaming message\n        setMessages(prev =>\n          prev.map(msg =>\n            msg.id === assistantMessageId\n              ? { ...msg, content: accumulatedContent }\n              : msg\n          )\n        );\n      }\n\n      // Mark streaming as complete\n      setMessages(prev =>\n        prev.map(msg =>\n          msg.id === assistantMessageId\n            ? { ...msg, isStreaming: false }\n            : msg\n        )\n      );\n\n    } catch (error) {\n      console.error('[Influtify] Chat error:', error);\n      \n      // Update the assistant message with error\n      setMessages(prev =>\n        prev.map(msg =>\n          msg.id === assistantMessageId\n            ? {\n                ...msg,\n                content: 'Sorry, I encountered an error. Please try again.',\n                isStreaming: false,\n              }\n            : msg\n        )\n      );\n    } finally {\n      setIsLoading(false);\n      setCurrentStreamingId(null);\n      abortControllerRef.current = null;\n    }\n  };\n\n  const handleStop = () => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      setIsLoading(false);\n      setCurrentStreamingId(null);\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Chat Messages Area */}\n      <div className=\"flex-1 p-6 overflow-y-auto bg-gradient-to-b from-base-50 to-base-100\">\n        <div className=\"space-y-6 max-w-4xl\">\n          {/* Welcome Message */}\n          <div className=\"chat chat-start\">\n            <div className=\"chat-image avatar\">\n              <div className=\"w-12 rounded-full bg-gradient-to-br from-primary to-secondary text-white flex items-center justify-center shadow-lg\">\n                🤖\n              </div>\n            </div>\n            <div className=\"chat-bubble chat-bubble-primary shadow-lg max-w-lg\">\n              <div className=\"font-medium mb-2\">Welcome to Influtify! 🚀</div>\n              I'm your AI social media growth mentor. I'll help you build your presence using proven viral strategies from Brendan Kane's methods.\n              <br /><br />\n              <div className=\"bg-primary/10 rounded-lg p-3 mt-3\">\n                <div className=\"font-medium text-primary-content/90 mb-2\">To get started, tell me:</div>\n                <div className=\"space-y-1 text-sm\">\n                  <div>• What platform do you want to focus on?</div>\n                  <div>• What's your current follower count?</div>\n                  <div>• What type of content do you create?</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Dynamic Messages */}\n          {messages.map((message) => (\n            <div key={message.id} className={`chat ${message.role === 'user' ? 'chat-end' : 'chat-start'}`}>\n              <div className=\"chat-image avatar\">\n                <div className={`w-10 rounded-full flex items-center justify-center shadow-md ${\n                  message.role === 'user' \n                    ? 'bg-gradient-to-br from-accent to-accent-focus text-white' \n                    : 'bg-gradient-to-br from-primary to-secondary text-white'\n                }`}>\n                  {message.role === 'user' ? (user?.firstName?.[0] || '👤') : '🤖'}\n                </div>\n              </div>\n              <div className={`chat-bubble shadow-lg max-w-lg ${\n                message.role === 'user' ? 'chat-bubble-accent' : 'chat-bubble-primary'\n              }`}>\n                <div className=\"whitespace-pre-wrap\">\n                  {message.content}\n                  {message.isStreaming && (\n                    <span className=\"inline-block w-2 h-5 bg-current ml-1 animate-pulse\" />\n                  )}\n                </div>\n              </div>\n              <div className=\"chat-footer opacity-50 text-xs\">\n                {message.timestamp.toLocaleTimeString()}\n              </div>\n            </div>\n          ))}\n          \n          <div ref={messagesEndRef} />\n        </div>\n      </div>\n      \n      {/* Chat Input */}\n      <div className=\"bg-base-100 p-6 border-t border-base-200\">\n        <form onSubmit={handleSubmit} className=\"flex gap-3\">\n          <input\n            type=\"text\"\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            placeholder=\"Type your message...\"\n            className=\"input input-bordered flex-1 shadow-sm focus:shadow-md transition-shadow\"\n            disabled={isLoading}\n          />\n          {isLoading ? (\n            <button \n              type=\"button\"\n              onClick={handleStop}\n              className=\"btn btn-error shadow-lg hover:shadow-xl transition-all gap-2\"\n            >\n              <span>Stop</span>\n              <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin\" />\n            </button>\n          ) : (\n            <button \n              type=\"submit\"\n              className=\"btn btn-primary shadow-lg hover:shadow-xl transition-all gap-2\"\n              disabled={!input.trim()}\n            >\n              <span>Send</span>\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n              </svg>\n            </button>\n          )}\n        </form>\n        {isLoading && (\n          <div className=\"flex items-center gap-2 mt-3\">\n            <div className=\"badge badge-primary badge-sm\">🤖</div>\n            <p className=\"text-xs text-base-content/70\">AI mentor is thinking...</p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAkBe,SAAS,cAAc,EAAE,cAAc,EAAE,sBAAsB,EAAsB;IAClG,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAE1D,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,WAAW;QAEhC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS,MAAM,IAAI;YACnB,WAAW,IAAI;QACjB;QAEA,MAAM,qBAAqB,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;QACpD,MAAM,mBAA4B;YAChC,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;YACf,aAAa;QACf;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;gBAAa;aAAiB;QAC5D,SAAS;QACT,aAAa;QACb,sBAAsB;QAEtB,2CAA2C;QAC3C,mBAAmB,OAAO,GAAG,IAAI;QAEjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,YAAY,OAAO;oBAC5B;oBACA,UAAU;oBACV,eAAe;gBACjB;gBACA,QAAQ,mBAAmB,OAAO,CAAC,MAAM;YAC3C;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,mCAAmC;YACnC,MAAM,oBAAoB,SAAS,OAAO,CAAC,GAAG,CAAC;YAC/C,IAAI,qBAAqB,sBAAsB,gBAAgB;gBAC7D,yBAAyB;YAC3B;YAEA,uCAAuC;YACvC,MAAM,aAAa,SAAS,OAAO,CAAC,GAAG,CAAC;YACxC,MAAM,aAAa,SAAS,OAAO,CAAC,GAAG,CAAC;YACxC,MAAM,WAAW,SAAS,OAAO,CAAC,GAAG,CAAC;YACtC,MAAM,UAAU,SAAS,OAAO,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,kCAAkC;gBAC5C,UAAU;gBACV,QAAQ;gBACR;gBACA;YACF;YAEA,6BAA6B;YAC7B,MAAM,SAAS,SAAS,IAAI,EAAE;YAC9B,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAU,IAAI;YACpB,IAAI,qBAAqB;YAEzB,MAAO,KAAM;gBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;gBACzC,IAAI,MAAM;gBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC,OAAO;oBAAE,QAAQ;gBAAK;gBACnD,sBAAsB;gBAEtB,+BAA+B;gBAC/B,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,qBACP;4BAAE,GAAG,GAAG;4BAAE,SAAS;wBAAmB,IACtC;YAGV;YAEA,6BAA6B;YAC7B,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,qBACP;wBAAE,GAAG,GAAG;wBAAE,aAAa;oBAAM,IAC7B;QAIV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,0CAA0C;YAC1C,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,qBACP;wBACE,GAAG,GAAG;wBACN,SAAS;wBACT,aAAa;oBACf,IACA;QAGV,SAAU;YACR,aAAa;YACb,sBAAsB;YACtB,mBAAmB,OAAO,GAAG;QAC/B;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,KAAK;YAChC,aAAa;YACb,sBAAsB;QACxB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAsH;;;;;;;;;;;8CAIvI,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAmB;;;;;;wCAA8B;sDAEhE,8OAAC;;;;;sDAAK,8OAAC;;;;;sDACP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAA2C;;;;;;8DAC1D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;sEACL,8OAAC;sEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAOZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gCAAqB,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,aAAa,cAAc;;kDAC5F,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,CAAC,6DAA6D,EAC5E,QAAQ,IAAI,KAAK,SACb,6DACA,0DACJ;sDACC,QAAQ,IAAI,KAAK,SAAU,MAAM,WAAW,CAAC,EAAE,IAAI,OAAQ;;;;;;;;;;;kDAGhE,8OAAC;wCAAI,WAAW,CAAC,+BAA+B,EAC9C,QAAQ,IAAI,KAAK,SAAS,uBAAuB,uBACjD;kDACA,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,QAAQ,OAAO;gDACf,QAAQ,WAAW,kBAClB,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAItB,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;+BArB/B,QAAQ,EAAE;;;;;sCA0BtB,8OAAC;4BAAI,KAAK;;;;;;;;;;;;;;;;;0BAKd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,WAAU;gCACV,UAAU;;;;;;4BAEX,0BACC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAI,WAAU;;;;;;;;;;;qDAGjB,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,UAAU,CAAC,MAAM,IAAI;;kDAErB,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;oBAK5E,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA+B;;;;;;0CAC9C,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;;;;;;;;;;;;;;;;;;;AAMxD", "debugId": null}}]}