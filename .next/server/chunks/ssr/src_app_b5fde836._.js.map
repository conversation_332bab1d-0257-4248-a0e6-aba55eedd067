{"version": 3, "sources": [], "sections": [{"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/app/dashboard/page.tsx"], "sourcesContent": ["import { auth } from '@clerk/nextjs/server';\nimport { redirect } from 'next/navigation';\nimport { UserButton } from '@clerk/nextjs';\nimport Link from 'next/link';\n\nexport default async function DashboardPage() {\n  const { userId } = await auth();\n  \n  if (!userId) {\n    redirect('/');\n  }\n\n  return (\n    <div className=\"min-h-screen bg-base-200\">\n      {/* Navigation */}\n      <nav className=\"navbar bg-base-100 shadow-lg\">\n        <div className=\"navbar-start\">\n          <Link href=\"/\" className=\"btn btn-ghost text-xl font-bold text-primary\">\n            🚀 Influtify\n          </Link>\n        </div>\n        <div className=\"navbar-center\">\n          <div className=\"tabs tabs-boxed\">\n            <a className=\"tab tab-active\">Chat</a>\n            <a className=\"tab\">Roadmap</a>\n            <a className=\"tab\">Progress</a>\n          </div>\n        </div>\n        <div className=\"navbar-end\">\n          <UserButton />\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <div className=\"flex h-[calc(100vh-4rem)]\">\n        {/* Chat Panel */}\n        <div className=\"flex-1 flex flex-col\">\n          <div className=\"bg-base-100 p-4 border-b\">\n            <h2 className=\"text-xl font-semibold\">AI Mentor Chat</h2>\n            <p className=\"text-sm opacity-70\">Ask me anything about growing your social media presence</p>\n          </div>\n          \n          {/* Chat Messages Area */}\n          <div className=\"flex-1 p-4 overflow-y-auto\">\n            <div className=\"space-y-4\">\n              {/* Welcome Message */}\n              <div className=\"chat chat-start\">\n                <div className=\"chat-image avatar\">\n                  <div className=\"w-10 rounded-full bg-primary text-primary-content flex items-center justify-center\">\n                    🤖\n                  </div>\n                </div>\n                <div className=\"chat-bubble chat-bubble-primary\">\n                  Welcome to Influtify! I'm your AI social media growth mentor. \n                  I'll help you build your presence using proven viral strategies from Brendan Kane's methods.\n                  <br /><br />\n                  To get started, tell me:\n                  <br />• What platform do you want to focus on?\n                  <br />• What's your current follower count?\n                  <br />• What type of content do you create?\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          {/* Chat Input */}\n          <div className=\"bg-base-100 p-4 border-t\">\n            <div className=\"flex gap-2\">\n              <input \n                type=\"text\" \n                placeholder=\"Type your message...\" \n                className=\"input input-bordered flex-1\"\n                disabled\n              />\n              <button className=\"btn btn-primary\" disabled>\n                Send\n              </button>\n            </div>\n            <p className=\"text-xs opacity-50 mt-2\">Chat functionality coming soon...</p>\n          </div>\n        </div>\n\n        {/* Artifacts Panel */}\n        <div className=\"w-96 bg-base-100 border-l\">\n          <div className=\"p-4 border-b\">\n            <h3 className=\"text-lg font-semibold\">Interactive Tools</h3>\n            <p className=\"text-sm opacity-70\">Roadmaps, progress tracking, and more</p>\n          </div>\n          \n          <div className=\"p-4 space-y-4\">\n            {/* Quick Stats */}\n            <div className=\"stats stats-vertical shadow w-full\">\n              <div className=\"stat\">\n                <div className=\"stat-title\">Level</div>\n                <div className=\"stat-value text-primary\">1</div>\n                <div className=\"stat-desc\">Beginner</div>\n              </div>\n              \n              <div className=\"stat\">\n                <div className=\"stat-title\">XP Points</div>\n                <div className=\"stat-value text-secondary\">0</div>\n                <div className=\"stat-desc\">Start your journey!</div>\n              </div>\n              \n              <div className=\"stat\">\n                <div className=\"stat-title\">Streak</div>\n                <div className=\"stat-value\">0 days</div>\n                <div className=\"stat-desc\">🔥 Keep going!</div>\n              </div>\n            </div>\n\n            {/* Quick Actions */}\n            <div className=\"card bg-base-200\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title text-sm\">Quick Start</h4>\n                <div className=\"space-y-2\">\n                  <button className=\"btn btn-sm btn-outline w-full\">\n                    📊 Set Goals\n                  </button>\n                  <button className=\"btn btn-sm btn-outline w-full\">\n                    🗺️ View Roadmap\n                  </button>\n                  <button className=\"btn btn-sm btn-outline w-full\">\n                    📚 Browse Content Blocks\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Achievements */}\n            <div className=\"card bg-base-200\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title text-sm\">Achievements</h4>\n                <p className=\"text-xs opacity-70\">Complete actions to unlock badges!</p>\n                <div className=\"grid grid-cols-3 gap-2 mt-2\">\n                  <div className=\"badge badge-ghost\">🏆</div>\n                  <div className=\"badge badge-ghost\">⭐</div>\n                  <div className=\"badge badge-ghost\">🚀</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AACA;AACA;;;;;;AAEe,eAAe;IAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,mLAAA,CAAA,OAAI,AAAD;IAE5B,IAAI,CAAC,QAAQ;QACX,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAA+C;;;;;;;;;;;kCAI1E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAiB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAM;;;;;;8CACnB,8OAAC;oCAAE,WAAU;8CAAM;;;;;;;;;;;;;;;;;kCAGvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sLAAA,CAAA,aAAU;;;;;;;;;;;;;;;;0BAKf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAIpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAAqF;;;;;;;;;;;0DAItG,8OAAC;gDAAI,WAAU;;oDAAkC;kEAG/C,8OAAC;;;;;kEAAK,8OAAC;;;;;oDAAK;kEAEZ,8OAAC;;;;;oDAAK;kEACN,8OAAC;;;;;oDAAK;kEACN,8OAAC;;;;;oDAAK;;;;;;;;;;;;;;;;;;;;;;;0CAOd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;0DAEV,8OAAC;gDAAO,WAAU;gDAAkB,QAAQ;0DAAC;;;;;;;;;;;;kDAI/C,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEAAY;;;;;;;;;;;;0DAG7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,8OAAC;wDAAI,WAAU;kEAA4B;;;;;;kEAC3C,8OAAC;wDAAI,WAAU;kEAAY;;;;;;;;;;;;0DAG7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,8OAAC;wDAAI,WAAU;kEAAa;;;;;;kEAC5B,8OAAC;wDAAI,WAAU;kEAAY;;;;;;;;;;;;;;;;;;kDAK/B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;sEAAgC;;;;;;sEAGlD,8OAAC;4DAAO,WAAU;sEAAgC;;;;;;sEAGlD,8OAAC;4DAAO,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;kDAQxD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAoB;;;;;;sEACnC,8OAAC;4DAAI,WAAU;sEAAoB;;;;;;sEACnC,8OAAC;4DAAI,WAAU;sEAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD", "debugId": null}}]}