# Influtify Artifacts System Prompt

## Feature Overview
Create an artifacts system that renders interactive components in the right panel of the chat interface. These artifacts allow users to interact with goal selectors, progress visualizations, roadmap previews, content creation assistants, and other dynamic components generated during the conversation. This system makes the chat interface truly interactive and engaging.

## Technical Requirements
- Implement using NextJS with the sliced-feature architecture
- Use Zod for data validation
- Use Drizzle ORM for database operations
- Implement UI with DaisyUI components
- Support multilanguage with next-intl
- Create reusable artifact components
- Handle artifact state management and interactions

## Artifact Types and Components

### 1. Goal Selector Artifact
Interactive component for users to select their social media goals:

```typescript
interface GoalSelectorArtifact {
  type: 'goal_selector';
  data: {
    title: string;
    description?: string;
    options: {
      id: string;
      title: string;
      description: string;
      icon: string;
      metrics: string[];
      difficulty: 'beginner' | 'intermediate' | 'advanced';
    }[];
    selectedGoals?: string[];
    maxSelections?: number;
  };
}

export function GoalSelectorArtifact({ artifact, onInteraction }: ArtifactProps) {
  const [selectedGoals, setSelectedGoals] = useState(artifact.data.selectedGoals || []);
  const maxSelections = artifact.data.maxSelections || 3;
  
  const handleGoalToggle = (goalId: string) => {
    const isSelected = selectedGoals.includes(goalId);
    let newSelection: string[];
    
    if (isSelected) {
      newSelection = selectedGoals.filter(id => id !== goalId);
    } else if (selectedGoals.length < maxSelections) {
      newSelection = [...selectedGoals, goalId];
    } else {
      return; // Max selections reached
    }
    
    setSelectedGoals(newSelection);
    onInteraction(artifact.id, 'goal_selection_changed', { selectedGoals: newSelection });
  };
  
  const handleConfirm = () => {
    onInteraction(artifact.id, 'goals_confirmed', { selectedGoals });
  };
  
  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-primary">{artifact.data.title}</h2>
        {artifact.data.description && (
          <p className="text-base-content/70 mb-4">{artifact.data.description}</p>
        )}
        
        <div className="text-sm text-base-content/60 mb-4">
          Select up to {maxSelections} goals ({selectedGoals.length}/{maxSelections} selected)
        </div>
        
        <div className="grid grid-cols-1 gap-3">
          {artifact.data.options.map(option => {
            const isSelected = selectedGoals.includes(option.id);
            const canSelect = selectedGoals.length < maxSelections || isSelected;
            
            return (
              <div 
                key={option.id}
                className={`card cursor-pointer transition-all border-2 ${
                  isSelected 
                    ? 'bg-primary text-primary-content border-primary' 
                    : canSelect
                    ? 'bg-base-200 hover:bg-base-300 border-transparent hover:border-primary/30'
                    : 'bg-base-200/50 border-transparent opacity-50 cursor-not-allowed'
                }`}
                onClick={() => canSelect && handleGoalToggle(option.id)}
              >
                <div className="card-body p-4">
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{option.icon}</span>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="font-semibold">{option.title}</h3>
                        <span className={`badge badge-sm ${
                          option.difficulty === 'beginner' ? 'badge-success' :
                          option.difficulty === 'intermediate' ? 'badge-warning' : 'badge-error'
                        }`}>
                          {option.difficulty}
                        </span>
                      </div>
                      <p className="text-sm opacity-80 mb-2">{option.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {option.metrics.map(metric => (
                          <span key={metric} className="badge badge-outline badge-xs">
                            {metric}
                          </span>
                        ))}
                      </div>
                    </div>
                    {isSelected && (
                      <div className="text-xl">✓</div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {selectedGoals.length > 0 && (
          <div className="card-actions justify-end mt-4">
            <button 
              className="btn btn-primary"
              onClick={handleConfirm}
            >
              Continue with Selected Goals ({selectedGoals.length})
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
```

### 2. Roadmap Preview Artifact
Shows a preview of the generated roadmap with content blocks:

```typescript
interface RoadmapPreviewArtifact {
  type: 'roadmap_preview';
  data: {
    title: string;
    goal: string;
    timeline: string;
    viralFormat: string;
    platform: string;
    estimatedTimePerWeek: string;
    blocks: {
      id: string;
      title: string;
      description: string;
      category: 'setup' | 'content_creation' | 'engagement' | 'optimization';
      estimatedTime: string;
      tasks: number;
      viralFormat?: string;
      dependencies?: string[];
    }[];
    successMetrics: string[];
  };
}

export function RoadmapPreviewArtifact({ artifact, onInteraction }: ArtifactProps) {
  const { data } = artifact;
  const categoryColors = {
    setup: 'badge-info',
    content_creation: 'badge-primary',
    engagement: 'badge-secondary',
    optimization: 'badge-accent',
  };
  
  const categoryIcons = {
    setup: '⚙️',
    content_creation: '🎨',
    engagement: '💬',
    optimization: '📈',
  };
  
  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-primary">{data.title}</h2>
        
        {/* Goal Summary */}
        <div className="bg-gradient-to-r from-primary/10 to-secondary/10 p-4 rounded-lg mb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
            <div>
              <span className="font-semibold">🎯 Goal:</span> {data.goal}
            </div>
            <div>
              <span className="font-semibold">⏰ Timeline:</span> {data.timeline}
            </div>
            <div>
              <span className="font-semibold">📱 Platform:</span> {data.platform}
            </div>
            <div>
              <span className="font-semibold">🎬 Format:</span> {data.viralFormat}
            </div>
            <div className="md:col-span-2">
              <span className="font-semibold">⏱️ Time Commitment:</span> {data.estimatedTimePerWeek}
            </div>
          </div>
        </div>
        
        {/* Success Metrics */}
        <div className="mb-4">
          <h3 className="font-semibold mb-2">📊 Success Metrics:</h3>
          <div className="flex flex-wrap gap-2">
            {data.successMetrics.map(metric => (
              <span key={metric} className="badge badge-outline">
                {metric}
              </span>
            ))}
          </div>
        </div>
        
        {/* Roadmap Blocks */}
        <div className="mb-4">
          <h3 className="font-semibold mb-3">🗺️ Your Roadmap ({data.blocks.length} blocks):</h3>
          <div className="space-y-3">
            {data.blocks.map((block, index) => (
              <div key={block.id} className="flex items-start gap-3 p-3 bg-base-200 rounded-lg">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-primary text-primary-content rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-semibold">{block.title}</h4>
                    <span className="text-lg">{categoryIcons[block.category]}</span>
                  </div>
                  <p className="text-sm opacity-70 mb-2">{block.description}</p>
                  <div className="flex flex-wrap gap-2">
                    <span className={`badge badge-sm ${categoryColors[block.category]}`}>
                      {block.category.replace('_', ' ')}
                    </span>
                    <span className="badge badge-outline badge-sm">
                      {block.tasks} tasks
                    </span>
                    <span className="badge badge-outline badge-sm">
                      {block.estimatedTime}
                    </span>
                    {block.viralFormat && (
                      <span className="badge badge-outline badge-sm">
                        {block.viralFormat}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="card-actions justify-between">
          <button 
            className="btn btn-outline"
            onClick={() => onInteraction(artifact.id, 'modify_roadmap', {})}
          >
            🔧 Customize Plan
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => onInteraction(artifact.id, 'accept_roadmap', {})}
          >
            🚀 Start This Roadmap
          </button>
        </div>
      </div>
    </div>
  );
}
```

### 3. Progress Chart Artifact
Visualizes user progress toward their goals:

```typescript
interface ProgressChartArtifact {
  type: 'progress_chart';
  data: {
    title: string;
    metric: string;
    currentValue: number;
    targetValue: number;
    startValue: number;
    timeRemaining: string;
    projectedValue: number;
    isOnTrack: boolean;
    chartData: {
      date: string;
      actual: number;
      projected: number;
      target?: number;
    }[];
    insights: string[];
  };
}

export function ProgressChartArtifact({ artifact, onInteraction }: ArtifactProps) {
  const { data } = artifact;
  const progressPercentage = ((data.currentValue - data.startValue) / (data.targetValue - data.startValue)) * 100;
  const growthRate = data.currentValue - data.startValue;
  const projectedGrowth = data.projectedValue - data.startValue;
  
  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title">{data.title}</h2>
        
        {/* Progress Stats */}
        <div className="stats stats-vertical lg:stats-horizontal shadow mb-4">
          <div className="stat">
            <div className="stat-figure text-primary">
              📊
            </div>
            <div className="stat-title">Current</div>
            <div className="stat-value text-primary text-lg">
              {data.currentValue.toLocaleString()}
            </div>
            <div className="stat-desc">
              +{growthRate.toLocaleString()} from start
            </div>
          </div>
          
          <div className="stat">
            <div className="stat-figure text-secondary">
              🎯
            </div>
            <div className="stat-title">Target</div>
            <div className="stat-value text-secondary text-lg">
              {data.targetValue.toLocaleString()}
            </div>
            <div className="stat-desc">
              {data.timeRemaining} remaining
            </div>
          </div>
          
          <div className="stat">
            <div className="stat-figure">
              {data.isOnTrack ? '📈' : '⚠️'}
            </div>
            <div className="stat-title">Projected</div>
            <div className={`stat-value text-lg ${data.isOnTrack ? 'text-success' : 'text-warning'}`}>
              {data.projectedValue.toLocaleString()}
            </div>
            <div className="stat-desc">
              {data.isOnTrack ? 'On track!' : 'Behind schedule'}
            </div>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm mb-2">
            <span>Progress to Goal</span>
            <span>{Math.max(0, progressPercentage).toFixed(1)}%</span>
          </div>
          <progress 
            className={`progress w-full ${data.isOnTrack ? 'progress-success' : 'progress-warning'}`} 
            value={Math.max(0, progressPercentage)} 
            max="100"
          ></progress>
          <div className="flex justify-between text-xs text-base-content/60 mt-1">
            <span>{data.startValue.toLocaleString()}</span>
            <span>{data.targetValue.toLocaleString()}</span>
          </div>
        </div>
        
        {/* Status Alert */}
        <div className={`alert ${data.isOnTrack ? 'alert-success' : 'alert-warning'} mb-4`}>
          <div className="flex-shrink-0">
            {data.isOnTrack ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            )}
          </div>
          <div>
            <h3 className="font-bold">
              {data.isOnTrack ? 'Great Progress!' : 'Needs Attention'}
            </h3>
            <div className="text-sm">
              {data.isOnTrack 
                ? `You're on track to reach your goal! Keep up the great work.`
                : `You're currently behind schedule. Consider adjusting your strategy.`
              }
            </div>
          </div>
        </div>
        
        {/* Insights */}
        {data.insights.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2">💡 Insights:</h3>
            <ul className="space-y-1">
              {data.insights.map((insight, index) => (
                <li key={index} className="text-sm flex items-start gap-2">
                  <span className="text-primary">•</span>
                  {insight}
                </li>
              ))}
            </ul>
          </div>
        )}
        
        <div className="card-actions justify-end">
          {!data.isOnTrack && (
            <button 
              className="btn btn-warning btn-sm"
              onClick={() => onInteraction(artifact.id, 'request_strategy_adjustment', {})}
            >
              Get Strategy Help
            </button>
          )}
          <button 
            className="btn btn-primary btn-sm"
            onClick={() => onInteraction(artifact.id, 'update_progress', {})}
          >
            Update Progress
          </button>
        </div>
      </div>
    </div>
  );
}
```

### 4. Content Creation Assistant Artifact
Helps users create content based on viral formats:

```typescript
interface ContentCreationArtifact {
  type: 'content_creation_assistant';
  data: {
    viralFormat: string;
    topic: string;
    platform: string;
    template: {
      structure: string[];
      tips: string[];
      examples: string[];
      hooks: string[];
    };
    userContent?: {
      title: string;
      description: string;
      script: string;
      hashtags: string[];
    };
  };
}

export function ContentCreationArtifact({ artifact, onInteraction }: ArtifactProps) {
  const [userContent, setUserContent] = useState(artifact.data.userContent || {
    title: '',
    description: '',
    script: '',
    hashtags: [],
  });
  
  const [activeTab, setActiveTab] = useState('create');
  
  const handleContentChange = (field: string, value: any) => {
    const newContent = { ...userContent, [field]: value };
    setUserContent(newContent);
    onInteraction(artifact.id, 'content_updated', { content: newContent });
  };
  
  const handleHashtagAdd = (hashtag: string) => {
    if (hashtag && !userContent.hashtags.includes(hashtag)) {
      const newHashtags = [...userContent.hashtags, hashtag];
      handleContentChange('hashtags', newHashtags);
    }
  };
  
  const handleHashtagRemove = (hashtag: string) => {
    const newHashtags = userContent.hashtags.filter(h => h !== hashtag);
    handleContentChange('hashtags', newHashtags);
  };
  
  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-primary">Content Creation Assistant</h2>
        <p className="text-sm opacity-70 mb-4">
          Creating {artifact.data.viralFormat} content about {artifact.data.topic} for {artifact.data.platform}
        </p>
        
        {/* Tabs */}
        <div className="tabs tabs-boxed mb-4">
          <a 
            className={`tab ${activeTab === 'create' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('create')}
          >
            Create
          </a>
          <a 
            className={`tab ${activeTab === 'guide' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('guide')}
          >
            Guide
          </a>
          <a 
            className={`tab ${activeTab === 'examples' ? 'tab-active' : ''}`}
            onClick={() => setActiveTab('examples')}
          >
            Examples
          </a>
        </div>
        
        {/* Create Tab */}
        {activeTab === 'create' && (
          <div className="space-y-4">
            <div className="form-control">
              <label className="label">
                <span className="label-text font-semibold">Title/Hook</span>
              </label>
              <input 
                type="text" 
                className="input input-bordered" 
                value={userContent.title}
                onChange={(e) => handleContentChange('title', e.target.value)}
                placeholder="Enter your catchy title or hook..."
              />
            </div>
            
            <div className="form-control">
              <label className="label">
                <span className="label-text font-semibold">Description</span>
              </label>
              <textarea 
                className="textarea textarea-bordered h-20" 
                value={userContent.description}
                onChange={(e) => handleContentChange('description', e.target.value)}
                placeholder="Describe your content idea..."
              />
            </div>
            
            <div className="form-control">
              <label className="label">
                <span className="label-text font-semibold">Script/Content</span>
              </label>
              <textarea 
                className="textarea textarea-bordered h-32" 
                value={userContent.script}
                onChange={(e) => handleContentChange('script', e.target.value)}
                placeholder="Write your content script or outline..."
              />
            </div>
            
            <div className="form-control">
              <label className="label">
                <span className="label-text font-semibold">Hashtags</span>
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {userContent.hashtags.map(hashtag => (
                  <span key={hashtag} className="badge badge-primary gap-2">
                    #{hashtag}
                    <button 
                      className="btn btn-ghost btn-xs"
                      onClick={() => handleHashtagRemove(hashtag)}
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input 
                type="text" 
                className="input input-bordered" 
                placeholder="Add hashtag (without #)"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleHashtagAdd(e.currentTarget.value);
                    e.currentTarget.value = '';
                  }
                }}
              />
            </div>
          </div>
        )}
        
        {/* Guide Tab */}
        {activeTab === 'guide' && (
          <div className="space-y-4">
            <div className="bg-base-200 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">📋 {artifact.data.viralFormat} Structure:</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                {artifact.data.template.structure.map((step, index) => (
                  <li key={index}>{step}</li>
                ))}
              </ol>
            </div>
            
            <div className="bg-info/10 p-4 rounded-lg">
              <h4 className="font-semibold text-info mb-2">💡 Pro Tips:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                {artifact.data.template.tips.map((tip, index) => (
                  <li key={index}>{tip}</li>
                ))}
              </ul>
            </div>
            
            <div className="bg-success/10 p-4 rounded-lg">
              <h4 className="font-semibold text-success mb-2">🎣 Hook Ideas:</h4>
              <div className="space-y-2">
                {artifact.data.template.hooks.map((hook, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="text-sm bg-base-100 p-2 rounded flex-1">"{hook}"</span>
                    <button 
                      className="btn btn-xs btn-outline"
                      onClick={() => handleContentChange('title', hook)}
                    >
                      Use
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {/* Examples Tab */}
        {activeTab === 'examples' && (
          <div className="space-y-4">
            <h3 className="font-semibold">🌟 Successful Examples:</h3>
            {artifact.data.template.examples.map((example, index) => (
              <div key={index} className="bg-base-200 p-4 rounded-lg">
                <p className="text-sm">{example}</p>
              </div>
            ))}
          </div>
        )}
        
        <div className="card-actions justify-end mt-6">
          <button 
            className="btn btn-outline"
            onClick={() => onInteraction(artifact.id, 'get_ai_feedback', { content: userContent })}
          >
            Get AI Feedback
          </button>
          <button 
            className="btn btn-secondary"
            onClick={() => onInteraction(artifact.id, 'generate_variations', { content: userContent })}
          >
            Generate Variations
          </button>
          <button 
            className="btn btn-primary"
            onClick={() => onInteraction(artifact.id, 'save_content', { content: userContent })}
          >
            Save Content
          </button>
        </div>
      </div>
    </div>
  );
}
```

## Artifact Component Router
Create a router component to render different artifact types:

```typescript
export function ArtifactComponent({ 
  artifact, 
  onInteraction 
}: { 
  artifact: ArtifactData; 
  onInteraction: (artifactId: string, action: string, data: any) => void;
}) {
  switch (artifact.type) {
    case 'goal_selector':
      return <GoalSelectorArtifact artifact={artifact} onInteraction={onInteraction} />;
    case 'roadmap_preview':
      return <RoadmapPreviewArtifact artifact={artifact} onInteraction={onInteraction} />;
    case 'progress_chart':
      return <ProgressChartArtifact artifact={artifact} onInteraction={onInteraction} />;
    case 'content_creation_assistant':
      return <ContentCreationArtifact artifact={artifact} onInteraction={onInteraction} />;
    default:
      return (
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h2 className="card-title">Unknown Artifact</h2>
            <p>Artifact type "{artifact.type}" is not supported.</p>
          </div>
        </div>
      );
  }
}
```

## Artifact Interaction Handler
Handle artifact interactions and update the conversation:

```typescript
export class ArtifactInteractionService {
  async handleInteraction(
    artifactId: string,
    action: string,
    data: any,
    conversationId: number
  ): Promise<void> {
    // Get the artifact
    const artifact = await db.query.artifacts.findFirst({
      where: eq(artifacts.id, parseInt(artifactId)),
    });
    
    if (!artifact) {
      throw new Error('Artifact not found');
    }
    
    // Handle different interaction types
    switch (action) {
      case 'goal_selection_changed':
        await this.handleGoalSelectionChanged(artifact, data);
        break;
      case 'goals_confirmed':
        await this.handleGoalsConfirmed(artifact, data, conversationId);
        break;
      case 'accept_roadmap':
        await this.handleRoadmapAccepted(artifact, data, conversationId);
        break;
      case 'modify_roadmap':
        await this.handleRoadmapModification(artifact, data, conversationId);
        break;
      case 'update_progress':
        await this.handleProgressUpdate(artifact, data, conversationId);
        break;
      case 'save_content':
        await this.handleContentSaved(artifact, data, conversationId);
        break;
      default:
        console.warn(`Unknown artifact interaction: ${action}`);
    }
    
    // Log the interaction
    await db.update(artifacts)
      .set({
        userInteractions: sql`${artifacts.userInteractions} || ${JSON.stringify([{
          action,
          data,
          timestamp: new Date().toISOString(),
        }])}`,
        updatedAt: new Date(),
      })
      .where(eq(artifacts.id, parseInt(artifactId)));
  }
  
  private async handleGoalsConfirmed(
    artifact: any,
    data: any,
    conversationId: number
  ): Promise<void> {
    // Update conversation with selected goals
    await db.update(conversations)
      .set({
        extractedData: sql`${conversations.extractedData} || ${JSON.stringify({
          selectedGoals: data.selectedGoals,
        })}`,
        currentStep: 'roadmap_creation',
        updatedAt: new Date(),
      })
      .where(eq(conversations.id, conversationId));
    
    // Generate AI response about next steps
    const chatService = new AIChatService();
    await chatService.processMessage(
      conversationId,
      `I've selected my goals: ${data.selectedGoals.join(', ')}. What's next?`
    );
  }
  
  private async handleRoadmapAccepted(
    artifact: any,
    data: any,
    conversationId: number
  ): Promise<void> {
    // Create the actual roadmap in the database
    const conversation = await db.query.conversations.findFirst({
      where: eq(conversations.id, conversationId),
    });
    
    if (!conversation) return;
    
    // Create roadmap and content blocks
    const roadmap = await this.createRoadmapFromArtifact(
      conversation.userId,
      artifact.data
    );
    
    // Update conversation status
    await db.update(conversations)
      .set({
        currentStep: 'mentoring',
        completionPercentage: 100,
        updatedAt: new Date(),
      })
      .where(eq(conversations.id, conversationId));
    
    // Generate congratulatory message
    const chatService = new AIChatService();
    await chatService.processMessage(
      conversationId,
      "Perfect! I've created your personalized roadmap. Let's start with the first block!"
    );
  }
  
  // Additional interaction handlers...
}
```

## Multilanguage Support
Implement translations for artifacts:

```typescript
// Example translation keys
export const artifactTranslations = {
  en: {
    "artifact.goal_selector.title": "Select Your Goals",
    "artifact.goal_selector.max_selections": "Select up to {max} goals",
    "artifact.roadmap.title": "Your Personalized Roadmap",
    "artifact.progress.title": "Progress Tracking",
    "artifact.content.title": "Content Creation Assistant",
    // Additional translations...
  },
  es: {
    "artifact.goal_selector.title": "Selecciona Tus Objetivos",
    "artifact.goal_selector.max_selections": "Selecciona hasta {max} objetivos",
    "artifact.roadmap.title": "Tu Hoja de Ruta Personalizada",
    "artifact.progress.title": "Seguimiento de Progreso",
    "artifact.content.title": "Asistente de Creación de Contenido",
    // Additional translations...
  }
};
```

## Additional Requirements
- Implement artifact state persistence
- Create smooth animations for artifact updates
- Add artifact export functionality
- Implement artifact sharing capabilities
- Create artifact templates for different use cases
- Add artifact versioning and history
- Implement responsive design for all artifacts
- Create artifact performance analytics
- Add artifact customization options
- Implement artifact collaboration features

