'use client';

import { useState, useRef, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isStreaming?: boolean;
}

interface ChatInterfaceProps {
  conversationId?: string;
  onConversationIdChange?: (id: string) => void;
}

export default function ChatInterface({ conversationId, onConversationIdChange }: ChatInterfaceProps) {
  const { user } = useUser();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentStreamingId, setCurrentStreamingId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
    };

    const assistantMessageId = (Date.now() + 1).toString();
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true,
    };

    setMessages(prev => [...prev, userMessage, assistantMessage]);
    setInput('');
    setIsLoading(true);
    setCurrentStreamingId(assistantMessageId);

    // Create abort controller for this request
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch('/api/ai/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          conversationId,
          platform: 'instagram', // TODO: Get from user profile
          followerCount: 1000, // TODO: Get from user profile
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Get conversation ID from headers
      const newConversationId = response.headers.get('X-Conversation-Id');
      if (newConversationId && newConversationId !== conversationId) {
        onConversationIdChange?.(newConversationId);
      }

      // Get additional metadata from headers
      const aiProvider = response.headers.get('X-AI-Provider');
      const userIntent = response.headers.get('X-User-Intent');
      const platform = response.headers.get('X-Platform');
      const urgency = response.headers.get('X-Urgency');

      console.log('[Influtify] Response metadata:', {
        provider: aiProvider,
        intent: userIntent,
        platform,
        urgency,
      });

      // Process streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let accumulatedContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        accumulatedContent += chunk;

        // Update the streaming message
        setMessages(prev =>
          prev.map(msg =>
            msg.id === assistantMessageId
              ? { ...msg, content: accumulatedContent }
              : msg
          )
        );
      }

      // Mark streaming as complete
      setMessages(prev =>
        prev.map(msg =>
          msg.id === assistantMessageId
            ? { ...msg, isStreaming: false }
            : msg
        )
      );

    } catch (error) {
      console.error('[Influtify] Chat error:', error);
      
      // Update the assistant message with error
      setMessages(prev =>
        prev.map(msg =>
          msg.id === assistantMessageId
            ? {
                ...msg,
                content: 'Sorry, I encountered an error. Please try again.',
                isStreaming: false,
              }
            : msg
        )
      );
    } finally {
      setIsLoading(false);
      setCurrentStreamingId(null);
      abortControllerRef.current = null;
    }
  };

  const handleStop = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsLoading(false);
      setCurrentStreamingId(null);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Chat Messages Area */}
      <div className="flex-1 p-6 overflow-y-auto bg-gradient-to-b from-base-50 to-base-100">
        <div className="space-y-6 max-w-4xl">
          {/* Welcome Message */}
          <div className="chat chat-start">
            <div className="chat-image avatar">
              <div className="w-12 rounded-full bg-gradient-to-br from-primary to-secondary text-white flex items-center justify-center shadow-lg">
                🤖
              </div>
            </div>
            <div className="chat-bubble chat-bubble-primary shadow-lg max-w-lg">
              <div className="font-medium mb-2">Welcome to Influtify! 🚀</div>
              I'm your AI social media growth mentor. I'll help you build your presence using proven viral strategies from Brendan Kane's methods.
              <br /><br />
              <div className="bg-primary/10 rounded-lg p-3 mt-3">
                <div className="font-medium text-primary-content/90 mb-2">To get started, tell me:</div>
                <div className="space-y-1 text-sm">
                  <div>• What platform do you want to focus on?</div>
                  <div>• What's your current follower count?</div>
                  <div>• What type of content do you create?</div>
                </div>
              </div>
            </div>
          </div>

          {/* Dynamic Messages */}
          {messages.map((message) => (
            <div key={message.id} className={`chat ${message.role === 'user' ? 'chat-end' : 'chat-start'}`}>
              <div className="chat-image avatar">
                <div className={`w-10 rounded-full flex items-center justify-center shadow-md ${
                  message.role === 'user' 
                    ? 'bg-gradient-to-br from-accent to-accent-focus text-white' 
                    : 'bg-gradient-to-br from-primary to-secondary text-white'
                }`}>
                  {message.role === 'user' ? (user?.firstName?.[0] || '👤') : '🤖'}
                </div>
              </div>
              <div className={`chat-bubble shadow-lg max-w-lg ${
                message.role === 'user' ? 'chat-bubble-accent' : 'chat-bubble-primary'
              }`}>
                <div className="whitespace-pre-wrap">
                  {message.content}
                  {message.isStreaming && (
                    <span className="inline-block w-2 h-5 bg-current ml-1 animate-pulse" />
                  )}
                </div>
              </div>
              <div className="chat-footer opacity-50 text-xs">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          ))}
          
          <div ref={messagesEndRef} />
        </div>
      </div>
      
      {/* Chat Input */}
      <div className="bg-base-100 p-6 border-t border-base-200">
        <form onSubmit={handleSubmit} className="flex gap-3">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Type your message..."
            className="input input-bordered flex-1 shadow-sm focus:shadow-md transition-shadow"
            disabled={isLoading}
          />
          {isLoading ? (
            <button 
              type="button"
              onClick={handleStop}
              className="btn btn-error shadow-lg hover:shadow-xl transition-all gap-2"
            >
              <span>Stop</span>
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            </button>
          ) : (
            <button 
              type="submit"
              className="btn btn-primary shadow-lg hover:shadow-xl transition-all gap-2"
              disabled={!input.trim()}
            >
              <span>Send</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          )}
        </form>
        {isLoading && (
          <div className="flex items-center gap-2 mt-3">
            <div className="badge badge-primary badge-sm">🤖</div>
            <p className="text-xs text-base-content/70">AI mentor is thinking...</p>
          </div>
        )}
      </div>
    </div>
  );
}
