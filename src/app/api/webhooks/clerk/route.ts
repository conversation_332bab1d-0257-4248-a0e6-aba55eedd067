import { headers } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { Webhook } from 'svix';
import { db } from '@/lib/db';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

const webhookSecret = process.env.CLERK_WEBHOOK_SECRET;

export async function POST(req: NextRequest) {
  if (!webhookSecret) {
    console.error('CLERK_WEBHOOK_SECRET is missing from environment variables');
    return new Response('Webhook secret not configured', { status: 500 });
  }

  if (webhookSecret.includes('REPLACE') || webhookSecret.includes('your_')) {
    console.error('CLERK_WEBHOOK_SECRET contains placeholder value');
    return new Response('Webhook secret not properly configured', { status: 500 });
  }

  // Get the headers
  const headerPayload = await headers();
  const svix_id = headerPayload.get('svix-id');
  const svix_timestamp = headerPayload.get('svix-timestamp');
  const svix_signature = headerPayload.get('svix-signature');

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error occured -- no svix headers', {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your secret.
  const wh = new Webhook(webhookSecret);

  let evt: any;

  // Verify the payload with the headers
  try {
    evt = wh.verify(body, {
      'svix-id': svix_id,
      'svix-timestamp': svix_timestamp,
      'svix-signature': svix_signature,
    }) as any;
  } catch (err) {
    console.error('Error verifying webhook:', err);
    return new Response('Error occured', {
      status: 400,
    });
  }

  // Handle the webhook
  const eventType = evt.type;
  
  try {
    switch (eventType) {
      case 'user.created':
        await handleUserCreated(evt.data);
        break;
      case 'user.updated':
        await handleUserUpdated(evt.data);
        break;
      case 'user.deleted':
        await handleUserDeleted(evt.data);
        break;
      default:
        console.log(`Unhandled event type: ${eventType}`);
    }

    return NextResponse.json({ message: 'Webhook processed successfully' });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return new Response('Error processing webhook', { status: 500 });
  }
}

async function handleUserCreated(userData: any) {
  const { id, email_addresses, first_name, last_name, image_url, created_at } = userData;

  const primaryEmail = email_addresses.find((email: any) => email.id === userData.primary_email_address_id);

  await db.insert(users).values({
    id: id,
    email: primaryEmail?.email_address || '',
    firstName: first_name || '',
    lastName: last_name || '',
    imageUrl: image_url || '',
    createdAt: new Date(created_at),
    updatedAt: new Date(),
  });

  console.log(`User created: ${id}`);
}

async function handleUserUpdated(userData: any) {
  const { id, email_addresses, first_name, last_name, image_url } = userData;

  const primaryEmail = email_addresses.find((email: any) => email.id === userData.primary_email_address_id);

  await db
    .update(users)
    .set({
      email: primaryEmail?.email_address || '',
      firstName: first_name || '',
      lastName: last_name || '',
      imageUrl: image_url || '',
      updatedAt: new Date(),
    })
    .where(eq(users.id, id));

  console.log(`User updated: ${id}`);
}

async function handleUserDeleted(userData: any) {
  const { id } = userData;

  await db.delete(users).where(eq(users.id, id));

  console.log(`User deleted: ${id}`);
}
