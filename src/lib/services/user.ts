import { db } from '@/lib/db';
import { users, userStats, achievements, userAchievements } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { auth } from '@clerk/nextjs/server';

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  imageUrl: string;
  platforms: Record<string, any>;
  goals: string[];
  currentFollowers: number;
  targetFollowers: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserStatsData {
  totalPoints: number;
  currentStreak: number;
  longestStreak: number;
  level: number;
  completedBlocks: number;
  totalSessions: number;
  lastActiveAt: Date;
}

export class UserService {
  /**
   * Get the current authenticated user's profile
   */
  static async getCurrentUser(): Promise<UserProfile | null> {
    const { userId } = await auth();
    if (!userId) return null;

    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    return user[0] || null;
  }

  /**
   * Get user by Clerk ID
   */
  static async getUserByClerkId(clerkId: string): Promise<UserProfile | null> {
    const user = await db
      .select()
      .from(users)
      .where(eq(users.id, clerkId))
      .limit(1);

    return user[0] || null;
  }

  /**
   * Create or update user profile
   */
  static async upsertUser(userData: Partial<UserProfile>): Promise<UserProfile> {
    const existingUser = await this.getUserByClerkId(userData.id!);

    if (existingUser) {
      // Update existing user
      const updated = await db
        .update(users)
        .set({
          ...userData,
          updatedAt: new Date(),
        })
        .where(eq(users.id, userData.id!))
        .returning();

      return updated[0];
    } else {
      // Create new user
      const created = await db
        .insert(users)
        .values({
          ...userData,
          createdAt: new Date(),
          updatedAt: new Date(),
        } as any)
        .returning();

      // Initialize user stats
      await this.initializeUserStats(created[0].id);

      return created[0];
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(
    clerkId: string,
    updates: Partial<Pick<UserProfile, 'platforms' | 'goals' | 'currentFollowers' | 'targetFollowers'>>
  ): Promise<UserProfile | null> {
    const updated = await db
      .update(users)
      .set({
        ...updates,
        updatedAt: new Date(),
      })
      .where(eq(users.clerkId, clerkId))
      .returning();

    return updated[0] || null;
  }

  /**
   * Get user statistics
   */
  static async getUserStats(userId: string): Promise<UserStatsData | null> {
    const stats = await db
      .select()
      .from(userStats)
      .where(eq(userStats.userId, userId))
      .limit(1);

    return stats[0] || null;
  }

  /**
   * Initialize user statistics for new user
   */
  static async initializeUserStats(userId: string): Promise<void> {
    await db.insert(userStats).values({
      userId,
      totalPoints: 0,
      currentStreak: 0,
      longestStreak: 0,
      level: 1,
      completedBlocks: 0,
      totalSessions: 0,
      lastActiveAt: new Date(),
    });
  }

  /**
   * Update user statistics
   */
  static async updateStats(
    userId: string,
    updates: Partial<UserStatsData>
  ): Promise<UserStatsData | null> {
    const updated = await db
      .update(userStats)
      .set(updates)
      .where(eq(userStats.userId, userId))
      .returning();

    return updated[0] || null;
  }

  /**
   * Award achievement to user
   */
  static async awardAchievement(userId: string, achievementId: string): Promise<void> {
    // Check if user already has this achievement
    const existing = await db
      .select()
      .from(userAchievements)
      .where(
        and(
          eq(userAchievements.userId, userId),
          eq(userAchievements.achievementId, achievementId)
        )
      )
      .limit(1);

    if (existing.length === 0) {
      await db.insert(userAchievements).values({
        userId,
        achievementId,
        unlockedAt: new Date(),
      });
    }
  }

  /**
   * Get user achievements
   */
  static async getUserAchievements(userId: string) {
    return await db
      .select({
        achievement: achievements,
        unlockedAt: userAchievements.unlockedAt,
      })
      .from(userAchievements)
      .innerJoin(achievements, eq(achievements.id, userAchievements.achievementId))
      .where(eq(userAchievements.userId, userId));
  }

  /**
   * Add points to user and check for level up
   */
  static async addPoints(userId: string, points: number): Promise<{ leveledUp: boolean; newLevel?: number }> {
    const currentStats = await this.getUserStats(userId);
    if (!currentStats) throw new Error('User stats not found');

    const newPoints = currentStats.totalPoints + points;
    const newLevel = Math.floor(newPoints / 1000) + 1; // 1000 points per level
    const leveledUp = newLevel > currentStats.level;

    await this.updateStats(userId, {
      totalPoints: newPoints,
      level: newLevel,
      lastActiveAt: new Date(),
    });

    return { leveledUp, newLevel: leveledUp ? newLevel : undefined };
  }
}
