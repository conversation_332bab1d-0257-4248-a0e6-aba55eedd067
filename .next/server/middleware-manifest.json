{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_351a010a._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_466025e5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "yWXTVRnPejRJaZdiuSfKs/mOHwU6vBt0/hisfirdddo=", "__NEXT_PREVIEW_MODE_ID": "6561b34d366d1f8e50d149b9729e2037", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c3a2ce28f7b9452ab5bf674c2fa4365b9697036725be54cbe29cfe952a164981", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ca1a26661181e2c4307ccc1bbcff8b967c62ed98fd8881b1eade73c702fddba0"}}}, "sortedMiddleware": ["/"], "functions": {}}