{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_351a010a._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_466025e5.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "yWXTVRnPejRJaZdiuSfKs/mOHwU6vBt0/hisfirdddo=", "__NEXT_PREVIEW_MODE_ID": "9f28cbd34645518481a249f7b0aae6b4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b47d631a046edecf1c22c7dfad7832f4565c8fff88687f2c2fe9d6e4e67433b9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "71d9212a41d534cabe32617814ec0ba7ffc8dc9e42cbe6eda4e2868505d73b92"}}}, "sortedMiddleware": ["/"], "functions": {}}