import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import ChatInterface from '../../components/chat/ChatInterface';

export default async function ChatPage() {
  const { userId } = await auth();

  if (!userId) {
    redirect('/sign-in');
  }

  return (
    <div className="min-h-screen bg-base-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-primary mb-2">
              AI Mentor Chat
            </h1>
            <p className="text-base-content/70">
              Get personalized guidance to grow your social media presence
            </p>
          </div>
          
          <ChatInterface />
        </div>
      </div>
    </div>
  );
}
