{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/node_modules/%40clerk/nextjs/dist/esm/app-router/keyless-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies, headers } from \"next/headers\";\nimport { redirect, RedirectType } from \"next/navigation\";\nimport { errorThrower } from \"../server/errorThrower\";\nimport { detectClerkMiddleware } from \"../server/headers-utils\";\nimport { getKeylessCookieName, getKeylessCookieValue } from \"../server/keyless\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nconst keylessCookieConfig = {\n  secure: false,\n  httpOnly: false,\n  sameSite: \"lax\"\n};\nasync function syncKeylessConfigAction(args) {\n  const { claimUrl, publishableKey, secretKey, returnUrl } = args;\n  const cookieStore = await cookies();\n  const request = new Request(\"https://placeholder.com\", { headers: await headers() });\n  const keyless = await getKeylessCookieValue((name) => {\n    var _a;\n    return (_a = cookieStore.get(name)) == null ? void 0 : _a.value;\n  });\n  const pksMatch = (keyless == null ? void 0 : keyless.publishableKey) === publishableKey;\n  const sksMatch = (keyless == null ? void 0 : keyless.secretKey) === secretKey;\n  if (pksMatch && sksMatch) {\n    return;\n  }\n  cookieStore.set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  if (detectClerkMiddleware(request)) {\n    redirect(`/clerk-sync-keyless?returnUrl=${returnUrl}`, RedirectType.replace);\n    return;\n  }\n  return;\n}\nasync function createOrReadKeylessAction() {\n  if (!canUseKeyless) {\n    return null;\n  }\n  const result = await import(\"../server/keyless-node.js\").then((m) => m.createOrReadKeyless()).catch(() => null);\n  if (!result) {\n    errorThrower.throwMissingPublishableKeyError();\n    return null;\n  }\n  const { clerkDevelopmentCache, createKeylessModeMessage } = await import(\"../server/keyless-log-cache.js\");\n  clerkDevelopmentCache == null ? void 0 : clerkDevelopmentCache.log({\n    cacheKey: result.publishableKey,\n    msg: createKeylessModeMessage(result)\n  });\n  const { claimUrl, publishableKey, secretKey, apiKeysUrl } = result;\n  void (await cookies()).set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  return {\n    claimUrl,\n    publishableKey,\n    apiKeysUrl\n  };\n}\nasync function deleteKeylessAction() {\n  if (!canUseKeyless) {\n    return;\n  }\n  await import(\"../server/keyless-node.js\").then((m) => m.removeKeyless()).catch(() => {\n  });\n  return;\n}\nexport {\n  createOrReadKeylessAction,\n  deleteKeylessAction,\n  syncKeylessConfigAction\n};\n"], "names": [], "mappings": ";;;;;;IAuEE,4BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/node_modules/%40clerk/nextjs/dist/esm/app-router/keyless-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies, headers } from \"next/headers\";\nimport { redirect, RedirectType } from \"next/navigation\";\nimport { errorThrower } from \"../server/errorThrower\";\nimport { detectClerkMiddleware } from \"../server/headers-utils\";\nimport { getKeylessCookieName, getKeylessCookieValue } from \"../server/keyless\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nconst keylessCookieConfig = {\n  secure: false,\n  httpOnly: false,\n  sameSite: \"lax\"\n};\nasync function syncKeylessConfigAction(args) {\n  const { claimUrl, publishableKey, secretKey, returnUrl } = args;\n  const cookieStore = await cookies();\n  const request = new Request(\"https://placeholder.com\", { headers: await headers() });\n  const keyless = await getKeylessCookieValue((name) => {\n    var _a;\n    return (_a = cookieStore.get(name)) == null ? void 0 : _a.value;\n  });\n  const pksMatch = (keyless == null ? void 0 : keyless.publishableKey) === publishableKey;\n  const sksMatch = (keyless == null ? void 0 : keyless.secretKey) === secretKey;\n  if (pksMatch && sksMatch) {\n    return;\n  }\n  cookieStore.set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  if (detectClerkMiddleware(request)) {\n    redirect(`/clerk-sync-keyless?returnUrl=${returnUrl}`, RedirectType.replace);\n    return;\n  }\n  return;\n}\nasync function createOrReadKeylessAction() {\n  if (!canUseKeyless) {\n    return null;\n  }\n  const result = await import(\"../server/keyless-node.js\").then((m) => m.createOrReadKeyless()).catch(() => null);\n  if (!result) {\n    errorThrower.throwMissingPublishableKeyError();\n    return null;\n  }\n  const { clerkDevelopmentCache, createKeylessModeMessage } = await import(\"../server/keyless-log-cache.js\");\n  clerkDevelopmentCache == null ? void 0 : clerkDevelopmentCache.log({\n    cacheKey: result.publishableKey,\n    msg: createKeylessModeMessage(result)\n  });\n  const { claimUrl, publishableKey, secretKey, apiKeysUrl } = result;\n  void (await cookies()).set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  return {\n    claimUrl,\n    publishableKey,\n    apiKeysUrl\n  };\n}\nasync function deleteKeylessAction() {\n  if (!canUseKeyless) {\n    return;\n  }\n  await import(\"../server/keyless-node.js\").then((m) => m.removeKeyless()).catch(() => {\n  });\n  return;\n}\nexport {\n  createOrReadKeylessAction,\n  deleteKeylessAction,\n  syncKeylessConfigAction\n};\n"], "names": [], "mappings": ";;;;;;IAwEE,sBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/node_modules/%40clerk/nextjs/dist/esm/app-router/keyless-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies, headers } from \"next/headers\";\nimport { redirect, RedirectType } from \"next/navigation\";\nimport { errorThrower } from \"../server/errorThrower\";\nimport { detectClerkMiddleware } from \"../server/headers-utils\";\nimport { getKeylessCookieName, getKeylessCookieValue } from \"../server/keyless\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nconst keylessCookieConfig = {\n  secure: false,\n  httpOnly: false,\n  sameSite: \"lax\"\n};\nasync function syncKeylessConfigAction(args) {\n  const { claimUrl, publishableKey, secretKey, returnUrl } = args;\n  const cookieStore = await cookies();\n  const request = new Request(\"https://placeholder.com\", { headers: await headers() });\n  const keyless = await getKeylessCookieValue((name) => {\n    var _a;\n    return (_a = cookieStore.get(name)) == null ? void 0 : _a.value;\n  });\n  const pksMatch = (keyless == null ? void 0 : keyless.publishableKey) === publishableKey;\n  const sksMatch = (keyless == null ? void 0 : keyless.secretKey) === secretKey;\n  if (pksMatch && sksMatch) {\n    return;\n  }\n  cookieStore.set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  if (detectClerkMiddleware(request)) {\n    redirect(`/clerk-sync-keyless?returnUrl=${returnUrl}`, RedirectType.replace);\n    return;\n  }\n  return;\n}\nasync function createOrReadKeylessAction() {\n  if (!canUseKeyless) {\n    return null;\n  }\n  const result = await import(\"../server/keyless-node.js\").then((m) => m.createOrReadKeyless()).catch(() => null);\n  if (!result) {\n    errorThrower.throwMissingPublishableKeyError();\n    return null;\n  }\n  const { clerkDevelopmentCache, createKeylessModeMessage } = await import(\"../server/keyless-log-cache.js\");\n  clerkDevelopmentCache == null ? void 0 : clerkDevelopmentCache.log({\n    cacheKey: result.publishableKey,\n    msg: createKeylessModeMessage(result)\n  });\n  const { claimUrl, publishableKey, secretKey, apiKeysUrl } = result;\n  void (await cookies()).set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  return {\n    claimUrl,\n    publishableKey,\n    apiKeysUrl\n  };\n}\nasync function deleteKeylessAction() {\n  if (!canUseKeyless) {\n    return;\n  }\n  await import(\"../server/keyless-node.js\").then((m) => m.removeKeyless()).catch(() => {\n  });\n  return;\n}\nexport {\n  createOrReadKeylessAction,\n  deleteKeylessAction,\n  syncKeylessConfigAction\n};\n"], "names": [], "mappings": ";;;;;;IAyEE,0BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "ignoreList": [0], "debugId": null}}]}