import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { generateInflutifyMentorResponse } from '../../../../lib/services/ai-provider';
import { conversationService } from '../../../../lib/services/conversation';
import { UserService } from '../../../../lib/services/user';
import { z } from 'zod';

const chatRequestSchema = z.object({
  message: z.string().min(1, 'Message cannot be empty'),
  conversationId: z.coerce.number().optional(),
});

export async function GET() {
  return NextResponse.json({ message: 'AI stream API is working' });
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { message, conversationId } = chatRequestSchema.parse(body);

    // Get user profile for context
    const userProfile = await UserService.getCurrentUser();

    // Get conversation history for context
    let conversationHistory: any[] = [];
    if (conversationId) {
      conversationHistory = await conversationService.getConversationHistory(conversationId);
    }

    // Build conversation context
    const context = {
      userId,
      conversationId,
      userProfile,
      currentGoals: userProfile?.goals || [],
      conversationHistory,
    };

    // Create or get conversation ID
    let finalConversationId = conversationId;
    if (!finalConversationId) {
      finalConversationId = await conversationService.createConversation(userId, 'Chat Session');
    }

    // Save user message
    await conversationService.saveMessage(finalConversationId, 'user', message);

    // Generate AI response stream
    const aiResponseStream = await generateInflutifyMentorResponse(message, context);

    // Create a new stream that handles the AI response and saves it
    const stream = new ReadableStream({
      async start(controller) {
        try {
          const reader = aiResponseStream.getReader();
          let fullResponse = '';

          // Send conversation ID first
          controller.enqueue(
            new TextEncoder().encode(JSON.stringify({
              type: 'conversation_id',
              conversationId: finalConversationId
            }) + '\n')
          );

          // Stream the AI response
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = new TextDecoder().decode(value);
            fullResponse += chunk;

            // Forward the chunk to the client
            controller.enqueue(
              new TextEncoder().encode(JSON.stringify({
                type: 'content',
                content: chunk
              }) + '\n')
            );
          }

          // Save assistant message
          await conversationService.saveMessage(finalConversationId, 'assistant', fullResponse);

          controller.close();

        } catch (error) {
          console.error('AI streaming error:', error);
          const errorResponse = {
            type: 'error',
            error: 'Failed to generate response',
            details: error instanceof Error ? error.message : 'Unknown error'
          };
          controller.enqueue(
            new TextEncoder().encode(JSON.stringify(errorResponse) + '\n')
          );
          controller.close();
        }
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'X-Conversation-Id': finalConversationId.toString(),
      },
    });

  } catch (error) {
    console.error('AI stream API error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
