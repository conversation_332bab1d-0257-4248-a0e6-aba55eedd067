/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai/stream/route";
exports.ids = ["app/api/ai/stream/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fstream%2Froute&page=%2Fapi%2Fai%2Fstream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fstream%2Froute.ts&appDir=%2FUsers%2Fbrunao%2FPROJECTS%2Finflutify%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbrunao%2FPROJECTS%2Finflutify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fstream%2Froute&page=%2Fapi%2Fai%2Fstream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fstream%2Froute.ts&appDir=%2FUsers%2Fbrunao%2FPROJECTS%2Finflutify%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbrunao%2FPROJECTS%2Finflutify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_brunao_PROJECTS_influtify_src_app_api_ai_stream_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai/stream/route.ts */ \"(rsc)/./src/app/api/ai/stream/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Users_brunao_PROJECTS_influtify_src_app_api_ai_stream_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\n_Users_brunao_PROJECTS_influtify_src_app_api_ai_stream_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai/stream/route\",\n        pathname: \"/api/ai/stream\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai/stream/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/PROJECTS/influtify/src/app/api/ai/stream/route.ts\",\n    nextConfigOutput,\n    userland: _Users_brunao_PROJECTS_influtify_src_app_api_ai_stream_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fstream%2Froute&page=%2Fapi%2Fai%2Fstream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fstream%2Froute.ts&appDir=%2FUsers%2Fbrunao%2FPROJECTS%2Finflutify%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbrunao%2FPROJECTS%2Finflutify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/ai/stream/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/ai/stream/route.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @clerk/nextjs/server */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js\");\n/* harmony import */ var _lib_services_ai_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../lib/services/ai-provider */ \"(rsc)/./src/lib/services/ai-provider.ts\");\n/* harmony import */ var _lib_services_conversation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../lib/services/conversation */ \"(rsc)/./src/lib/services/conversation.ts\");\n/* harmony import */ var _lib_services_user__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../lib/services/user */ \"(rsc)/./src/lib/services/user.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/types.js\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/v3/ZodError.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_services_conversation__WEBPACK_IMPORTED_MODULE_2__, _lib_services_user__WEBPACK_IMPORTED_MODULE_3__]);\n([_lib_services_conversation__WEBPACK_IMPORTED_MODULE_2__, _lib_services_user__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst chatRequestSchema = zod__WEBPACK_IMPORTED_MODULE_4__.object({\n    message: zod__WEBPACK_IMPORTED_MODULE_4__.string().min(1, 'Message cannot be empty'),\n    conversationId: zod__WEBPACK_IMPORTED_MODULE_4__.coerce.number().optional()\n});\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: 'AI stream API is working'\n    });\n}\nasync function POST(req) {\n    try {\n        const { userId } = await (0,_clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_5__.auth)();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await req.json();\n        const { message, conversationId } = chatRequestSchema.parse(body);\n        // Get user profile for context\n        const userProfile = await _lib_services_user__WEBPACK_IMPORTED_MODULE_3__.UserService.getCurrentUser();\n        // Build conversation context\n        const context = {\n            userId,\n            conversationId,\n            userProfile,\n            currentGoals: userProfile?.goals || []\n        };\n        // Get conversation history for context\n        let conversationHistory = [];\n        if (conversationId) {\n            conversationHistory = await _lib_services_conversation__WEBPACK_IMPORTED_MODULE_2__.conversationService.getConversationHistory(conversationId.toString());\n        }\n        // Create streaming response\n        const stream = new ReadableStream({\n            async start (controller) {\n                try {\n                    // Generate AI response with streaming\n                    const response = await (0,_lib_services_ai_provider__WEBPACK_IMPORTED_MODULE_1__.generateInflutifyMentorResponse)(message, context, conversationHistory);\n                    // Save the conversation\n                    const { conversationId: finalConversationId } = await _lib_services_conversation__WEBPACK_IMPORTED_MODULE_2__.conversationService.generateMentorResponse(message, context);\n                    // Send the response with conversation ID\n                    const responseData = {\n                        response,\n                        conversationId: finalConversationId\n                    };\n                    controller.enqueue(new TextEncoder().encode(JSON.stringify(responseData)));\n                    controller.close();\n                } catch (error) {\n                    console.error('AI streaming error:', error);\n                    const errorResponse = {\n                        error: 'Failed to generate response',\n                        details: error instanceof Error ? error.message : 'Unknown error'\n                    };\n                    controller.enqueue(new TextEncoder().encode(JSON.stringify(errorResponse)));\n                    controller.close();\n                }\n            }\n        });\n        return new Response(stream, {\n            headers: {\n                'Content-Type': 'application/json; charset=utf-8',\n                'Cache-Control': 'no-cache',\n                'Connection': 'keep-alive',\n                'X-Conversation-Id': conversationId?.toString() || 'new'\n            }\n        });\n    } catch (error) {\n        console.error('AI stream API error:', error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_6__.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request data',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai/stream/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   achievements: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.achievements),\n/* harmony export */   artifacts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.artifacts),\n/* harmony export */   contentBlocks: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.contentBlocks),\n/* harmony export */   conversations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.conversations),\n/* harmony export */   dailyActivity: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.dailyActivity),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   messages: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.messages),\n/* harmony export */   roadmaps: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.roadmaps),\n/* harmony export */   userAchievements: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements),\n/* harmony export */   userBlockProgress: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userBlockProgress),\n/* harmony export */   userStats: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userStats),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_node_postgres__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/node-postgres */ \"(rsc)/./node_modules/drizzle-orm/node-postgres/driver.js\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([pg__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_node_postgres__WEBPACK_IMPORTED_MODULE_2__]);\n([pg__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_node_postgres__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    connectionString: process.env.DATABASE_URL\n});\nconst db = (0,drizzle_orm_node_postgres__WEBPACK_IMPORTED_MODULE_2__.drizzle)(pool, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFvRDtBQUMxQjtBQUNTO0FBRW5DLE1BQU1HLE9BQU8sSUFBSUYsb0NBQUlBLENBQUM7SUFDcEJHLGtCQUFrQkMsUUFBUUMsR0FBRyxDQUFDQyxZQUFZO0FBQzVDO0FBRU8sTUFBTUMsS0FBS1Isa0VBQU9BLENBQUNHLE1BQU07SUFBRUQsTUFBTUEsc0NBQUFBO0FBQUMsR0FBRztBQUVuQiIsInNvdXJjZXMiOlsiL1VzZXJzL2JydW5hby9QUk9KRUNUUy9pbmZsdXRpZnkvc3JjL2xpYi9kYi9pbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkcml6emxlIH0gZnJvbSAnZHJpenpsZS1vcm0vbm9kZS1wb3N0Z3Jlcyc7XG5pbXBvcnQgeyBQb29sIH0gZnJvbSAncGcnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgcG9vbCA9IG5ldyBQb29sKHtcbiAgY29ubmVjdGlvblN0cmluZzogcHJvY2Vzcy5lbnYuREFUQUJBU0VfVVJMLFxufSk7XG5cbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUocG9vbCwgeyBzY2hlbWEgfSk7XG5cbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hJztcbiJdLCJuYW1lcyI6WyJkcml6emxlIiwiUG9vbCIsInNjaGVtYSIsInBvb2wiLCJjb25uZWN0aW9uU3RyaW5nIiwicHJvY2VzcyIsImVudiIsIkRBVEFCQVNFX1VSTCIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   achievements: () => (/* binding */ achievements),\n/* harmony export */   artifacts: () => (/* binding */ artifacts),\n/* harmony export */   contentBlocks: () => (/* binding */ contentBlocks),\n/* harmony export */   conversations: () => (/* binding */ conversations),\n/* harmony export */   dailyActivity: () => (/* binding */ dailyActivity),\n/* harmony export */   messages: () => (/* binding */ messages),\n/* harmony export */   roadmaps: () => (/* binding */ roadmaps),\n/* harmony export */   userAchievements: () => (/* binding */ userAchievements),\n/* harmony export */   userBlockProgress: () => (/* binding */ userBlockProgress),\n/* harmony export */   userStats: () => (/* binding */ userStats),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/serial.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/date.js\");\n\n// Users table (extends Clerk user data)\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('id').primaryKey(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('updated_at').defaultNow().notNull(),\n    // Profile Information\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('email').notNull(),\n    firstName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('first_name'),\n    lastName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('last_name'),\n    imageUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('image_url'),\n    // Social Media Goals\n    primaryGoal: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('primary_goal'),\n    targetAudience: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('target_audience'),\n    currentFollowers: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('current_followers').default(0),\n    targetFollowers: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('target_followers'),\n    // Platform Information\n    platforms: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('platforms').$type().default([]),\n    // Preferences\n    contentStyle: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('content_style'),\n    postingFrequency: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('posting_frequency'),\n    timezone: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('timezone').default('UTC'),\n    language: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('language').default('en'),\n    // Subscription\n    subscriptionStatus: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('subscription_status').default('free'),\n    subscriptionId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('subscription_id'),\n    subscriptionEndsAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('subscription_ends_at')\n});\n// Conversations table\nconst conversations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('conversations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('user_id').notNull().references(()=>users.id),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('updated_at').defaultNow().notNull(),\n    // Conversation metadata\n    title: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('title'),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('is_active').default(true),\n    // Context and state\n    context: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('context').$type().default({})\n});\n// Messages table\nconst messages = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('messages', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    conversationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('conversation_id').references(()=>conversations.id).notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    // Message content\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('role').notNull(),\n    content: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('content').notNull(),\n    // AI function calling\n    functionCall: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('function_call').$type(),\n    functionResult: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('function_result'),\n    // Message metadata\n    metadata: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('metadata').$type().default({})\n});\n// Artifacts table\nconst artifacts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('artifacts', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('id').primaryKey(),\n    conversationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('conversation_id').references(()=>conversations.id).notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('user_id').notNull().references(()=>users.id),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('updated_at').defaultNow().notNull(),\n    // Artifact information\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('type').notNull(),\n    title: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('title').notNull(),\n    description: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('description'),\n    // Artifact data\n    data: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('data').notNull(),\n    // State and interactions\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('is_active').default(true),\n    interactions: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('interactions').$type().default([])\n});\n// Roadmaps table\nconst roadmaps = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('roadmaps', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('user_id').notNull().references(()=>users.id),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('updated_at').defaultNow().notNull(),\n    // Roadmap information\n    title: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('title').notNull(),\n    description: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('description'),\n    goal: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('goal').notNull(),\n    // Roadmap configuration\n    duration: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('duration').notNull(),\n    difficulty: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('difficulty').notNull(),\n    // Progress tracking\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('status').default('active'),\n    progress: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('progress').default(0),\n    // Roadmap data\n    blocks: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('blocks').$type().notNull(),\n    // Metadata\n    isCustom: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('is_custom').default(false),\n    templateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('template_id')\n});\n// Content blocks table\nconst contentBlocks = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('content_blocks', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    // Block information\n    title: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('title').notNull(),\n    description: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('description').notNull(),\n    category: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('category').notNull(),\n    // Content structure\n    format: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('format').notNull(),\n    difficulty: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('difficulty').notNull(),\n    estimatedTime: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('estimated_time').notNull(),\n    // Block content\n    content: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('content').$type().notNull(),\n    // Requirements and outcomes\n    prerequisites: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('prerequisites').$type().default([]),\n    learningOutcomes: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('learning_outcomes').$type().default([]),\n    // Metadata\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('is_active').default(true),\n    sortOrder: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('sort_order').default(0),\n    tags: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('tags').$type().default([])\n});\n// User block progress table\nconst userBlockProgress = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_block_progress', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('user_id').notNull().references(()=>users.id),\n    blockId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('block_id').references(()=>contentBlocks.id).notNull(),\n    roadmapId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('roadmap_id').references(()=>roadmaps.id),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('updated_at').defaultNow().notNull(),\n    // Progress tracking\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('status').default('not_started'),\n    progress: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('progress').default(0),\n    timeSpent: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('time_spent').default(0),\n    // Completion data\n    startedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('started_at'),\n    completedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('completed_at'),\n    // User responses and work\n    responses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('responses').$type().default([]),\n    // Generated content\n    generatedContent: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('generated_content').$type().default([]),\n    // Feedback and notes\n    userNotes: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('user_notes'),\n    aifeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('ai_feedback').$type()\n});\n// Achievements table\nconst achievements = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('achievements', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    // Achievement Information\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('name').notNull(),\n    title: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('title').notNull(),\n    description: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('description').notNull(),\n    category: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('category').notNull(),\n    // Achievement Criteria\n    criteria: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('criteria').$type().notNull(),\n    // Visual Elements\n    icon: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('icon').notNull(),\n    color: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('color').notNull(),\n    rarity: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('rarity').notNull(),\n    // Rewards\n    points: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('points').default(0),\n    unlocks: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('unlocks').$type(),\n    // Metadata\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('is_active').default(true),\n    sortOrder: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('sort_order').default(0)\n});\n// User Achievements table\nconst userAchievements = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_achievements', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('user_id').notNull().references(()=>users.id),\n    achievementId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('achievement_id').references(()=>achievements.id).notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    // Achievement Data\n    unlockedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('unlocked_at').notNull(),\n    progress: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('progress').default(100),\n    // Context\n    roadmapId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('roadmap_id').references(()=>roadmaps.id),\n    triggerData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('trigger_data'),\n    // Celebration\n    wasNotified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('was_notified').default(false),\n    wasCelebrated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('was_celebrated').default(false)\n});\n// User Stats table\nconst userStats = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_stats', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('user_id').notNull().references(()=>users.id),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('updated_at').defaultNow().notNull(),\n    // Overall Stats\n    totalPoints: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('total_points').default(0),\n    level: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('level').default(1),\n    experiencePoints: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('experience_points').default(0),\n    // Achievement Stats\n    totalAchievements: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('total_achievements').default(0),\n    commonAchievements: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('common_achievements').default(0),\n    rareAchievements: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rare_achievements').default(0),\n    epicAchievements: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('epic_achievements').default(0),\n    legendaryAchievements: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('legendary_achievements').default(0),\n    // Activity Stats\n    streakDays: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('streak_days').default(0),\n    longestStreak: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('longest_streak').default(0),\n    lastActivityDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('last_activity_date'),\n    // Content Stats\n    totalContentCreated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('total_content_created').default(0),\n    totalBlocksCompleted: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('total_blocks_completed').default(0),\n    totalRoadmapsCompleted: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('total_roadmaps_completed').default(0)\n});\n// Daily Activity table\nconst dailyActivity = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('daily_activity', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.serial)('id').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.text)('user_id').notNull().references(()=>users.id),\n    date: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.date)('date').notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.timestamp)('created_at').defaultNow().notNull(),\n    // Activity Data\n    activitiesCompleted: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.json)('activities_completed').$type().default([]),\n    // Points and Experience\n    pointsEarned: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('points_earned').default(0),\n    experienceEarned: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('experience_earned').default(0),\n    // Streaks\n    isStreakDay: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('is_streak_day').default(false),\n    streakCount: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('streak_count').default(0)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3NjaGVtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXFHO0FBRXJHLHdDQUF3QztBQUNqQyxNQUFNUSxRQUFRUiw0REFBT0EsQ0FBQyxTQUFTO0lBQ3BDUyxJQUFJUCx5REFBSUEsQ0FBQyxNQUFNUSxVQUFVO0lBQ3pCQyxXQUFXUiw4REFBU0EsQ0FBQyxjQUFjUyxVQUFVLEdBQUdDLE9BQU87SUFDdkRDLFdBQVdYLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUV2RCxzQkFBc0I7SUFDdEJFLE9BQU9iLHlEQUFJQSxDQUFDLFNBQVNXLE9BQU87SUFDNUJHLFdBQVdkLHlEQUFJQSxDQUFDO0lBQ2hCZSxVQUFVZix5REFBSUEsQ0FBQztJQUNmZ0IsVUFBVWhCLHlEQUFJQSxDQUFDO0lBRWYscUJBQXFCO0lBQ3JCaUIsYUFBYWpCLHlEQUFJQSxDQUFDO0lBQ2xCa0IsZ0JBQWdCbEIseURBQUlBLENBQUM7SUFDckJtQixrQkFBa0JoQiw0REFBT0EsQ0FBQyxxQkFBcUJpQixPQUFPLENBQUM7SUFDdkRDLGlCQUFpQmxCLDREQUFPQSxDQUFDO0lBRXpCLHVCQUF1QjtJQUN2Qm1CLFdBQVdwQix5REFBSUEsQ0FBQyxhQUFhcUIsS0FBSyxHQUszQkgsT0FBTyxDQUFDLEVBQUU7SUFFakIsY0FBYztJQUNkSSxjQUFjeEIseURBQUlBLENBQUM7SUFDbkJ5QixrQkFBa0J6Qix5REFBSUEsQ0FBQztJQUN2QjBCLFVBQVUxQix5REFBSUEsQ0FBQyxZQUFZb0IsT0FBTyxDQUFDO0lBQ25DTyxVQUFVM0IseURBQUlBLENBQUMsWUFBWW9CLE9BQU8sQ0FBQztJQUVuQyxlQUFlO0lBQ2ZRLG9CQUFvQjVCLHlEQUFJQSxDQUFDLHVCQUF1Qm9CLE9BQU8sQ0FBQztJQUN4RFMsZ0JBQWdCN0IseURBQUlBLENBQUM7SUFDckI4QixvQkFBb0I3Qiw4REFBU0EsQ0FBQztBQUNoQyxHQUFHO0FBRUgsc0JBQXNCO0FBQ2YsTUFBTThCLGdCQUFnQmpDLDREQUFPQSxDQUFDLGlCQUFpQjtJQUNwRFMsSUFBSVIsMkRBQU1BLENBQUMsTUFBTVMsVUFBVTtJQUMzQndCLFFBQVFoQyx5REFBSUEsQ0FBQyxXQUFXVyxPQUFPLEdBQUdzQixVQUFVLENBQUMsSUFBTTNCLE1BQU1DLEVBQUU7SUFDM0RFLFdBQVdSLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUN2REMsV0FBV1gsOERBQVNBLENBQUMsY0FBY1MsVUFBVSxHQUFHQyxPQUFPO0lBRXZELHdCQUF3QjtJQUN4QnVCLE9BQU9sQyx5REFBSUEsQ0FBQztJQUNabUMsVUFBVS9CLDREQUFPQSxDQUFDLGFBQWFnQixPQUFPLENBQUM7SUFFdkMsb0JBQW9CO0lBQ3BCZ0IsU0FBU2xDLHlEQUFJQSxDQUFDLFdBQVdxQixLQUFLLEdBS3pCSCxPQUFPLENBQUMsQ0FBQztBQUNoQixHQUFHO0FBRUgsaUJBQWlCO0FBQ1YsTUFBTWlCLFdBQVd2Qyw0REFBT0EsQ0FBQyxZQUFZO0lBQzFDUyxJQUFJUiwyREFBTUEsQ0FBQyxNQUFNUyxVQUFVO0lBQzNCOEIsZ0JBQWdCbkMsNERBQU9BLENBQUMsbUJBQW1COEIsVUFBVSxDQUFDLElBQU1GLGNBQWN4QixFQUFFLEVBQUVJLE9BQU87SUFDckZGLFdBQVdSLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUV2RCxrQkFBa0I7SUFDbEI0QixNQUFNdkMseURBQUlBLENBQUMsUUFBUVcsT0FBTztJQUMxQjZCLFNBQVN4Qyx5REFBSUEsQ0FBQyxXQUFXVyxPQUFPO0lBRWhDLHNCQUFzQjtJQUN0QjhCLGNBQWN2Qyx5REFBSUEsQ0FBQyxpQkFBaUJxQixLQUFLO0lBSXpDbUIsZ0JBQWdCeEMseURBQUlBLENBQUM7SUFFckIsbUJBQW1CO0lBQ25CeUMsVUFBVXpDLHlEQUFJQSxDQUFDLFlBQVlxQixLQUFLLEdBSzNCSCxPQUFPLENBQUMsQ0FBQztBQUNoQixHQUFHO0FBRUgsa0JBQWtCO0FBQ1gsTUFBTXdCLFlBQVk5Qyw0REFBT0EsQ0FBQyxhQUFhO0lBQzVDUyxJQUFJUCx5REFBSUEsQ0FBQyxNQUFNUSxVQUFVO0lBQ3pCOEIsZ0JBQWdCbkMsNERBQU9BLENBQUMsbUJBQW1COEIsVUFBVSxDQUFDLElBQU1GLGNBQWN4QixFQUFFLEVBQUVJLE9BQU87SUFDckZxQixRQUFRaEMseURBQUlBLENBQUMsV0FBV1csT0FBTyxHQUFHc0IsVUFBVSxDQUFDLElBQU0zQixNQUFNQyxFQUFFO0lBQzNERSxXQUFXUiw4REFBU0EsQ0FBQyxjQUFjUyxVQUFVLEdBQUdDLE9BQU87SUFDdkRDLFdBQVdYLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUV2RCx1QkFBdUI7SUFDdkJrQyxNQUFNN0MseURBQUlBLENBQUMsUUFBUVcsT0FBTztJQUMxQnVCLE9BQU9sQyx5REFBSUEsQ0FBQyxTQUFTVyxPQUFPO0lBQzVCbUMsYUFBYTlDLHlEQUFJQSxDQUFDO0lBRWxCLGdCQUFnQjtJQUNoQitDLE1BQU03Qyx5REFBSUEsQ0FBQyxRQUFRUyxPQUFPO0lBRTFCLHlCQUF5QjtJQUN6QndCLFVBQVUvQiw0REFBT0EsQ0FBQyxhQUFhZ0IsT0FBTyxDQUFDO0lBQ3ZDNEIsY0FBYzlDLHlEQUFJQSxDQUFDLGdCQUFnQnFCLEtBQUssR0FJakNILE9BQU8sQ0FBQyxFQUFFO0FBQ25CLEdBQUc7QUFFSCxpQkFBaUI7QUFDVixNQUFNNkIsV0FBV25ELDREQUFPQSxDQUFDLFlBQVk7SUFDMUNTLElBQUlSLDJEQUFNQSxDQUFDLE1BQU1TLFVBQVU7SUFDM0J3QixRQUFRaEMseURBQUlBLENBQUMsV0FBV1csT0FBTyxHQUFHc0IsVUFBVSxDQUFDLElBQU0zQixNQUFNQyxFQUFFO0lBQzNERSxXQUFXUiw4REFBU0EsQ0FBQyxjQUFjUyxVQUFVLEdBQUdDLE9BQU87SUFDdkRDLFdBQVdYLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUV2RCxzQkFBc0I7SUFDdEJ1QixPQUFPbEMseURBQUlBLENBQUMsU0FBU1csT0FBTztJQUM1Qm1DLGFBQWE5Qyx5REFBSUEsQ0FBQztJQUNsQmtELE1BQU1sRCx5REFBSUEsQ0FBQyxRQUFRVyxPQUFPO0lBRTFCLHdCQUF3QjtJQUN4QndDLFVBQVVoRCw0REFBT0EsQ0FBQyxZQUFZUSxPQUFPO0lBQ3JDeUMsWUFBWXBELHlEQUFJQSxDQUFDLGNBQWNXLE9BQU87SUFFdEMsb0JBQW9CO0lBQ3BCMEMsUUFBUXJELHlEQUFJQSxDQUFDLFVBQVVvQixPQUFPLENBQUM7SUFDL0JrQyxVQUFVbkQsNERBQU9BLENBQUMsWUFBWWlCLE9BQU8sQ0FBQztJQUV0QyxlQUFlO0lBQ2ZtQyxRQUFRckQseURBQUlBLENBQUMsVUFBVXFCLEtBQUssR0FPckJaLE9BQU87SUFFZCxXQUFXO0lBQ1g2QyxVQUFVcEQsNERBQU9BLENBQUMsYUFBYWdCLE9BQU8sQ0FBQztJQUN2Q3FDLFlBQVl6RCx5REFBSUEsQ0FBQztBQUNuQixHQUFHO0FBRUgsdUJBQXVCO0FBQ2hCLE1BQU0wRCxnQkFBZ0I1RCw0REFBT0EsQ0FBQyxrQkFBa0I7SUFDckRTLElBQUlSLDJEQUFNQSxDQUFDLE1BQU1TLFVBQVU7SUFDM0JDLFdBQVdSLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUV2RCxvQkFBb0I7SUFDcEJ1QixPQUFPbEMseURBQUlBLENBQUMsU0FBU1csT0FBTztJQUM1Qm1DLGFBQWE5Qyx5REFBSUEsQ0FBQyxlQUFlVyxPQUFPO0lBQ3hDZ0QsVUFBVTNELHlEQUFJQSxDQUFDLFlBQVlXLE9BQU87SUFFbEMsb0JBQW9CO0lBQ3BCaUQsUUFBUTVELHlEQUFJQSxDQUFDLFVBQVVXLE9BQU87SUFDOUJ5QyxZQUFZcEQseURBQUlBLENBQUMsY0FBY1csT0FBTztJQUN0Q2tELGVBQWUxRCw0REFBT0EsQ0FBQyxrQkFBa0JRLE9BQU87SUFFaEQsZ0JBQWdCO0lBQ2hCNkIsU0FBU3RDLHlEQUFJQSxDQUFDLFdBQVdxQixLQUFLLEdBa0J6QlosT0FBTztJQUVaLDRCQUE0QjtJQUM1Qm1ELGVBQWU1RCx5REFBSUEsQ0FBQyxpQkFBaUJxQixLQUFLLEdBQWFILE9BQU8sQ0FBQyxFQUFFO0lBQ2pFMkMsa0JBQWtCN0QseURBQUlBLENBQUMscUJBQXFCcUIsS0FBSyxHQUFhSCxPQUFPLENBQUMsRUFBRTtJQUV4RSxXQUFXO0lBQ1hlLFVBQVUvQiw0REFBT0EsQ0FBQyxhQUFhZ0IsT0FBTyxDQUFDO0lBQ3ZDNEMsV0FBVzdELDREQUFPQSxDQUFDLGNBQWNpQixPQUFPLENBQUM7SUFDekM2QyxNQUFNL0QseURBQUlBLENBQUMsUUFBUXFCLEtBQUssR0FBYUgsT0FBTyxDQUFDLEVBQUU7QUFDakQsR0FBRztBQUVILDRCQUE0QjtBQUNyQixNQUFNOEMsb0JBQW9CcEUsNERBQU9BLENBQUMsdUJBQXVCO0lBQzlEUyxJQUFJUiwyREFBTUEsQ0FBQyxNQUFNUyxVQUFVO0lBQzNCd0IsUUFBUWhDLHlEQUFJQSxDQUFDLFdBQVdXLE9BQU8sR0FBR3NCLFVBQVUsQ0FBQyxJQUFNM0IsTUFBTUMsRUFBRTtJQUMzRDRELFNBQVNoRSw0REFBT0EsQ0FBQyxZQUFZOEIsVUFBVSxDQUFDLElBQU15QixjQUFjbkQsRUFBRSxFQUFFSSxPQUFPO0lBQ3ZFeUQsV0FBV2pFLDREQUFPQSxDQUFDLGNBQWM4QixVQUFVLENBQUMsSUFBTWdCLFNBQVMxQyxFQUFFO0lBQzdERSxXQUFXUiw4REFBU0EsQ0FBQyxjQUFjUyxVQUFVLEdBQUdDLE9BQU87SUFDdkRDLFdBQVdYLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUV2RCxvQkFBb0I7SUFDcEIwQyxRQUFRckQseURBQUlBLENBQUMsVUFBVW9CLE9BQU8sQ0FBQztJQUMvQmtDLFVBQVVuRCw0REFBT0EsQ0FBQyxZQUFZaUIsT0FBTyxDQUFDO0lBQ3RDaUQsV0FBV2xFLDREQUFPQSxDQUFDLGNBQWNpQixPQUFPLENBQUM7SUFFekMsa0JBQWtCO0lBQ2xCa0QsV0FBV3JFLDhEQUFTQSxDQUFDO0lBQ3JCc0UsYUFBYXRFLDhEQUFTQSxDQUFDO0lBRXZCLDBCQUEwQjtJQUMxQnVFLFdBQVd0RSx5REFBSUEsQ0FBQyxhQUFhcUIsS0FBSyxHQUkzQkgsT0FBTyxDQUFDLEVBQUU7SUFFakIsb0JBQW9CO0lBQ3BCcUQsa0JBQWtCdkUseURBQUlBLENBQUMscUJBQXFCcUIsS0FBSyxHQUsxQ0gsT0FBTyxDQUFDLEVBQUU7SUFFakIscUJBQXFCO0lBQ3JCc0QsV0FBVzFFLHlEQUFJQSxDQUFDO0lBQ2hCMkUsWUFBWXpFLHlEQUFJQSxDQUFDLGVBQWVxQixLQUFLO0FBTXZDLEdBQUc7QUFFSCxxQkFBcUI7QUFDZCxNQUFNcUQsZUFBZTlFLDREQUFPQSxDQUFDLGdCQUFnQjtJQUNsRFMsSUFBSVIsMkRBQU1BLENBQUMsTUFBTVMsVUFBVTtJQUMzQkMsV0FBV1IsOERBQVNBLENBQUMsY0FBY1MsVUFBVSxHQUFHQyxPQUFPO0lBRXZELDBCQUEwQjtJQUMxQmtFLE1BQU03RSx5REFBSUEsQ0FBQyxRQUFRVyxPQUFPO0lBQzFCdUIsT0FBT2xDLHlEQUFJQSxDQUFDLFNBQVNXLE9BQU87SUFDNUJtQyxhQUFhOUMseURBQUlBLENBQUMsZUFBZVcsT0FBTztJQUN4Q2dELFVBQVUzRCx5REFBSUEsQ0FBQyxZQUFZVyxPQUFPO0lBRWxDLHVCQUF1QjtJQUN2Qm1FLFVBQVU1RSx5REFBSUEsQ0FBQyxZQUFZcUIsS0FBSyxHQUszQlosT0FBTztJQUVaLGtCQUFrQjtJQUNsQm9FLE1BQU0vRSx5REFBSUEsQ0FBQyxRQUFRVyxPQUFPO0lBQzFCcUUsT0FBT2hGLHlEQUFJQSxDQUFDLFNBQVNXLE9BQU87SUFDNUJzRSxRQUFRakYseURBQUlBLENBQUMsVUFBVVcsT0FBTztJQUU5QixVQUFVO0lBQ1Z1RSxRQUFRL0UsNERBQU9BLENBQUMsVUFBVWlCLE9BQU8sQ0FBQztJQUNsQytELFNBQVNqRix5REFBSUEsQ0FBQyxXQUFXcUIsS0FBSztJQUU5QixXQUFXO0lBQ1hZLFVBQVUvQiw0REFBT0EsQ0FBQyxhQUFhZ0IsT0FBTyxDQUFDO0lBQ3ZDNEMsV0FBVzdELDREQUFPQSxDQUFDLGNBQWNpQixPQUFPLENBQUM7QUFDM0MsR0FBRztBQUVILDBCQUEwQjtBQUNuQixNQUFNZ0UsbUJBQW1CdEYsNERBQU9BLENBQUMscUJBQXFCO0lBQzNEUyxJQUFJUiwyREFBTUEsQ0FBQyxNQUFNUyxVQUFVO0lBQzNCd0IsUUFBUWhDLHlEQUFJQSxDQUFDLFdBQVdXLE9BQU8sR0FBR3NCLFVBQVUsQ0FBQyxJQUFNM0IsTUFBTUMsRUFBRTtJQUMzRDhFLGVBQWVsRiw0REFBT0EsQ0FBQyxrQkFBa0I4QixVQUFVLENBQUMsSUFBTTJDLGFBQWFyRSxFQUFFLEVBQUVJLE9BQU87SUFDbEZGLFdBQVdSLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUV2RCxtQkFBbUI7SUFDbkIyRSxZQUFZckYsOERBQVNBLENBQUMsZUFBZVUsT0FBTztJQUM1QzJDLFVBQVVuRCw0REFBT0EsQ0FBQyxZQUFZaUIsT0FBTyxDQUFDO0lBRXRDLFVBQVU7SUFDVmdELFdBQVdqRSw0REFBT0EsQ0FBQyxjQUFjOEIsVUFBVSxDQUFDLElBQU1nQixTQUFTMUMsRUFBRTtJQUM3RGdGLGFBQWFyRix5REFBSUEsQ0FBQztJQUVsQixjQUFjO0lBQ2RzRixhQUFhcEYsNERBQU9BLENBQUMsZ0JBQWdCZ0IsT0FBTyxDQUFDO0lBQzdDcUUsZUFBZXJGLDREQUFPQSxDQUFDLGtCQUFrQmdCLE9BQU8sQ0FBQztBQUNuRCxHQUFHO0FBRUgsbUJBQW1CO0FBQ1osTUFBTXNFLFlBQVk1Riw0REFBT0EsQ0FBQyxjQUFjO0lBQzdDUyxJQUFJUiwyREFBTUEsQ0FBQyxNQUFNUyxVQUFVO0lBQzNCd0IsUUFBUWhDLHlEQUFJQSxDQUFDLFdBQVdXLE9BQU8sR0FBR3NCLFVBQVUsQ0FBQyxJQUFNM0IsTUFBTUMsRUFBRTtJQUMzREssV0FBV1gsOERBQVNBLENBQUMsY0FBY1MsVUFBVSxHQUFHQyxPQUFPO0lBRXZELGdCQUFnQjtJQUNoQmdGLGFBQWF4Riw0REFBT0EsQ0FBQyxnQkFBZ0JpQixPQUFPLENBQUM7SUFDN0N3RSxPQUFPekYsNERBQU9BLENBQUMsU0FBU2lCLE9BQU8sQ0FBQztJQUNoQ3lFLGtCQUFrQjFGLDREQUFPQSxDQUFDLHFCQUFxQmlCLE9BQU8sQ0FBQztJQUV2RCxvQkFBb0I7SUFDcEIwRSxtQkFBbUIzRiw0REFBT0EsQ0FBQyxzQkFBc0JpQixPQUFPLENBQUM7SUFDekQyRSxvQkFBb0I1Riw0REFBT0EsQ0FBQyx1QkFBdUJpQixPQUFPLENBQUM7SUFDM0Q0RSxrQkFBa0I3Riw0REFBT0EsQ0FBQyxxQkFBcUJpQixPQUFPLENBQUM7SUFDdkQ2RSxrQkFBa0I5Riw0REFBT0EsQ0FBQyxxQkFBcUJpQixPQUFPLENBQUM7SUFDdkQ4RSx1QkFBdUIvRiw0REFBT0EsQ0FBQywwQkFBMEJpQixPQUFPLENBQUM7SUFFakUsaUJBQWlCO0lBQ2pCK0UsWUFBWWhHLDREQUFPQSxDQUFDLGVBQWVpQixPQUFPLENBQUM7SUFDM0NnRixlQUFlakcsNERBQU9BLENBQUMsa0JBQWtCaUIsT0FBTyxDQUFDO0lBQ2pEaUYsa0JBQWtCcEcsOERBQVNBLENBQUM7SUFFNUIsZ0JBQWdCO0lBQ2hCcUcscUJBQXFCbkcsNERBQU9BLENBQUMseUJBQXlCaUIsT0FBTyxDQUFDO0lBQzlEbUYsc0JBQXNCcEcsNERBQU9BLENBQUMsMEJBQTBCaUIsT0FBTyxDQUFDO0lBQ2hFb0Ysd0JBQXdCckcsNERBQU9BLENBQUMsNEJBQTRCaUIsT0FBTyxDQUFDO0FBQ3RFLEdBQUc7QUFFSCx1QkFBdUI7QUFDaEIsTUFBTXFGLGdCQUFnQjNHLDREQUFPQSxDQUFDLGtCQUFrQjtJQUNyRFMsSUFBSVIsMkRBQU1BLENBQUMsTUFBTVMsVUFBVTtJQUMzQndCLFFBQVFoQyx5REFBSUEsQ0FBQyxXQUFXVyxPQUFPLEdBQUdzQixVQUFVLENBQUMsSUFBTTNCLE1BQU1DLEVBQUU7SUFDM0RGLE1BQU1BLHlEQUFJQSxDQUFDLFFBQVFNLE9BQU87SUFDMUJGLFdBQVdSLDhEQUFTQSxDQUFDLGNBQWNTLFVBQVUsR0FBR0MsT0FBTztJQUV2RCxnQkFBZ0I7SUFDaEIrRixxQkFBcUJ4Ryx5REFBSUEsQ0FBQyx3QkFBd0JxQixLQUFLLEdBSWhESCxPQUFPLENBQUMsRUFBRTtJQUVqQix3QkFBd0I7SUFDeEJ1RixjQUFjeEcsNERBQU9BLENBQUMsaUJBQWlCaUIsT0FBTyxDQUFDO0lBQy9Dd0Ysa0JBQWtCekcsNERBQU9BLENBQUMscUJBQXFCaUIsT0FBTyxDQUFDO0lBRXZELFVBQVU7SUFDVnlGLGFBQWF6Ryw0REFBT0EsQ0FBQyxpQkFBaUJnQixPQUFPLENBQUM7SUFDOUMwRixhQUFhM0csNERBQU9BLENBQUMsZ0JBQWdCaUIsT0FBTyxDQUFDO0FBQy9DLEdBQUciLCJzb3VyY2VzIjpbIi9Vc2Vycy9icnVuYW8vUFJPSkVDVFMvaW5mbHV0aWZ5L3NyYy9saWIvZGIvc2NoZW1hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBnVGFibGUsIHNlcmlhbCwgdGV4dCwgdGltZXN0YW1wLCBqc29uLCBpbnRlZ2VyLCBib29sZWFuLCBkYXRlIH0gZnJvbSAnZHJpenpsZS1vcm0vcGctY29yZSc7XG5cbi8vIFVzZXJzIHRhYmxlIChleHRlbmRzIENsZXJrIHVzZXIgZGF0YSlcbmV4cG9ydCBjb25zdCB1c2VycyA9IHBnVGFibGUoJ3VzZXJzJywge1xuICBpZDogdGV4dCgnaWQnKS5wcmltYXJ5S2V5KCksIC8vIENsZXJrIHVzZXIgSURcbiAgY3JlYXRlZEF0OiB0aW1lc3RhbXAoJ2NyZWF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxuICB1cGRhdGVkQXQ6IHRpbWVzdGFtcCgndXBkYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXG4gIFxuICAvLyBQcm9maWxlIEluZm9ybWF0aW9uXG4gIGVtYWlsOiB0ZXh0KCdlbWFpbCcpLm5vdE51bGwoKSxcbiAgZmlyc3ROYW1lOiB0ZXh0KCdmaXJzdF9uYW1lJyksXG4gIGxhc3ROYW1lOiB0ZXh0KCdsYXN0X25hbWUnKSxcbiAgaW1hZ2VVcmw6IHRleHQoJ2ltYWdlX3VybCcpLFxuICBcbiAgLy8gU29jaWFsIE1lZGlhIEdvYWxzXG4gIHByaW1hcnlHb2FsOiB0ZXh0KCdwcmltYXJ5X2dvYWwnKSwgLy8gZm9sbG93ZXJfZ3Jvd3RoLCBlbmdhZ2VtZW50LCBtb25ldGl6YXRpb24sIGJyYW5kX2F3YXJlbmVzc1xuICB0YXJnZXRBdWRpZW5jZTogdGV4dCgndGFyZ2V0X2F1ZGllbmNlJyksXG4gIGN1cnJlbnRGb2xsb3dlcnM6IGludGVnZXIoJ2N1cnJlbnRfZm9sbG93ZXJzJykuZGVmYXVsdCgwKSxcbiAgdGFyZ2V0Rm9sbG93ZXJzOiBpbnRlZ2VyKCd0YXJnZXRfZm9sbG93ZXJzJyksXG4gIFxuICAvLyBQbGF0Zm9ybSBJbmZvcm1hdGlvblxuICBwbGF0Zm9ybXM6IGpzb24oJ3BsYXRmb3JtcycpLiR0eXBlPHtcbiAgICBwbGF0Zm9ybTogc3RyaW5nO1xuICAgIHVzZXJuYW1lOiBzdHJpbmc7XG4gICAgZm9sbG93ZXJzOiBudW1iZXI7XG4gICAgaXNBY3RpdmU6IGJvb2xlYW47XG4gIH1bXT4oKS5kZWZhdWx0KFtdKSxcbiAgXG4gIC8vIFByZWZlcmVuY2VzXG4gIGNvbnRlbnRTdHlsZTogdGV4dCgnY29udGVudF9zdHlsZScpLCAvLyBlZHVjYXRpb25hbCwgZW50ZXJ0YWluaW5nLCBpbnNwaXJhdGlvbmFsLCBwcm9tb3Rpb25hbFxuICBwb3N0aW5nRnJlcXVlbmN5OiB0ZXh0KCdwb3N0aW5nX2ZyZXF1ZW5jeScpLCAvLyBkYWlseSwgd2Vla2x5LCBiaV93ZWVrbHlcbiAgdGltZXpvbmU6IHRleHQoJ3RpbWV6b25lJykuZGVmYXVsdCgnVVRDJyksXG4gIGxhbmd1YWdlOiB0ZXh0KCdsYW5ndWFnZScpLmRlZmF1bHQoJ2VuJyksXG4gIFxuICAvLyBTdWJzY3JpcHRpb25cbiAgc3Vic2NyaXB0aW9uU3RhdHVzOiB0ZXh0KCdzdWJzY3JpcHRpb25fc3RhdHVzJykuZGVmYXVsdCgnZnJlZScpLCAvLyBmcmVlLCBwcm8sIHByZW1pdW1cbiAgc3Vic2NyaXB0aW9uSWQ6IHRleHQoJ3N1YnNjcmlwdGlvbl9pZCcpLFxuICBzdWJzY3JpcHRpb25FbmRzQXQ6IHRpbWVzdGFtcCgnc3Vic2NyaXB0aW9uX2VuZHNfYXQnKSxcbn0pO1xuXG4vLyBDb252ZXJzYXRpb25zIHRhYmxlXG5leHBvcnQgY29uc3QgY29udmVyc2F0aW9ucyA9IHBnVGFibGUoJ2NvbnZlcnNhdGlvbnMnLCB7XG4gIGlkOiBzZXJpYWwoJ2lkJykucHJpbWFyeUtleSgpLFxuICB1c2VySWQ6IHRleHQoJ3VzZXJfaWQnKS5ub3ROdWxsKCkucmVmZXJlbmNlcygoKSA9PiB1c2Vycy5pZCksXG4gIGNyZWF0ZWRBdDogdGltZXN0YW1wKCdjcmVhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcbiAgdXBkYXRlZEF0OiB0aW1lc3RhbXAoJ3VwZGF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxuICBcbiAgLy8gQ29udmVyc2F0aW9uIG1ldGFkYXRhXG4gIHRpdGxlOiB0ZXh0KCd0aXRsZScpLFxuICBpc0FjdGl2ZTogYm9vbGVhbignaXNfYWN0aXZlJykuZGVmYXVsdCh0cnVlKSxcbiAgXG4gIC8vIENvbnRleHQgYW5kIHN0YXRlXG4gIGNvbnRleHQ6IGpzb24oJ2NvbnRleHQnKS4kdHlwZTx7XG4gICAgY3VycmVudEdvYWw/OiBzdHJpbmc7XG4gICAgcm9hZG1hcElkPzogbnVtYmVyO1xuICAgIGxhc3RBcnRpZmFjdElkPzogc3RyaW5nO1xuICAgIHVzZXJQcmVmZXJlbmNlcz86IFJlY29yZDxzdHJpbmcsIGFueT47XG4gIH0+KCkuZGVmYXVsdCh7fSksXG59KTtcblxuLy8gTWVzc2FnZXMgdGFibGVcbmV4cG9ydCBjb25zdCBtZXNzYWdlcyA9IHBnVGFibGUoJ21lc3NhZ2VzJywge1xuICBpZDogc2VyaWFsKCdpZCcpLnByaW1hcnlLZXkoKSxcbiAgY29udmVyc2F0aW9uSWQ6IGludGVnZXIoJ2NvbnZlcnNhdGlvbl9pZCcpLnJlZmVyZW5jZXMoKCkgPT4gY29udmVyc2F0aW9ucy5pZCkubm90TnVsbCgpLFxuICBjcmVhdGVkQXQ6IHRpbWVzdGFtcCgnY3JlYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXG4gIFxuICAvLyBNZXNzYWdlIGNvbnRlbnRcbiAgcm9sZTogdGV4dCgncm9sZScpLm5vdE51bGwoKSwgLy8gdXNlciwgYXNzaXN0YW50LCBzeXN0ZW1cbiAgY29udGVudDogdGV4dCgnY29udGVudCcpLm5vdE51bGwoKSxcbiAgXG4gIC8vIEFJIGZ1bmN0aW9uIGNhbGxpbmdcbiAgZnVuY3Rpb25DYWxsOiBqc29uKCdmdW5jdGlvbl9jYWxsJykuJHR5cGU8e1xuICAgIG5hbWU6IHN0cmluZztcbiAgICBhcmd1bWVudHM6IFJlY29yZDxzdHJpbmcsIGFueT47XG4gIH0+KCksXG4gIGZ1bmN0aW9uUmVzdWx0OiBqc29uKCdmdW5jdGlvbl9yZXN1bHQnKSxcbiAgXG4gIC8vIE1lc3NhZ2UgbWV0YWRhdGFcbiAgbWV0YWRhdGE6IGpzb24oJ21ldGFkYXRhJykuJHR5cGU8e1xuICAgIGFydGlmYWN0SWQ/OiBzdHJpbmc7XG4gICAgdG9rZW5zPzogbnVtYmVyO1xuICAgIG1vZGVsPzogc3RyaW5nO1xuICAgIHByb2Nlc3NpbmdUaW1lPzogbnVtYmVyO1xuICB9PigpLmRlZmF1bHQoe30pLFxufSk7XG5cbi8vIEFydGlmYWN0cyB0YWJsZVxuZXhwb3J0IGNvbnN0IGFydGlmYWN0cyA9IHBnVGFibGUoJ2FydGlmYWN0cycsIHtcbiAgaWQ6IHRleHQoJ2lkJykucHJpbWFyeUtleSgpLCAvLyBVVUlEXG4gIGNvbnZlcnNhdGlvbklkOiBpbnRlZ2VyKCdjb252ZXJzYXRpb25faWQnKS5yZWZlcmVuY2VzKCgpID0+IGNvbnZlcnNhdGlvbnMuaWQpLm5vdE51bGwoKSxcbiAgdXNlcklkOiB0ZXh0KCd1c2VyX2lkJykubm90TnVsbCgpLnJlZmVyZW5jZXMoKCkgPT4gdXNlcnMuaWQpLFxuICBjcmVhdGVkQXQ6IHRpbWVzdGFtcCgnY3JlYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXG4gIHVwZGF0ZWRBdDogdGltZXN0YW1wKCd1cGRhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcbiAgXG4gIC8vIEFydGlmYWN0IGluZm9ybWF0aW9uXG4gIHR5cGU6IHRleHQoJ3R5cGUnKS5ub3ROdWxsKCksIC8vIGdvYWxfc2VsZWN0b3IsIHJvYWRtYXBfcHJldmlldywgcHJvZ3Jlc3NfY2hhcnQsIGV0Yy5cbiAgdGl0bGU6IHRleHQoJ3RpdGxlJykubm90TnVsbCgpLFxuICBkZXNjcmlwdGlvbjogdGV4dCgnZGVzY3JpcHRpb24nKSxcbiAgXG4gIC8vIEFydGlmYWN0IGRhdGFcbiAgZGF0YToganNvbignZGF0YScpLm5vdE51bGwoKSxcbiAgXG4gIC8vIFN0YXRlIGFuZCBpbnRlcmFjdGlvbnNcbiAgaXNBY3RpdmU6IGJvb2xlYW4oJ2lzX2FjdGl2ZScpLmRlZmF1bHQodHJ1ZSksXG4gIGludGVyYWN0aW9uczoganNvbignaW50ZXJhY3Rpb25zJykuJHR5cGU8e1xuICAgIHRpbWVzdGFtcDogc3RyaW5nO1xuICAgIGFjdGlvbjogc3RyaW5nO1xuICAgIGRhdGE6IGFueTtcbiAgfVtdPigpLmRlZmF1bHQoW10pLFxufSk7XG5cbi8vIFJvYWRtYXBzIHRhYmxlXG5leHBvcnQgY29uc3Qgcm9hZG1hcHMgPSBwZ1RhYmxlKCdyb2FkbWFwcycsIHtcbiAgaWQ6IHNlcmlhbCgnaWQnKS5wcmltYXJ5S2V5KCksXG4gIHVzZXJJZDogdGV4dCgndXNlcl9pZCcpLm5vdE51bGwoKS5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkKSxcbiAgY3JlYXRlZEF0OiB0aW1lc3RhbXAoJ2NyZWF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxuICB1cGRhdGVkQXQ6IHRpbWVzdGFtcCgndXBkYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXG4gIFxuICAvLyBSb2FkbWFwIGluZm9ybWF0aW9uXG4gIHRpdGxlOiB0ZXh0KCd0aXRsZScpLm5vdE51bGwoKSxcbiAgZGVzY3JpcHRpb246IHRleHQoJ2Rlc2NyaXB0aW9uJyksXG4gIGdvYWw6IHRleHQoJ2dvYWwnKS5ub3ROdWxsKCksIC8vIGZvbGxvd2VyX2dyb3d0aCwgZW5nYWdlbWVudCwgZXRjLlxuICBcbiAgLy8gUm9hZG1hcCBjb25maWd1cmF0aW9uXG4gIGR1cmF0aW9uOiBpbnRlZ2VyKCdkdXJhdGlvbicpLm5vdE51bGwoKSwgLy8gZGF5c1xuICBkaWZmaWN1bHR5OiB0ZXh0KCdkaWZmaWN1bHR5Jykubm90TnVsbCgpLCAvLyBiZWdpbm5lciwgaW50ZXJtZWRpYXRlLCBhZHZhbmNlZFxuICBcbiAgLy8gUHJvZ3Jlc3MgdHJhY2tpbmdcbiAgc3RhdHVzOiB0ZXh0KCdzdGF0dXMnKS5kZWZhdWx0KCdhY3RpdmUnKSwgLy8gYWN0aXZlLCBjb21wbGV0ZWQsIHBhdXNlZFxuICBwcm9ncmVzczogaW50ZWdlcigncHJvZ3Jlc3MnKS5kZWZhdWx0KDApLCAvLyBwZXJjZW50YWdlXG4gIFxuICAvLyBSb2FkbWFwIGRhdGFcbiAgYmxvY2tzOiBqc29uKCdibG9ja3MnKS4kdHlwZTx7XG4gICAgaWQ6IHN0cmluZztcbiAgICB0aXRsZTogc3RyaW5nO1xuICAgIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gICAgb3JkZXI6IG51bWJlcjtcbiAgICBlc3RpbWF0ZWRUaW1lOiBudW1iZXI7XG4gICAgZGVwZW5kZW5jaWVzOiBzdHJpbmdbXTtcbiAgfVtdPigpLm5vdE51bGwoKSxcbiAgXG4gIC8vIE1ldGFkYXRhXG4gIGlzQ3VzdG9tOiBib29sZWFuKCdpc19jdXN0b20nKS5kZWZhdWx0KGZhbHNlKSxcbiAgdGVtcGxhdGVJZDogdGV4dCgndGVtcGxhdGVfaWQnKSxcbn0pO1xuXG4vLyBDb250ZW50IGJsb2NrcyB0YWJsZVxuZXhwb3J0IGNvbnN0IGNvbnRlbnRCbG9ja3MgPSBwZ1RhYmxlKCdjb250ZW50X2Jsb2NrcycsIHtcbiAgaWQ6IHNlcmlhbCgnaWQnKS5wcmltYXJ5S2V5KCksXG4gIGNyZWF0ZWRBdDogdGltZXN0YW1wKCdjcmVhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcbiAgXG4gIC8vIEJsb2NrIGluZm9ybWF0aW9uXG4gIHRpdGxlOiB0ZXh0KCd0aXRsZScpLm5vdE51bGwoKSxcbiAgZGVzY3JpcHRpb246IHRleHQoJ2Rlc2NyaXB0aW9uJykubm90TnVsbCgpLFxuICBjYXRlZ29yeTogdGV4dCgnY2F0ZWdvcnknKS5ub3ROdWxsKCksIC8vIHZpcmFsX2Zvcm1hdCwgc3RyYXRlZ3ksIGFuYWx5c2lzLCBldGMuXG4gIFxuICAvLyBDb250ZW50IHN0cnVjdHVyZVxuICBmb3JtYXQ6IHRleHQoJ2Zvcm1hdCcpLm5vdE51bGwoKSwgLy8gdmlzdWFsX21ldGFwaG9yLCB0d29fY2hhcmFjdGVycywgdW50b2xkX3N0b3JpZXMsIGNoYWxsZW5nZVxuICBkaWZmaWN1bHR5OiB0ZXh0KCdkaWZmaWN1bHR5Jykubm90TnVsbCgpLCAvLyBiZWdpbm5lciwgaW50ZXJtZWRpYXRlLCBhZHZhbmNlZFxuICBlc3RpbWF0ZWRUaW1lOiBpbnRlZ2VyKCdlc3RpbWF0ZWRfdGltZScpLm5vdE51bGwoKSwgLy8gbWludXRlc1xuICBcbiAgLy8gQmxvY2sgY29udGVudFxuICBjb250ZW50OiBqc29uKCdjb250ZW50JykuJHR5cGU8e1xuICAgIHNlY3Rpb25zOiB7XG4gICAgICBpZDogc3RyaW5nO1xuICAgICAgdGl0bGU6IHN0cmluZztcbiAgICAgIHR5cGU6IHN0cmluZzsgLy8gdGV4dCwgY2hlY2tsaXN0LCB0ZW1wbGF0ZSwgZXhhbXBsZVxuICAgICAgY29udGVudDogc3RyaW5nO1xuICAgICAgb3JkZXI6IG51bWJlcjtcbiAgICB9W107XG4gICAgZXhhbXBsZXM6IHtcbiAgICAgIHBsYXRmb3JtOiBzdHJpbmc7XG4gICAgICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICAgICAgY29udGVudDogc3RyaW5nO1xuICAgIH1bXTtcbiAgICB0ZW1wbGF0ZXM6IHtcbiAgICAgIG5hbWU6IHN0cmluZztcbiAgICAgIHRlbXBsYXRlOiBzdHJpbmc7XG4gICAgICB2YXJpYWJsZXM6IHN0cmluZ1tdO1xuICAgIH1bXTtcbiAgfT4oKS5ub3ROdWxsKCksXG4gIFxuICAvLyBSZXF1aXJlbWVudHMgYW5kIG91dGNvbWVzXG4gIHByZXJlcXVpc2l0ZXM6IGpzb24oJ3ByZXJlcXVpc2l0ZXMnKS4kdHlwZTxzdHJpbmdbXT4oKS5kZWZhdWx0KFtdKSxcbiAgbGVhcm5pbmdPdXRjb21lczoganNvbignbGVhcm5pbmdfb3V0Y29tZXMnKS4kdHlwZTxzdHJpbmdbXT4oKS5kZWZhdWx0KFtdKSxcbiAgXG4gIC8vIE1ldGFkYXRhXG4gIGlzQWN0aXZlOiBib29sZWFuKCdpc19hY3RpdmUnKS5kZWZhdWx0KHRydWUpLFxuICBzb3J0T3JkZXI6IGludGVnZXIoJ3NvcnRfb3JkZXInKS5kZWZhdWx0KDApLFxuICB0YWdzOiBqc29uKCd0YWdzJykuJHR5cGU8c3RyaW5nW10+KCkuZGVmYXVsdChbXSksXG59KTtcblxuLy8gVXNlciBibG9jayBwcm9ncmVzcyB0YWJsZVxuZXhwb3J0IGNvbnN0IHVzZXJCbG9ja1Byb2dyZXNzID0gcGdUYWJsZSgndXNlcl9ibG9ja19wcm9ncmVzcycsIHtcbiAgaWQ6IHNlcmlhbCgnaWQnKS5wcmltYXJ5S2V5KCksXG4gIHVzZXJJZDogdGV4dCgndXNlcl9pZCcpLm5vdE51bGwoKS5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkKSxcbiAgYmxvY2tJZDogaW50ZWdlcignYmxvY2tfaWQnKS5yZWZlcmVuY2VzKCgpID0+IGNvbnRlbnRCbG9ja3MuaWQpLm5vdE51bGwoKSxcbiAgcm9hZG1hcElkOiBpbnRlZ2VyKCdyb2FkbWFwX2lkJykucmVmZXJlbmNlcygoKSA9PiByb2FkbWFwcy5pZCksXG4gIGNyZWF0ZWRBdDogdGltZXN0YW1wKCdjcmVhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcbiAgdXBkYXRlZEF0OiB0aW1lc3RhbXAoJ3VwZGF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxuICBcbiAgLy8gUHJvZ3Jlc3MgdHJhY2tpbmdcbiAgc3RhdHVzOiB0ZXh0KCdzdGF0dXMnKS5kZWZhdWx0KCdub3Rfc3RhcnRlZCcpLCAvLyBub3Rfc3RhcnRlZCwgaW5fcHJvZ3Jlc3MsIGNvbXBsZXRlZFxuICBwcm9ncmVzczogaW50ZWdlcigncHJvZ3Jlc3MnKS5kZWZhdWx0KDApLCAvLyBwZXJjZW50YWdlXG4gIHRpbWVTcGVudDogaW50ZWdlcigndGltZV9zcGVudCcpLmRlZmF1bHQoMCksIC8vIG1pbnV0ZXNcbiAgXG4gIC8vIENvbXBsZXRpb24gZGF0YVxuICBzdGFydGVkQXQ6IHRpbWVzdGFtcCgnc3RhcnRlZF9hdCcpLFxuICBjb21wbGV0ZWRBdDogdGltZXN0YW1wKCdjb21wbGV0ZWRfYXQnKSxcbiAgXG4gIC8vIFVzZXIgcmVzcG9uc2VzIGFuZCB3b3JrXG4gIHJlc3BvbnNlczoganNvbigncmVzcG9uc2VzJykuJHR5cGU8e1xuICAgIHNlY3Rpb25JZDogc3RyaW5nO1xuICAgIHJlc3BvbnNlOiBhbnk7XG4gICAgdGltZXN0YW1wOiBzdHJpbmc7XG4gIH1bXT4oKS5kZWZhdWx0KFtdKSxcbiAgXG4gIC8vIEdlbmVyYXRlZCBjb250ZW50XG4gIGdlbmVyYXRlZENvbnRlbnQ6IGpzb24oJ2dlbmVyYXRlZF9jb250ZW50JykuJHR5cGU8e1xuICAgIHR5cGU6IHN0cmluZztcbiAgICBjb250ZW50OiBzdHJpbmc7XG4gICAgcGxhdGZvcm06IHN0cmluZztcbiAgICB0aW1lc3RhbXA6IHN0cmluZztcbiAgfVtdPigpLmRlZmF1bHQoW10pLFxuICBcbiAgLy8gRmVlZGJhY2sgYW5kIG5vdGVzXG4gIHVzZXJOb3RlczogdGV4dCgndXNlcl9ub3RlcycpLFxuICBhaWZlZWRiYWNrOiBqc29uKCdhaV9mZWVkYmFjaycpLiR0eXBlPHtcbiAgICBzY29yZTogbnVtYmVyO1xuICAgIGZlZWRiYWNrOiBzdHJpbmc7XG4gICAgc3VnZ2VzdGlvbnM6IHN0cmluZ1tdO1xuICAgIHRpbWVzdGFtcDogc3RyaW5nO1xuICB9PigpLFxufSk7XG5cbi8vIEFjaGlldmVtZW50cyB0YWJsZVxuZXhwb3J0IGNvbnN0IGFjaGlldmVtZW50cyA9IHBnVGFibGUoJ2FjaGlldmVtZW50cycsIHtcbiAgaWQ6IHNlcmlhbCgnaWQnKS5wcmltYXJ5S2V5KCksXG4gIGNyZWF0ZWRBdDogdGltZXN0YW1wKCdjcmVhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcblxuICAvLyBBY2hpZXZlbWVudCBJbmZvcm1hdGlvblxuICBuYW1lOiB0ZXh0KCduYW1lJykubm90TnVsbCgpLFxuICB0aXRsZTogdGV4dCgndGl0bGUnKS5ub3ROdWxsKCksXG4gIGRlc2NyaXB0aW9uOiB0ZXh0KCdkZXNjcmlwdGlvbicpLm5vdE51bGwoKSxcbiAgY2F0ZWdvcnk6IHRleHQoJ2NhdGVnb3J5Jykubm90TnVsbCgpLCAvLyBtaWxlc3RvbmUsIGNvbnNpc3RlbmN5LCBlbmdhZ2VtZW50LCBncm93dGgsIGNvbnRlbnRcblxuICAvLyBBY2hpZXZlbWVudCBDcml0ZXJpYVxuICBjcml0ZXJpYToganNvbignY3JpdGVyaWEnKS4kdHlwZTx7XG4gICAgdHlwZTogc3RyaW5nOyAvLyBmb2xsb3dlcl9jb3VudCwgcG9zdF9jb3VudCwgZW5nYWdlbWVudF9yYXRlLCBzdHJlYWtfZGF5cywgZXRjLlxuICAgIHRocmVzaG9sZDogbnVtYmVyO1xuICAgIHRpbWVmcmFtZT86IG51bWJlcjsgLy8gZGF5c1xuICAgIHBsYXRmb3JtPzogc3RyaW5nO1xuICB9PigpLm5vdE51bGwoKSxcblxuICAvLyBWaXN1YWwgRWxlbWVudHNcbiAgaWNvbjogdGV4dCgnaWNvbicpLm5vdE51bGwoKSxcbiAgY29sb3I6IHRleHQoJ2NvbG9yJykubm90TnVsbCgpLFxuICByYXJpdHk6IHRleHQoJ3Jhcml0eScpLm5vdE51bGwoKSwgLy8gY29tbW9uLCByYXJlLCBlcGljLCBsZWdlbmRhcnlcblxuICAvLyBSZXdhcmRzXG4gIHBvaW50czogaW50ZWdlcigncG9pbnRzJykuZGVmYXVsdCgwKSxcbiAgdW5sb2NrczoganNvbigndW5sb2NrcycpLiR0eXBlPHN0cmluZ1tdPigpLCAvLyBGZWF0dXJlcyBvciBjb250ZW50IHVubG9ja2VkXG5cbiAgLy8gTWV0YWRhdGFcbiAgaXNBY3RpdmU6IGJvb2xlYW4oJ2lzX2FjdGl2ZScpLmRlZmF1bHQodHJ1ZSksXG4gIHNvcnRPcmRlcjogaW50ZWdlcignc29ydF9vcmRlcicpLmRlZmF1bHQoMCksXG59KTtcblxuLy8gVXNlciBBY2hpZXZlbWVudHMgdGFibGVcbmV4cG9ydCBjb25zdCB1c2VyQWNoaWV2ZW1lbnRzID0gcGdUYWJsZSgndXNlcl9hY2hpZXZlbWVudHMnLCB7XG4gIGlkOiBzZXJpYWwoJ2lkJykucHJpbWFyeUtleSgpLFxuICB1c2VySWQ6IHRleHQoJ3VzZXJfaWQnKS5ub3ROdWxsKCkucmVmZXJlbmNlcygoKSA9PiB1c2Vycy5pZCksXG4gIGFjaGlldmVtZW50SWQ6IGludGVnZXIoJ2FjaGlldmVtZW50X2lkJykucmVmZXJlbmNlcygoKSA9PiBhY2hpZXZlbWVudHMuaWQpLm5vdE51bGwoKSxcbiAgY3JlYXRlZEF0OiB0aW1lc3RhbXAoJ2NyZWF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxuXG4gIC8vIEFjaGlldmVtZW50IERhdGFcbiAgdW5sb2NrZWRBdDogdGltZXN0YW1wKCd1bmxvY2tlZF9hdCcpLm5vdE51bGwoKSxcbiAgcHJvZ3Jlc3M6IGludGVnZXIoJ3Byb2dyZXNzJykuZGVmYXVsdCgxMDApLCAvLyBQZXJjZW50YWdlIHdoZW4gdW5sb2NrZWRcblxuICAvLyBDb250ZXh0XG4gIHJvYWRtYXBJZDogaW50ZWdlcigncm9hZG1hcF9pZCcpLnJlZmVyZW5jZXMoKCkgPT4gcm9hZG1hcHMuaWQpLFxuICB0cmlnZ2VyRGF0YToganNvbigndHJpZ2dlcl9kYXRhJyksIC8vIERhdGEgdGhhdCB0cmlnZ2VyZWQgdGhlIGFjaGlldmVtZW50XG5cbiAgLy8gQ2VsZWJyYXRpb25cbiAgd2FzTm90aWZpZWQ6IGJvb2xlYW4oJ3dhc19ub3RpZmllZCcpLmRlZmF1bHQoZmFsc2UpLFxuICB3YXNDZWxlYnJhdGVkOiBib29sZWFuKCd3YXNfY2VsZWJyYXRlZCcpLmRlZmF1bHQoZmFsc2UpLFxufSk7XG5cbi8vIFVzZXIgU3RhdHMgdGFibGVcbmV4cG9ydCBjb25zdCB1c2VyU3RhdHMgPSBwZ1RhYmxlKCd1c2VyX3N0YXRzJywge1xuICBpZDogc2VyaWFsKCdpZCcpLnByaW1hcnlLZXkoKSxcbiAgdXNlcklkOiB0ZXh0KCd1c2VyX2lkJykubm90TnVsbCgpLnJlZmVyZW5jZXMoKCkgPT4gdXNlcnMuaWQpLFxuICB1cGRhdGVkQXQ6IHRpbWVzdGFtcCgndXBkYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXG5cbiAgLy8gT3ZlcmFsbCBTdGF0c1xuICB0b3RhbFBvaW50czogaW50ZWdlcigndG90YWxfcG9pbnRzJykuZGVmYXVsdCgwKSxcbiAgbGV2ZWw6IGludGVnZXIoJ2xldmVsJykuZGVmYXVsdCgxKSxcbiAgZXhwZXJpZW5jZVBvaW50czogaW50ZWdlcignZXhwZXJpZW5jZV9wb2ludHMnKS5kZWZhdWx0KDApLFxuXG4gIC8vIEFjaGlldmVtZW50IFN0YXRzXG4gIHRvdGFsQWNoaWV2ZW1lbnRzOiBpbnRlZ2VyKCd0b3RhbF9hY2hpZXZlbWVudHMnKS5kZWZhdWx0KDApLFxuICBjb21tb25BY2hpZXZlbWVudHM6IGludGVnZXIoJ2NvbW1vbl9hY2hpZXZlbWVudHMnKS5kZWZhdWx0KDApLFxuICByYXJlQWNoaWV2ZW1lbnRzOiBpbnRlZ2VyKCdyYXJlX2FjaGlldmVtZW50cycpLmRlZmF1bHQoMCksXG4gIGVwaWNBY2hpZXZlbWVudHM6IGludGVnZXIoJ2VwaWNfYWNoaWV2ZW1lbnRzJykuZGVmYXVsdCgwKSxcbiAgbGVnZW5kYXJ5QWNoaWV2ZW1lbnRzOiBpbnRlZ2VyKCdsZWdlbmRhcnlfYWNoaWV2ZW1lbnRzJykuZGVmYXVsdCgwKSxcblxuICAvLyBBY3Rpdml0eSBTdGF0c1xuICBzdHJlYWtEYXlzOiBpbnRlZ2VyKCdzdHJlYWtfZGF5cycpLmRlZmF1bHQoMCksXG4gIGxvbmdlc3RTdHJlYWs6IGludGVnZXIoJ2xvbmdlc3Rfc3RyZWFrJykuZGVmYXVsdCgwKSxcbiAgbGFzdEFjdGl2aXR5RGF0ZTogdGltZXN0YW1wKCdsYXN0X2FjdGl2aXR5X2RhdGUnKSxcblxuICAvLyBDb250ZW50IFN0YXRzXG4gIHRvdGFsQ29udGVudENyZWF0ZWQ6IGludGVnZXIoJ3RvdGFsX2NvbnRlbnRfY3JlYXRlZCcpLmRlZmF1bHQoMCksXG4gIHRvdGFsQmxvY2tzQ29tcGxldGVkOiBpbnRlZ2VyKCd0b3RhbF9ibG9ja3NfY29tcGxldGVkJykuZGVmYXVsdCgwKSxcbiAgdG90YWxSb2FkbWFwc0NvbXBsZXRlZDogaW50ZWdlcigndG90YWxfcm9hZG1hcHNfY29tcGxldGVkJykuZGVmYXVsdCgwKSxcbn0pO1xuXG4vLyBEYWlseSBBY3Rpdml0eSB0YWJsZVxuZXhwb3J0IGNvbnN0IGRhaWx5QWN0aXZpdHkgPSBwZ1RhYmxlKCdkYWlseV9hY3Rpdml0eScsIHtcbiAgaWQ6IHNlcmlhbCgnaWQnKS5wcmltYXJ5S2V5KCksXG4gIHVzZXJJZDogdGV4dCgndXNlcl9pZCcpLm5vdE51bGwoKS5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkKSxcbiAgZGF0ZTogZGF0ZSgnZGF0ZScpLm5vdE51bGwoKSxcbiAgY3JlYXRlZEF0OiB0aW1lc3RhbXAoJ2NyZWF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxuXG4gIC8vIEFjdGl2aXR5IERhdGFcbiAgYWN0aXZpdGllc0NvbXBsZXRlZDoganNvbignYWN0aXZpdGllc19jb21wbGV0ZWQnKS4kdHlwZTx7XG4gICAgdHlwZTogc3RyaW5nO1xuICAgIGNvdW50OiBudW1iZXI7XG4gICAgZGV0YWlscz86IGFueTtcbiAgfVtdPigpLmRlZmF1bHQoW10pLFxuXG4gIC8vIFBvaW50cyBhbmQgRXhwZXJpZW5jZVxuICBwb2ludHNFYXJuZWQ6IGludGVnZXIoJ3BvaW50c19lYXJuZWQnKS5kZWZhdWx0KDApLFxuICBleHBlcmllbmNlRWFybmVkOiBpbnRlZ2VyKCdleHBlcmllbmNlX2Vhcm5lZCcpLmRlZmF1bHQoMCksXG5cbiAgLy8gU3RyZWFrc1xuICBpc1N0cmVha0RheTogYm9vbGVhbignaXNfc3RyZWFrX2RheScpLmRlZmF1bHQoZmFsc2UpLFxuICBzdHJlYWtDb3VudDogaW50ZWdlcignc3RyZWFrX2NvdW50JykuZGVmYXVsdCgwKSxcbn0pO1xuIl0sIm5hbWVzIjpbInBnVGFibGUiLCJzZXJpYWwiLCJ0ZXh0IiwidGltZXN0YW1wIiwianNvbiIsImludGVnZXIiLCJib29sZWFuIiwiZGF0ZSIsInVzZXJzIiwiaWQiLCJwcmltYXJ5S2V5IiwiY3JlYXRlZEF0IiwiZGVmYXVsdE5vdyIsIm5vdE51bGwiLCJ1cGRhdGVkQXQiLCJlbWFpbCIsImZpcnN0TmFtZSIsImxhc3ROYW1lIiwiaW1hZ2VVcmwiLCJwcmltYXJ5R29hbCIsInRhcmdldEF1ZGllbmNlIiwiY3VycmVudEZvbGxvd2VycyIsImRlZmF1bHQiLCJ0YXJnZXRGb2xsb3dlcnMiLCJwbGF0Zm9ybXMiLCIkdHlwZSIsImNvbnRlbnRTdHlsZSIsInBvc3RpbmdGcmVxdWVuY3kiLCJ0aW1lem9uZSIsImxhbmd1YWdlIiwic3Vic2NyaXB0aW9uU3RhdHVzIiwic3Vic2NyaXB0aW9uSWQiLCJzdWJzY3JpcHRpb25FbmRzQXQiLCJjb252ZXJzYXRpb25zIiwidXNlcklkIiwicmVmZXJlbmNlcyIsInRpdGxlIiwiaXNBY3RpdmUiLCJjb250ZXh0IiwibWVzc2FnZXMiLCJjb252ZXJzYXRpb25JZCIsInJvbGUiLCJjb250ZW50IiwiZnVuY3Rpb25DYWxsIiwiZnVuY3Rpb25SZXN1bHQiLCJtZXRhZGF0YSIsImFydGlmYWN0cyIsInR5cGUiLCJkZXNjcmlwdGlvbiIsImRhdGEiLCJpbnRlcmFjdGlvbnMiLCJyb2FkbWFwcyIsImdvYWwiLCJkdXJhdGlvbiIsImRpZmZpY3VsdHkiLCJzdGF0dXMiLCJwcm9ncmVzcyIsImJsb2NrcyIsImlzQ3VzdG9tIiwidGVtcGxhdGVJZCIsImNvbnRlbnRCbG9ja3MiLCJjYXRlZ29yeSIsImZvcm1hdCIsImVzdGltYXRlZFRpbWUiLCJwcmVyZXF1aXNpdGVzIiwibGVhcm5pbmdPdXRjb21lcyIsInNvcnRPcmRlciIsInRhZ3MiLCJ1c2VyQmxvY2tQcm9ncmVzcyIsImJsb2NrSWQiLCJyb2FkbWFwSWQiLCJ0aW1lU3BlbnQiLCJzdGFydGVkQXQiLCJjb21wbGV0ZWRBdCIsInJlc3BvbnNlcyIsImdlbmVyYXRlZENvbnRlbnQiLCJ1c2VyTm90ZXMiLCJhaWZlZWRiYWNrIiwiYWNoaWV2ZW1lbnRzIiwibmFtZSIsImNyaXRlcmlhIiwiaWNvbiIsImNvbG9yIiwicmFyaXR5IiwicG9pbnRzIiwidW5sb2NrcyIsInVzZXJBY2hpZXZlbWVudHMiLCJhY2hpZXZlbWVudElkIiwidW5sb2NrZWRBdCIsInRyaWdnZXJEYXRhIiwid2FzTm90aWZpZWQiLCJ3YXNDZWxlYnJhdGVkIiwidXNlclN0YXRzIiwidG90YWxQb2ludHMiLCJsZXZlbCIsImV4cGVyaWVuY2VQb2ludHMiLCJ0b3RhbEFjaGlldmVtZW50cyIsImNvbW1vbkFjaGlldmVtZW50cyIsInJhcmVBY2hpZXZlbWVudHMiLCJlcGljQWNoaWV2ZW1lbnRzIiwibGVnZW5kYXJ5QWNoaWV2ZW1lbnRzIiwic3RyZWFrRGF5cyIsImxvbmdlc3RTdHJlYWsiLCJsYXN0QWN0aXZpdHlEYXRlIiwidG90YWxDb250ZW50Q3JlYXRlZCIsInRvdGFsQmxvY2tzQ29tcGxldGVkIiwidG90YWxSb2FkbWFwc0NvbXBsZXRlZCIsImRhaWx5QWN0aXZpdHkiLCJhY3Rpdml0aWVzQ29tcGxldGVkIiwicG9pbnRzRWFybmVkIiwiZXhwZXJpZW5jZUVhcm5lZCIsImlzU3RyZWFrRGF5Iiwic3RyZWFrQ291bnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/ai-provider.ts":
/*!*****************************************!*\
  !*** ./src/lib/services/ai-provider.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROVIDER_CONFIG: () => (/* binding */ PROVIDER_CONFIG),\n/* harmony export */   analyzeUserIntent: () => (/* binding */ analyzeUserIntent),\n/* harmony export */   anthropicConfig: () => (/* binding */ anthropicConfig),\n/* harmony export */   callAIForCompletion: () => (/* binding */ callAIForCompletion),\n/* harmony export */   callAIForJSON: () => (/* binding */ callAIForJSON),\n/* harmony export */   deepseekConfig: () => (/* binding */ deepseekConfig),\n/* harmony export */   geminiConfig: () => (/* binding */ geminiConfig),\n/* harmony export */   generateInflutifyCompletion: () => (/* binding */ generateInflutifyCompletion),\n/* harmony export */   generateInflutifyJSON: () => (/* binding */ generateInflutifyJSON),\n/* harmony export */   generateInflutifyMentorResponse: () => (/* binding */ generateInflutifyMentorResponse),\n/* harmony export */   getApiKey: () => (/* binding */ getApiKey),\n/* harmony export */   getCurrentProvider: () => (/* binding */ getCurrentProvider),\n/* harmony export */   openaiConfig: () => (/* binding */ openaiConfig)\n/* harmony export */ });\n// AI Provider types and configurations for Influtify\n// Helper function to remove Markdown code block delimiters from streams\nfunction sanitizeStreamText(text) {\n    // Remove opening Markdown delimiters at the beginning of text\n    if (text.startsWith('```') || text.startsWith('```html')) {\n        return text.replace(/^```(?:html|markdown|text)?\\n?/, '');\n    }\n    // Remove trailing Markdown delimiters at the end of text\n    if (text.endsWith('```')) {\n        return text.replace(/```$/, '');\n    }\n    return text;\n}\n// OpenAI Configuration\nconst openaiConfig = {\n    endpoint: 'https://api.openai.com/v1/chat/completions',\n    model: 'gpt-4o-mini',\n    headers: (apiKey)=>({\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${apiKey}`\n        }),\n    formatRequest: (prompt, messages = [], systemMessage = '')=>({\n            model: 'gpt-4o-mini',\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemMessage\n                },\n                {\n                    role: \"user\",\n                    content: prompt\n                },\n                ...messages\n            ],\n            stream: true,\n            temperature: 0.1\n        }),\n    streamProcessor: async (response, controller)=>{\n        const reader = response.body?.getReader();\n        if (!reader) {\n            throw new Error(\"Response body reader not available\");\n        }\n        let buffer = \"\";\n        const encoder = new TextEncoder();\n        while(true){\n            const { done, value } = await reader.read();\n            if (done) break;\n            const chunk = new TextDecoder().decode(value);\n            buffer += chunk;\n            let lines = buffer.split(\"\\n\");\n            buffer = lines.pop() || \"\";\n            for (const line of lines){\n                if (line.startsWith(\"data: \")) {\n                    const data = line.slice(6);\n                    if (data === \"[DONE]\") continue;\n                    try {\n                        const json = JSON.parse(data);\n                        const content = json.choices[0]?.delta?.content || \"\";\n                        if (content) {\n                            const sanitizedContent = sanitizeStreamText(content);\n                            controller.enqueue(encoder.encode(sanitizedContent));\n                        }\n                    } catch (e) {\n                        console.error(\"Error parsing OpenAI stream:\", e);\n                    }\n                }\n            }\n        }\n        if (buffer && buffer.startsWith(\"data: \") && buffer !== \"data: [DONE]\") {\n            try {\n                const data = buffer.slice(6);\n                const json = JSON.parse(data);\n                const content = json.choices[0]?.delta?.content || \"\";\n                if (content) {\n                    const sanitizedContent = sanitizeStreamText(content);\n                    controller.enqueue(encoder.encode(sanitizedContent));\n                }\n            } catch (e) {\n                console.error(\"Error parsing final buffer in OpenAI stream:\", e);\n            }\n        }\n    }\n};\n// Anthropic Configuration\nconst anthropicConfig = {\n    endpoint: 'https://api.anthropic.com/v1/messages',\n    model: 'claude-3-haiku-20240307',\n    headers: (apiKey)=>({\n            'Content-Type': 'application/json',\n            'x-api-key': apiKey,\n            'anthropic-version': '2023-06-01'\n        }),\n    formatRequest: (prompt, messages = [], systemMessage = '')=>({\n            model: 'claude-3-haiku-20240307',\n            system: systemMessage,\n            messages: [\n                {\n                    role: \"user\",\n                    content: prompt\n                }\n            ],\n            temperature: 0.1,\n            stream: true\n        }),\n    streamProcessor: async (response, controller)=>{\n        const reader = response.body?.getReader();\n        if (!reader) {\n            throw new Error(\"Response body reader not available\");\n        }\n        let buffer = \"\";\n        const encoder = new TextEncoder();\n        while(true){\n            const { done, value } = await reader.read();\n            if (done) break;\n            const chunk = new TextDecoder().decode(value);\n            buffer += chunk;\n            let lines = buffer.split(\"\\n\");\n            buffer = lines.pop() || \"\";\n            for (const line of lines){\n                if (line.startsWith(\"event: content_block_delta\")) {\n                    const dataIndex = lines.indexOf(line) + 1;\n                    if (dataIndex < lines.length && lines[dataIndex].startsWith(\"data: \")) {\n                        try {\n                            const data = JSON.parse(lines[dataIndex].slice(6));\n                            if (data.delta && data.delta.text) {\n                                const sanitizedText = sanitizeStreamText(data.delta.text);\n                                controller.enqueue(encoder.encode(sanitizedText));\n                            }\n                        } catch (e) {\n                            console.error(\"Error parsing Anthropic stream:\", e);\n                        }\n                    }\n                } else if (line.startsWith(\"data: \")) {\n                    try {\n                        const data = JSON.parse(line.slice(6));\n                        if (data.type === \"content_block_delta\" && data.delta && data.delta.text) {\n                            const sanitizedText = sanitizeStreamText(data.delta.text);\n                            controller.enqueue(encoder.encode(sanitizedText));\n                        }\n                    } catch (e) {\n                    // Ignore parsing errors for lines that aren't content blocks\n                    }\n                }\n            }\n        }\n    }\n};\n// DeepSeek Configuration\nconst deepseekConfig = {\n    endpoint: 'https://api.deepseek.com/v1/chat/completions',\n    model: 'deepseek-chat',\n    headers: (apiKey)=>({\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${apiKey}`\n        }),\n    formatRequest: (prompt, messages = [], systemMessage = '')=>({\n            model: 'deepseek-chat',\n            messages: [\n                {\n                    role: \"system\",\n                    content: systemMessage\n                },\n                {\n                    role: \"user\",\n                    content: prompt\n                },\n                ...messages\n            ],\n            temperature: 0.1,\n            stream: true\n        }),\n    streamProcessor: async (response, controller)=>{\n        // DeepSeek uses the same response format as OpenAI\n        await openaiConfig.streamProcessor(response, controller);\n    }\n};\n// Gemini Configuration\nconst geminiConfig = {\n    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:streamGenerateContent',\n    model: 'gemini-2.0-flash-lite',\n    headers: (apiKey)=>({\n            'Content-Type': 'application/json'\n        }),\n    formatRequest: (prompt, messages = [], systemMessage = '')=>({\n            contents: [\n                {\n                    parts: [\n                        {\n                            text: systemMessage\n                        },\n                        {\n                            text: prompt\n                        }\n                    ]\n                }\n            ],\n            generationConfig: {\n                temperature: 0.1\n            }\n        }),\n    streamProcessor: async (response, controller)=>{\n        const reader = response.body?.getReader();\n        if (!reader) {\n            throw new Error(\"Response body reader not available for Gemini stream\");\n        }\n        let buffer = \"\";\n        const decoder = new TextDecoder();\n        const encoder = new TextEncoder();\n        try {\n            while(true){\n                const { done, value } = await reader.read();\n                if (done) {\n                    console.log(\"[Gemini Stream] Stream finished.\");\n                    break;\n                }\n                buffer += decoder.decode(value, {\n                    stream: true\n                });\n                let parseStartIndex = 0;\n                while(parseStartIndex < buffer.length){\n                    const objStartIndex = buffer.indexOf('{', parseStartIndex);\n                    if (objStartIndex === -1) {\n                        break;\n                    }\n                    let braceCount = 1;\n                    let objEndIndex = objStartIndex + 1;\n                    while(objEndIndex < buffer.length && braceCount > 0){\n                        if (buffer[objEndIndex] === '{') {\n                            braceCount++;\n                        } else if (buffer[objEndIndex] === '}') {\n                            braceCount--;\n                        }\n                        objEndIndex++;\n                    }\n                    if (braceCount === 0) {\n                        const jsonString = buffer.substring(objStartIndex, objEndIndex);\n                        try {\n                            const data = JSON.parse(jsonString);\n                            if (data.candidates && data.candidates[0]?.content?.parts) {\n                                for (const part of data.candidates[0].content.parts){\n                                    if (part.text) {\n                                        const sanitizedText = sanitizeStreamText(part.text);\n                                        controller.enqueue(encoder.encode(sanitizedText));\n                                    }\n                                }\n                            }\n                            buffer = buffer.substring(objEndIndex);\n                            parseStartIndex = 0;\n                        } catch (e) {\n                            parseStartIndex = objStartIndex + 1;\n                        }\n                    } else {\n                        break;\n                    }\n                }\n            }\n        } catch (error) {\n            console.error(\"Error reading Gemini stream:\", error);\n            controller.error(error);\n        } finally{\n            const finalChunk = decoder.decode();\n            reader.releaseLock();\n            console.log(\"[Gemini Stream] Processor finished.\");\n        }\n    }\n};\n// Provider Configuration Map\nconst PROVIDER_CONFIG = {\n    deepseek: deepseekConfig,\n    anthropic: anthropicConfig,\n    openai: openaiConfig,\n    gemini: geminiConfig\n};\n// Get current provider from environment\nfunction getCurrentProvider() {\n    const provider = process.env.AI_PROVIDER;\n    if (!provider || !PROVIDER_CONFIG[provider]) {\n        // Default to gemini if no provider is specified or invalid\n        return 'gemini';\n    }\n    return provider;\n}\n// Get API key for current provider\nfunction getApiKey(provider) {\n    const keys = {\n        deepseek: process.env.DEEPSEEK_API_KEY,\n        anthropic: process.env.ANTHROPIC_API_KEY,\n        openai: process.env.OPENAI_API_KEY,\n        gemini: process.env.GEMINI_API_KEY\n    };\n    const apiKey = keys[provider];\n    if (!apiKey) {\n        throw new Error(`API key not configured for provider: ${provider}`);\n    }\n    return apiKey;\n}\n// Helper function for non-streaming requests\nasync function callAIForCompletion(prompt, systemMessage = '', temperature = 0.1) {\n    const provider = getCurrentProvider();\n    const config = PROVIDER_CONFIG[provider];\n    const apiKey = getApiKey(provider);\n    console.log(`Using AI provider for completion: ${provider}`);\n    // Create endpoint URL (special handling for Gemini)\n    let endpoint = config.endpoint;\n    let requestBody;\n    if (provider === 'gemini') {\n        // Use a different model for non-streaming Gemini calls\n        const baseUrl = endpoint.split('/models/')[0] + '/models/';\n        const nonStreamingModel = 'gemini-2.0-flash-lite';\n        endpoint = `${baseUrl}${nonStreamingModel}:generateContent?key=${apiKey}`;\n        console.log(`[callAIForCompletion] Using ${nonStreamingModel} for non-streaming Gemini call`);\n        requestBody = {\n            ...config.formatRequest(prompt, [], systemMessage),\n            model: nonStreamingModel\n        };\n    } else {\n        endpoint = endpoint.replace('stream', '');\n        requestBody = config.formatRequest(prompt, [], systemMessage);\n    }\n    // For non-streaming requests, set stream to false\n    if (requestBody.stream !== undefined) {\n        requestBody.stream = false;\n    }\n    if (requestBody.temperature !== undefined) {\n        requestBody.temperature = temperature;\n    }\n    try {\n        const response = await fetch(endpoint, {\n            method: \"POST\",\n            headers: config.headers(apiKey),\n            body: JSON.stringify(requestBody)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(`${provider} API error: ${response.status} ${response.statusText} ${JSON.stringify(errorData)}`);\n        }\n        const data = await response.json();\n        // Extract content based on provider\n        let content = '';\n        if (provider === 'gemini') {\n            content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';\n        } else if (provider === 'anthropic') {\n            content = data.content?.[0]?.text || '';\n        } else {\n            // OpenAI and DeepSeek format\n            content = data.choices?.[0]?.message?.content || '';\n        }\n        // Clean up any markdown code blocks\n        content = content.replace(/```json\\n?/g, '').replace(/```\\n?/g, '').trim();\n        return content;\n    } catch (error) {\n        console.error(`Error calling ${provider} API for completion:`, error);\n        throw error;\n    }\n}\n/**\n * Function to call AI and parse the response as JSON\n * This is a wrapper around callAIForCompletion that ensures the response is valid JSON\n */ async function callAIForJSON(prompt, systemMessage = '', temperature = 0.1) {\n    const result = await callAIForCompletion(prompt, systemMessage, temperature);\n    // Clean up any markdown code fences from the response\n    let cleanResult = result;\n    if (cleanResult.includes('```')) {\n        cleanResult = cleanResult.replace(/```json\\n?|```\\n?|```$/gm, '');\n    }\n    try {\n        return JSON.parse(cleanResult);\n    } catch (error) {\n        console.error('Failed to parse AI response as JSON:', error);\n        throw new Error(`Failed to parse AI response as JSON: ${error}`);\n    }\n}\n/**\n * Influtify-specific AI mentor response generator\n * Generates streaming responses with social media growth context\n */ async function generateInflutifyMentorResponse(userMessage, context) {\n    const provider = getCurrentProvider();\n    const config = PROVIDER_CONFIG[provider];\n    const apiKey = getApiKey(provider);\n    console.log(`[Influtify] Using AI provider: ${provider}`);\n    // Build Influtify-specific system prompt\n    const systemPrompt = buildInflutifySystemPrompt(context);\n    // Format conversation history for context\n    const conversationContext = formatConversationHistory(context.conversationHistory || []);\n    // Create the final prompt with context\n    const fullPrompt = conversationContext ? `${conversationContext}\\n\\nUser: ${userMessage}` : userMessage;\n    // Create the endpoint URL (special handling for Gemini)\n    let endpoint = config.endpoint;\n    if (provider === 'gemini') {\n        endpoint = `${endpoint}?key=${apiKey}`;\n    }\n    // Create request body based on provider\n    const requestBody = config.formatRequest(fullPrompt, [], systemPrompt);\n    // Create stream\n    return new ReadableStream({\n        async start (controller) {\n            try {\n                console.log(`[Influtify] Calling ${provider} API for mentor response`);\n                const res = await fetch(endpoint, {\n                    method: \"POST\",\n                    headers: config.headers(apiKey),\n                    body: JSON.stringify(requestBody)\n                });\n                if (!res.ok) {\n                    const errorData = await res.json().catch(()=>({}));\n                    const errorMessage = `${provider} API error: ${res.status} ${res.statusText} ${JSON.stringify(errorData)}`;\n                    console.error(errorMessage);\n                    controller.error(new Error(errorMessage));\n                    return;\n                }\n                // Process the stream using provider-specific logic\n                await config.streamProcessor(res, controller);\n            } catch (e) {\n                console.error(`[Influtify] Error in ${provider} stream:`, e);\n                controller.error(e);\n            } finally{\n                controller.close();\n            }\n        }\n    });\n}\n/**\n * Generate non-streaming AI response for Influtify use cases\n */ async function generateInflutifyCompletion(prompt, context, temperature = 0.1) {\n    const systemPrompt = buildInflutifySystemPrompt(context);\n    return callAIForCompletion(prompt, systemPrompt, temperature);\n}\n/**\n * Generate structured JSON response for Influtify features\n */ async function generateInflutifyJSON(prompt, context, temperature = 0.1) {\n    const systemPrompt = buildInflutifySystemPrompt(context);\n    return callAIForJSON(prompt, systemPrompt, temperature);\n}\n/**\n * Build Influtify-specific system prompt based on user context\n */ function buildInflutifySystemPrompt(context) {\n    const { userProfile, currentGoals, platform, followerCount } = context;\n    return `You are an AI social media growth mentor for Influtify, based on Brendan Kane's \"The Guide to Going Viral\" methodology. Your expertise comes from proven strategies that have helped creators achieve viral success.\n\nCORE MISSION:\nHelp users grow their social media presence through strategic, data-driven content creation and audience engagement.\n\nBRENDAN KANE'S VIRAL METHODOLOGY:\n1. The 4 Viral Content Formats:\n   - Educational: Teach something valuable\n   - Entertaining: Make people laugh or feel good\n   - Inspirational: Motivate and uplift\n   - Conversational: Start discussions and debates\n\n2. The Hook-Retain-Reward Framework:\n   - Hook: Grab attention in first 3 seconds\n   - Retain: Keep viewers engaged throughout\n   - Reward: Provide value that makes sharing worthwhile\n\n3. Platform-Specific Optimization:\n   - Understand each platform's algorithm\n   - Adapt content format to platform strengths\n   - Time posting for maximum engagement\n\nUSER CONTEXT:\n${userProfile ? `\n- Name: ${userProfile.firstName || 'User'}\n- Platform Focus: ${platform || 'Not specified'}\n- Current Followers: ${followerCount || 'Not specified'}\n- Goals: ${currentGoals?.join(', ') || 'Growing social media presence'}\n` : '- New user starting their growth journey'}\n\nRESPONSE GUIDELINES:\n- Be encouraging and supportive while providing actionable advice\n- Ask strategic questions to understand their specific challenges\n- Provide concrete examples and step-by-step guidance\n- Reference specific viral strategies when relevant\n- Focus on measurable outcomes and testing approaches\n- Adapt advice to their current follower count and platform\n- Use emojis strategically to maintain engagement\n- Always end with a clear next action step\n\nCONVERSATION STYLE:\n- Conversational and friendly, like a knowledgeable mentor\n- Confident in your expertise but humble in approach\n- Focus on practical implementation over theory\n- Celebrate small wins and progress\n- Challenge users to think bigger while being realistic\n\nRemember: Your goal is to transform users from content creators into viral content strategists using proven methodologies.`;\n}\n/**\n * Format conversation history for AI context\n */ function formatConversationHistory(messages) {\n    if (!messages.length) return '';\n    // Take last 10 messages to avoid token limits\n    const recentMessages = messages.slice(-10);\n    return recentMessages.map((msg)=>{\n        const role = msg.role === 'user' ? 'User' : 'Mentor';\n        return `${role}: ${msg.content}`;\n    }).join('\\n');\n}\n/**\n * Extract user intent and goals from message\n */ function analyzeUserIntent(message) {\n    const lowerMessage = message.toLowerCase();\n    // Detect platform mentions\n    const platforms = [\n        'instagram',\n        'tiktok',\n        'youtube',\n        'twitter',\n        'linkedin',\n        'facebook'\n    ];\n    const mentionedPlatform = platforms.find((platform)=>lowerMessage.includes(platform));\n    // Detect content types\n    const contentTypes = [\n        'video',\n        'reel',\n        'post',\n        'story',\n        'short',\n        'carousel'\n    ];\n    const mentionedContentType = contentTypes.find((type)=>lowerMessage.includes(type));\n    // Determine intent\n    let intent = 'general';\n    if (lowerMessage.includes('goal') || lowerMessage.includes('want to') || lowerMessage.includes('trying to')) {\n        intent = 'goal_setting';\n    } else if (lowerMessage.includes('?') || lowerMessage.includes('how') || lowerMessage.includes('what') || lowerMessage.includes('why')) {\n        intent = 'question';\n    } else if (lowerMessage.includes('review') || lowerMessage.includes('feedback') || lowerMessage.includes('thoughts on')) {\n        intent = 'content_review';\n    } else if (lowerMessage.includes('strategy') || lowerMessage.includes('plan') || lowerMessage.includes('approach')) {\n        intent = 'strategy_request';\n    }\n    // Determine urgency\n    let urgency = 'medium';\n    if (lowerMessage.includes('urgent') || lowerMessage.includes('asap') || lowerMessage.includes('quickly')) {\n        urgency = 'high';\n    } else if (lowerMessage.includes('when i have time') || lowerMessage.includes('eventually')) {\n        urgency = 'low';\n    }\n    return {\n        intent,\n        platform: mentionedPlatform,\n        contentType: mentionedContentType,\n        urgency\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/ai-provider.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/conversation.ts":
/*!******************************************!*\
  !*** ./src/lib/services/conversation.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationService: () => (/* binding */ ConversationService),\n/* harmony export */   conversationService: () => (/* binding */ conversationService)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nclass ConversationService {\n    /**\n   * Create a new conversation\n   */ async createConversation(userId, title) {\n        const conversation = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations).values({\n            userId,\n            title: title || 'New Conversation',\n            createdAt: new Date(),\n            updatedAt: new Date()\n        }).returning();\n        return conversation[0].id;\n    }\n    /**\n   * Get conversation history\n   */ async getConversationHistory(conversationId) {\n        const messageHistory = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.messages).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.messages.conversationId, conversationId)).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.messages.createdAt);\n        return messageHistory.map((msg)=>({\n                role: msg.role,\n                content: msg.content\n            }));\n    }\n    /**\n   * Save message to conversation\n   */ async saveMessage(conversationId, role, content, metadata) {\n        await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.messages).values({\n            conversationId,\n            role,\n            content,\n            metadata,\n            createdAt: new Date()\n        });\n        // Update conversation timestamp\n        await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations).set({\n            updatedAt: new Date()\n        }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations.id, conversationId));\n    }\n    /**\n   * Build system prompt with user context\n   * This method is used by the API routes for generating context-aware prompts\n   */ buildSystemPrompt(context) {\n        const { userProfile, currentGoals } = context;\n        let prompt = `You are Influtify, an AI social media growth mentor based on Brendan Kane's \"The Guide to Going Viral\" methodology.\n\nYour expertise includes:\n- The 4 viral content formats: Educational, Entertaining, Inspirational, Conversational\n- Hook-Retain-Reward framework for viral content\n- Platform-specific strategies for Instagram, TikTok, YouTube, Twitter, LinkedIn\n- Audience analysis and engagement optimization\n- Content planning and consistency strategies\n\n`;\n        if (userProfile) {\n            prompt += `User Profile:\n- Platform focus: ${userProfile.platform || 'Not specified'}\n- Current follower count: ${userProfile.followerCount || 'Not specified'}\n- Content niche: ${userProfile.niche || 'Not specified'}\n- Experience level: ${userProfile.experienceLevel || 'Beginner'}\n\n`;\n        }\n        if (currentGoals && currentGoals.length > 0) {\n            prompt += `Current Goals:\n${currentGoals.map((goal)=>`- ${goal}`).join('\\n')}\n\n`;\n        }\n        prompt += `Provide actionable, specific advice based on proven viral strategies. Always include practical next steps the user can implement immediately.`;\n        return prompt;\n    }\n    /**\n   * Get user's recent conversations\n   */ async getUserConversations(userId, limit = 10) {\n        return await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations.userId, userId)).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations.updatedAt)).limit(limit);\n    }\n    /**\n   * Update conversation title\n   */ async updateConversationTitle(conversationId, title) {\n        await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations).set({\n            title,\n            updatedAt: new Date()\n        }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations.id, conversationId));\n    }\n    /**\n   * Delete conversation and all its messages\n   */ async deleteConversation(conversationId) {\n        // Delete messages first (due to foreign key constraint)\n        await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.delete(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.messages).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.messages.conversationId, conversationId));\n        // Delete conversation\n        await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.delete(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations.id, conversationId));\n    }\n    /**\n   * Get conversation by ID with basic info\n   */ async getConversation(conversationId) {\n        const conversation = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_2__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.conversations.id, conversationId)).limit(1);\n        return conversation[0] || null;\n    }\n}\nconst conversationService = new ConversationService();\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/conversation.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/user.ts":
/*!**********************************!*\
  !*** ./src/lib/services/user.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserService: () => (/* binding */ UserService)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs/server */ \"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/server/auth.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_db__WEBPACK_IMPORTED_MODULE_0__]);\n_lib_db__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nclass UserService {\n    /**\n   * Get the current authenticated user's profile\n   */ static async getCurrentUser() {\n        const { userId } = await (0,_clerk_nextjs_server__WEBPACK_IMPORTED_MODULE_2__.auth)();\n        if (!userId) return null;\n        const user = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.clerkId, userId)).limit(1);\n        return user[0] || null;\n    }\n    /**\n   * Get user by Clerk ID\n   */ static async getUserByClerkId(clerkId) {\n        const user = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.clerkId, clerkId)).limit(1);\n        return user[0] || null;\n    }\n    /**\n   * Create or update user profile\n   */ static async upsertUser(userData) {\n        const existingUser = await this.getUserByClerkId(userData.clerkId);\n        if (existingUser) {\n            // Update existing user\n            const updated = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).set({\n                ...userData,\n                updatedAt: new Date()\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.clerkId, userData.clerkId)).returning();\n            return updated[0];\n        } else {\n            // Create new user\n            const created = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).values({\n                ...userData,\n                createdAt: new Date(),\n                updatedAt: new Date()\n            }).returning();\n            // Initialize user stats\n            await this.initializeUserStats(created[0].id);\n            return created[0];\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(clerkId, updates) {\n        const updated = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).set({\n            ...updates,\n            updatedAt: new Date()\n        }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.clerkId, clerkId)).returning();\n        return updated[0] || null;\n    }\n    /**\n   * Get user statistics\n   */ static async getUserStats(userId) {\n        const stats = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userStats).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userStats.userId, userId)).limit(1);\n        return stats[0] || null;\n    }\n    /**\n   * Initialize user statistics for new user\n   */ static async initializeUserStats(userId) {\n        await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userStats).values({\n            userId,\n            totalPoints: 0,\n            currentStreak: 0,\n            longestStreak: 0,\n            level: 1,\n            completedBlocks: 0,\n            totalSessions: 0,\n            lastActiveAt: new Date()\n        });\n    }\n    /**\n   * Update user statistics\n   */ static async updateStats(userId, updates) {\n        const updated = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userStats).set(updates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userStats.userId, userId)).returning();\n        return updated[0] || null;\n    }\n    /**\n   * Award achievement to user\n   */ static async awardAchievement(userId, achievementId) {\n        // Check if user already has this achievement\n        const existing = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements.userId, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements.achievementId, achievementId))).limit(1);\n        if (existing.length === 0) {\n            await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements).values({\n                userId,\n                achievementId,\n                unlockedAt: new Date()\n            });\n        }\n    }\n    /**\n   * Get user achievements\n   */ static async getUserAchievements(userId) {\n        return await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select({\n            achievement: _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.achievements,\n            unlockedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements.unlockedAt\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.achievements, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.achievements.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements.achievementId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userAchievements.userId, userId));\n    }\n    /**\n   * Add points to user and check for level up\n   */ static async addPoints(userId, points) {\n        const currentStats = await this.getUserStats(userId);\n        if (!currentStats) throw new Error('User stats not found');\n        const newPoints = currentStats.totalPoints + points;\n        const newLevel = Math.floor(newPoints / 1000) + 1; // 1000 points per level\n        const leveledUp = newLevel > currentStats.level;\n        await this.updateStats(userId, {\n            totalPoints: newPoints,\n            level: newLevel,\n            lastActiveAt: new Date()\n        });\n        return {\n            leveledUp,\n            newLevel: leveledUp ? newLevel : undefined\n        };\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/user.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = import("pg");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@clerk","vendor-chunks/next","vendor-chunks/tslib","vendor-chunks/cookie","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/drizzle-orm","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fai%2Fstream%2Froute&page=%2Fapi%2Fai%2Fstream%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai%2Fstream%2Froute.ts&appDir=%2FUsers%2Fbrunao%2FPROJECTS%2Finflutify%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbrunao%2FPROJECTS%2Finflutify&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();