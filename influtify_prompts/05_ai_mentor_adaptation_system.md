# Influtify AI Mentor Adaptation System (Chat-Integrated) Prompt

## Feature Overview
Integrate the AI mentor adaptation system with the chat interface, allowing the AI to proactively suggest strategy adjustments, provide progress updates, and guide users through optimization decisions conversationally. This system monitors user progress, identifies when strategies aren't working, and provides intelligent recommendations through natural conversation and interactive artifacts.

## Technical Requirements
- Implement using NextJS with the sliced-feature architecture
- Use Zod for data validation
- Use Drizzle ORM for database operations
- Implement UI with DaisyUI components
- Support multilanguage with next-intl
- Integrate with the AI chat system
- Create automated progress monitoring
- Implement intelligent recommendation engine

## Database Schema
Extend the Drizzle schema with mentor adaptation tables:

```typescript
// Progress Snapshots Schema
export const progressSnapshots = pgTable('progress_snapshots', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  roadmapId: integer('roadmap_id').references(() => roadmaps.id).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Snapshot Data
  metrics: json('metrics').$type<{
    followers: number;
    engagement: number;
    views: number;
    likes: number;
    comments: number;
    shares: number;
    [key: string]: number;
  }>().notNull(),
  
  // Goals at time of snapshot
  targetMetrics: json('target_metrics').$type<{
    followers: number;
    engagement: number;
    timeframe: number; // days
    [key: string]: number;
  }>().notNull(),
  
  // Progress Analysis
  isOnTrack: boolean('is_on_track').notNull(),
  projectedValues: json('projected_values').$type<{
    [key: string]: number;
  }>(),
  
  // Context
  daysIntoGoal: integer('days_into_goal').notNull(),
  totalDays: integer('total_days').notNull(),
});

// AI Recommendations Schema
export const aiRecommendations = pgTable('ai_recommendations', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  roadmapId: integer('roadmap_id').references(() => roadmaps.id),
  conversationId: integer('conversation_id').references(() => conversations.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Recommendation Information
  type: text('type').notNull(), // strategy_adjustment, content_optimization, engagement_boost, format_change
  title: text('title').notNull(),
  description: text('description').notNull(),
  reasoning: text('reasoning').notNull(),
  
  // Recommendation Data
  recommendations: json('recommendations').$type<{
    id: string;
    type: string;
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    effort: 'high' | 'medium' | 'low';
    changes: any;
  }[]>().notNull(),
  
  // Current Situation
  currentProgress: json('current_progress').$type<{
    metric: string;
    current: number;
    target: number;
    projected: number;
  }>().notNull(),
  
  // User Response
  userResponse: text('user_response'), // accepted, rejected, modified
  selectedRecommendations: json('selected_recommendations').$type<string[]>(),
  responseAt: timestamp('response_at'),
  
  // Implementation
  implementedAt: timestamp('implemented_at'),
  implementationNotes: text('implementation_notes'),
});

// Strategy Adjustments Schema
export const strategyAdjustments = pgTable('strategy_adjustments', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  roadmapId: integer('roadmap_id').references(() => roadmaps.id).notNull(),
  recommendationId: integer('recommendation_id').references(() => aiRecommendations.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Adjustment Information
  adjustmentType: text('adjustment_type').notNull(), // viral_format_change, content_frequency, engagement_strategy, target_adjustment
  
  // Before/After
  previousStrategy: json('previous_strategy').notNull(),
  newStrategy: json('new_strategy').notNull(),
  
  // Results Tracking
  effectiveFrom: timestamp('effective_from').notNull(),
  resultsEvaluatedAt: timestamp('results_evaluated_at'),
  wasSuccessful: boolean('was_successful'),
  impactMetrics: json('impact_metrics').$type<{
    [key: string]: {
      before: number;
      after: number;
      change: number;
      changePercent: number;
    };
  }>(),
});

// Mentor Check-ins Schema
export const mentorCheckins = pgTable('mentor_checkins', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  roadmapId: integer('roadmap_id').references(() => roadmaps.id).notNull(),
  conversationId: integer('conversation_id').references(() => conversations.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Check-in Information
  checkinType: text('checkin_type').notNull(), // weekly, milestone, emergency
  triggerReason: text('trigger_reason'), // scheduled, off_track, user_request, milestone_reached
  
  // Check-in Data
  currentMetrics: json('current_metrics').notNull(),
  progressAnalysis: json('progress_analysis').notNull(),
  
  // AI Assessment
  aiAssessment: text('ai_assessment').notNull(),
  recommendationsGenerated: boolean('recommendations_generated').default(false),
  
  // User Interaction
  userEngaged: boolean('user_engaged').default(false),
  userFeedback: text('user_feedback'),
  nextCheckinScheduled: timestamp('next_checkin_scheduled'),
});
```

## AI Mentor Service
Implement the core AI mentor service:

```typescript
export class AIMentorService {
  private progressAnalyzer: ProgressAnalyzer;
  private recommendationEngine: RecommendationEngine;
  private chatService: AIChatService;
  
  constructor() {
    this.progressAnalyzer = new ProgressAnalyzer();
    this.recommendationEngine = new RecommendationEngine();
    this.chatService = new AIChatService();
  }
  
  // Analyze user progress and determine if intervention is needed
  async analyzeProgress(userId: string, roadmapId: number): Promise<{
    isOnTrack: boolean;
    needsIntervention: boolean;
    analysis: ProgressAnalysis;
  }> {
    // Get latest progress snapshot
    const latestSnapshot = await db.query.progressSnapshots.findFirst({
      where: and(
        eq(progressSnapshots.userId, userId),
        eq(progressSnapshots.roadmapId, roadmapId)
      ),
      orderBy: desc(progressSnapshots.createdAt),
    });
    
    if (!latestSnapshot) {
      return {
        isOnTrack: true,
        needsIntervention: false,
        analysis: { message: 'No progress data available yet' },
      };
    }
    
    // Analyze progress
    const analysis = await this.progressAnalyzer.analyze(latestSnapshot);
    
    // Determine if intervention is needed
    const needsIntervention = this.shouldIntervene(analysis, latestSnapshot);
    
    return {
      isOnTrack: latestSnapshot.isOnTrack,
      needsIntervention,
      analysis,
    };
  }
  
  // Create progress check-in conversation
  async createProgressCheckin(
    userId: string,
    roadmapId: number,
    triggerReason: string
  ): Promise<{
    conversationId: number;
    checkinId: number;
    initialMessage: string;
  }> {
    // Get current progress
    const progressAnalysis = await this.analyzeProgress(userId, roadmapId);
    
    // Create new conversation for check-in
    const conversation = await db.insert(conversations).values({
      userId,
      title: 'Progress Check-in',
      status: 'active',
      currentStep: 'mentoring',
    }).returning().then(rows => rows[0]);
    
    // Create check-in record
    const checkin = await db.insert(mentorCheckins).values({
      userId,
      roadmapId,
      conversationId: conversation.id,
      checkinType: this.getCheckinType(triggerReason),
      triggerReason,
      currentMetrics: progressAnalysis.analysis.currentMetrics || {},
      progressAnalysis: progressAnalysis.analysis,
      aiAssessment: this.generateAssessmentMessage(progressAnalysis),
    }).returning().then(rows => rows[0]);
    
    // Generate initial check-in message
    const initialMessage = await this.generateCheckinMessage(
      progressAnalysis,
      triggerReason
    );
    
    // Save initial message
    await db.insert(messages).values({
      conversationId: conversation.id,
      role: 'assistant',
      content: initialMessage,
    });
    
    return {
      conversationId: conversation.id,
      checkinId: checkin.id,
      initialMessage,
    };
  }
  
  // Generate strategy adjustment recommendations
  async generateRecommendations(
    userId: string,
    roadmapId: number,
    progressAnalysis: ProgressAnalysis
  ): Promise<AIRecommendation> {
    const recommendations = await this.recommendationEngine.generateRecommendations(
      progressAnalysis,
      userId,
      roadmapId
    );
    
    // Save recommendations to database
    const aiRecommendation = await db.insert(aiRecommendations).values({
      userId,
      roadmapId,
      type: 'strategy_adjustment',
      title: 'Strategy Adjustment Needed',
      description: 'Based on your current progress, I recommend adjusting your strategy',
      reasoning: recommendations.reasoning,
      recommendations: recommendations.items,
      currentProgress: progressAnalysis.currentProgress,
    }).returning().then(rows => rows[0]);
    
    return aiRecommendation;
  }
  
  // Apply strategy adjustments
  async applyStrategyAdjustments(
    userId: string,
    roadmapId: number,
    recommendationId: number,
    selectedRecommendations: string[]
  ): Promise<void> {
    // Get the recommendation
    const recommendation = await db.query.aiRecommendations.findFirst({
      where: eq(aiRecommendations.id, recommendationId),
    });
    
    if (!recommendation) {
      throw new Error('Recommendation not found');
    }
    
    // Get current roadmap
    const roadmap = await db.query.roadmaps.findFirst({
      where: eq(roadmaps.id, roadmapId),
    });
    
    if (!roadmap) {
      throw new Error('Roadmap not found');
    }
    
    // Apply each selected recommendation
    for (const recId of selectedRecommendations) {
      const rec = recommendation.recommendations.find(r => r.id === recId);
      if (!rec) continue;
      
      // Create strategy adjustment record
      await db.insert(strategyAdjustments).values({
        userId,
        roadmapId,
        recommendationId,
        adjustmentType: rec.type,
        previousStrategy: roadmap.strategy || {},
        newStrategy: rec.changes,
        effectiveFrom: new Date(),
      });
      
      // Apply changes to roadmap
      await this.applyChangesToRoadmap(roadmapId, rec.changes);
    }
    
    // Update recommendation with user response
    await db.update(aiRecommendations)
      .set({
        userResponse: 'accepted',
        selectedRecommendations,
        responseAt: new Date(),
      })
      .where(eq(aiRecommendations.id, recommendationId));
  }
  
  // Schedule next check-in
  async scheduleNextCheckin(
    userId: string,
    roadmapId: number,
    daysFromNow: number = 7
  ): Promise<void> {
    const nextCheckinDate = new Date();
    nextCheckinDate.setDate(nextCheckinDate.getDate() + daysFromNow);
    
    // This would integrate with a job scheduler in a real implementation
    // For now, we'll just store the scheduled date
    await db.update(mentorCheckins)
      .set({
        nextCheckinScheduled: nextCheckinDate,
      })
      .where(and(
        eq(mentorCheckins.userId, userId),
        eq(mentorCheckins.roadmapId, roadmapId)
      ));
  }
  
  private shouldIntervene(analysis: ProgressAnalysis, snapshot: any): boolean {
    // Intervention logic
    if (!snapshot.isOnTrack) return true;
    if (analysis.significantDecline) return true;
    if (analysis.stagnation) return true;
    if (analysis.opportunityDetected) return true;
    
    return false;
  }
  
  private getCheckinType(triggerReason: string): string {
    if (triggerReason === 'scheduled') return 'weekly';
    if (triggerReason === 'milestone_reached') return 'milestone';
    return 'emergency';
  }
  
  private generateAssessmentMessage(progressAnalysis: any): string {
    if (progressAnalysis.isOnTrack) {
      return 'User is making good progress toward their goals.';
    } else {
      return 'User is behind schedule and may need strategy adjustments.';
    }
  }
  
  private async generateCheckinMessage(
    progressAnalysis: any,
    triggerReason: string
  ): Promise<string> {
    const prompts = {
      scheduled: `Hi! It's time for our weekly check-in. Let's see how you're doing with your social media growth goals.`,
      off_track: `I've been monitoring your progress, and it looks like we might need to adjust your strategy. Let's talk about what's happening.`,
      milestone_reached: `Congratulations! You've reached an important milestone. Let's celebrate and plan your next steps.`,
      user_request: `I'm here to help! What would you like to discuss about your social media growth journey?`,
    };
    
    return prompts[triggerReason as keyof typeof prompts] || prompts.user_request;
  }
  
  private async applyChangesToRoadmap(roadmapId: number, changes: any): Promise<void> {
    // Apply changes to the roadmap based on the recommendation
    // This could involve updating content blocks, changing viral formats, etc.
    await db.update(roadmaps)
      .set({
        strategy: changes,
        updatedAt: new Date(),
      })
      .where(eq(roadmaps.id, roadmapId));
  }
}
```

## Progress Analyzer
Implement progress analysis logic:

```typescript
export class ProgressAnalyzer {
  async analyze(snapshot: any): Promise<ProgressAnalysis> {
    const analysis: ProgressAnalysis = {
      currentMetrics: snapshot.metrics,
      targetMetrics: snapshot.targetMetrics,
      projectedValues: snapshot.projectedValues,
      daysRemaining: snapshot.totalDays - snapshot.daysIntoGoal,
      progressPercentage: this.calculateProgressPercentage(snapshot),
      trends: await this.analyzeTrends(snapshot.userId, snapshot.roadmapId),
      insights: [],
      recommendations: [],
    };
    
    // Analyze different aspects
    analysis.isOnTrack = this.isOnTrack(analysis);
    analysis.significantDecline = this.hasSignificantDecline(analysis.trends);
    analysis.stagnation = this.hasStagnation(analysis.trends);
    analysis.opportunityDetected = this.hasOpportunity(analysis.trends);
    
    // Generate insights
    analysis.insights = this.generateInsights(analysis);
    
    return analysis;
  }
  
  private calculateProgressPercentage(snapshot: any): number {
    const current = snapshot.metrics.followers || 0;
    const target = snapshot.targetMetrics.followers || 1;
    const start = snapshot.metrics.startingFollowers || 0;
    
    return ((current - start) / (target - start)) * 100;
  }
  
  private async analyzeTrends(userId: string, roadmapId: number): Promise<any> {
    // Get recent snapshots for trend analysis
    const recentSnapshots = await db.query.progressSnapshots.findMany({
      where: and(
        eq(progressSnapshots.userId, userId),
        eq(progressSnapshots.roadmapId, roadmapId)
      ),
      orderBy: desc(progressSnapshots.createdAt),
      limit: 10,
    });
    
    if (recentSnapshots.length < 2) {
      return { insufficient_data: true };
    }
    
    // Calculate trends
    const trends = {
      followers: this.calculateTrend(recentSnapshots, 'followers'),
      engagement: this.calculateTrend(recentSnapshots, 'engagement'),
      views: this.calculateTrend(recentSnapshots, 'views'),
    };
    
    return trends;
  }
  
  private calculateTrend(snapshots: any[], metric: string): any {
    const values = snapshots.map(s => s.metrics[metric] || 0).reverse();
    
    if (values.length < 2) return { trend: 'stable', change: 0 };
    
    const recent = values.slice(-3).reduce((a, b) => a + b, 0) / 3;
    const older = values.slice(0, 3).reduce((a, b) => a + b, 0) / 3;
    
    const change = ((recent - older) / older) * 100;
    
    return {
      trend: change > 5 ? 'up' : change < -5 ? 'down' : 'stable',
      change: change,
      recent: recent,
      older: older,
    };
  }
  
  private isOnTrack(analysis: ProgressAnalysis): boolean {
    const timeProgress = (analysis.daysRemaining / analysis.targetMetrics.timeframe) * 100;
    const goalProgress = analysis.progressPercentage;
    
    // Should be at least 80% of expected progress
    return goalProgress >= (timeProgress * 0.8);
  }
  
  private hasSignificantDecline(trends: any): boolean {
    return Object.values(trends).some((trend: any) => 
      trend.trend === 'down' && Math.abs(trend.change) > 20
    );
  }
  
  private hasStagnation(trends: any): boolean {
    return Object.values(trends).every((trend: any) => 
      trend.trend === 'stable' && Math.abs(trend.change) < 2
    );
  }
  
  private hasOpportunity(trends: any): boolean {
    return Object.values(trends).some((trend: any) => 
      trend.trend === 'up' && trend.change > 15
    );
  }
  
  private generateInsights(analysis: ProgressAnalysis): string[] {
    const insights: string[] = [];
    
    if (analysis.isOnTrack) {
      insights.push("You're making great progress toward your goal!");
    } else {
      insights.push("You're currently behind schedule on your goal.");
    }
    
    if (analysis.trends.followers?.trend === 'up') {
      insights.push(`Your follower growth is trending upward (+${analysis.trends.followers.change.toFixed(1)}%)`);
    }
    
    if (analysis.trends.engagement?.trend === 'down') {
      insights.push(`Your engagement rate has declined recently (-${Math.abs(analysis.trends.engagement.change).toFixed(1)}%)`);
    }
    
    if (analysis.stagnation) {
      insights.push("Your metrics have been stagnant recently. Consider trying new content formats.");
    }
    
    return insights;
  }
}
```

## Recommendation Engine
Implement intelligent recommendation generation:

```typescript
export class RecommendationEngine {
  async generateRecommendations(
    analysis: ProgressAnalysis,
    userId: string,
    roadmapId: number
  ): Promise<{
    reasoning: string;
    items: RecommendationItem[];
  }> {
    const recommendations: RecommendationItem[] = [];
    
    // Get user's current strategy
    const roadmap = await db.query.roadmaps.findFirst({
      where: eq(roadmaps.id, roadmapId),
    });
    
    if (!roadmap) {
      throw new Error('Roadmap not found');
    }
    
    // Generate recommendations based on analysis
    if (!analysis.isOnTrack) {
      recommendations.push(...this.getOffTrackRecommendations(analysis, roadmap));
    }
    
    if (analysis.significantDecline) {
      recommendations.push(...this.getDeclineRecommendations(analysis, roadmap));
    }
    
    if (analysis.stagnation) {
      recommendations.push(...this.getStagnationRecommendations(analysis, roadmap));
    }
    
    if (analysis.opportunityDetected) {
      recommendations.push(...this.getOpportunityRecommendations(analysis, roadmap));
    }
    
    // Generate reasoning
    const reasoning = this.generateReasoning(analysis, recommendations);
    
    return {
      reasoning,
      items: recommendations.slice(0, 5), // Limit to 5 recommendations
    };
  }
  
  private getOffTrackRecommendations(analysis: ProgressAnalysis, roadmap: any): RecommendationItem[] {
    return [
      {
        id: 'increase_posting_frequency',
        type: 'content_frequency',
        title: 'Increase Posting Frequency',
        description: 'Post more frequently to increase visibility and engagement',
        impact: 'high',
        effort: 'medium',
        changes: {
          postingFrequency: 'daily',
          contentVolume: 'increased',
        },
      },
      {
        id: 'optimize_posting_times',
        type: 'timing_optimization',
        title: 'Optimize Posting Times',
        description: 'Post when your audience is most active',
        impact: 'medium',
        effort: 'low',
        changes: {
          postingSchedule: 'optimized',
          timingStrategy: 'audience_based',
        },
      },
    ];
  }
  
  private getDeclineRecommendations(analysis: ProgressAnalysis, roadmap: any): RecommendationItem[] {
    return [
      {
        id: 'change_viral_format',
        type: 'viral_format_change',
        title: 'Try a Different Viral Format',
        description: 'Switch to a viral format that might resonate better with your audience',
        impact: 'high',
        effort: 'high',
        changes: {
          viralFormat: this.suggestAlternativeFormat(roadmap.viralFormat),
          contentStrategy: 'refreshed',
        },
      },
      {
        id: 'improve_content_quality',
        type: 'content_optimization',
        title: 'Focus on Content Quality',
        description: 'Spend more time on content creation and editing',
        impact: 'high',
        effort: 'medium',
        changes: {
          qualityFocus: 'increased',
          productionTime: 'extended',
        },
      },
    ];
  }
  
  private getStagnationRecommendations(analysis: ProgressAnalysis, roadmap: any): RecommendationItem[] {
    return [
      {
        id: 'experiment_with_trends',
        type: 'trend_adoption',
        title: 'Experiment with Current Trends',
        description: 'Incorporate trending topics and formats into your content',
        impact: 'medium',
        effort: 'low',
        changes: {
          trendAdoption: 'active',
          contentMix: 'diversified',
        },
      },
      {
        id: 'increase_engagement_activities',
        type: 'engagement_boost',
        title: 'Boost Engagement Activities',
        description: 'Actively engage with your audience and other creators',
        impact: 'medium',
        effort: 'medium',
        changes: {
          engagementStrategy: 'proactive',
          communityBuilding: 'prioritized',
        },
      },
    ];
  }
  
  private getOpportunityRecommendations(analysis: ProgressAnalysis, roadmap: any): RecommendationItem[] {
    return [
      {
        id: 'scale_successful_content',
        type: 'content_scaling',
        title: 'Scale Your Successful Content',
        description: 'Create more content similar to your best-performing posts',
        impact: 'high',
        effort: 'low',
        changes: {
          contentStrategy: 'success_focused',
          scalingApproach: 'data_driven',
        },
      },
      {
        id: 'expand_to_new_platforms',
        type: 'platform_expansion',
        title: 'Expand to New Platforms',
        description: 'Leverage your success by expanding to additional social media platforms',
        impact: 'high',
        effort: 'high',
        changes: {
          platformStrategy: 'multi_platform',
          contentRepurposing: 'enabled',
        },
      },
    ];
  }
  
  private suggestAlternativeFormat(currentFormat: string): string {
    const alternatives = {
      'visual_metaphor': 'two_characters',
      'two_characters': 'untold_stories',
      'untold_stories': '30_day_challenge',
      '30_day_challenge': 'visual_metaphor',
    };
    
    return alternatives[currentFormat as keyof typeof alternatives] || 'visual_metaphor';
  }
  
  private generateReasoning(analysis: ProgressAnalysis, recommendations: RecommendationItem[]): string {
    let reasoning = '';
    
    if (!analysis.isOnTrack) {
      reasoning += `You're currently ${Math.abs(analysis.progressPercentage - 100).toFixed(1)}% behind your target progress. `;
    }
    
    if (analysis.significantDecline) {
      reasoning += 'Your metrics have shown a significant decline recently, indicating that your current strategy may need adjustment. ';
    }
    
    if (analysis.stagnation) {
      reasoning += 'Your growth has stagnated, suggesting it\'s time to try new approaches to re-engage your audience. ';
    }
    
    reasoning += `Based on this analysis, I recommend focusing on ${recommendations.length} key areas to get back on track.`;
    
    return reasoning;
  }
}
```

## Strategy Adjustment Artifact
Create the strategy adjustment artifact component:

```typescript
interface StrategyAdjustmentArtifact {
  type: 'strategy_adjustment';
  data: {
    title: string;
    currentProgress: {
      metric: string;
      current: number;
      target: number;
      projected: number;
    };
    issue: string;
    reasoning: string;
    recommendations: {
      id: string;
      type: string;
      title: string;
      description: string;
      impact: 'high' | 'medium' | 'low';
      effort: 'high' | 'medium' | 'low';
      changes: any;
    }[];
  };
}

export function StrategyAdjustmentArtifact({ artifact, onInteraction }: ArtifactProps) {
  const { data } = artifact;
  const [selectedRecommendations, setSelectedRecommendations] = useState<string[]>([]);
  
  const impactColors = {
    high: 'badge-error',
    medium: 'badge-warning',
    low: 'badge-info',
  };
  
  const effortColors = {
    high: 'badge-error',
    medium: 'badge-warning',
    low: 'badge-success',
  };
  
  const handleRecommendationToggle = (recId: string) => {
    setSelectedRecommendations(prev => 
      prev.includes(recId) 
        ? prev.filter(id => id !== recId)
        : [...prev, recId]
    );
  };
  
  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <h2 className="card-title text-warning">⚠️ {data.title}</h2>
        
        {/* Current Progress */}
        <div className="bg-base-200 p-4 rounded-lg mb-4">
          <h3 className="font-semibold mb-3">📊 Current Progress</h3>
          <div className="stats stats-horizontal shadow">
            <div className="stat">
              <div className="stat-title">Current</div>
              <div className="stat-value text-sm">{data.currentProgress.current.toLocaleString()}</div>
              <div className="stat-desc">{data.currentProgress.metric}</div>
            </div>
            <div className="stat">
              <div className="stat-title">Target</div>
              <div className="stat-value text-sm">{data.currentProgress.target.toLocaleString()}</div>
              <div className="stat-desc">Goal</div>
            </div>
            <div className="stat">
              <div className="stat-title">Projected</div>
              <div className="stat-value text-sm text-warning">{data.currentProgress.projected.toLocaleString()}</div>
              <div className="stat-desc">At current rate</div>
            </div>
          </div>
        </div>
        
        {/* Issue Description */}
        <div className="alert alert-warning mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div>
            <h3 className="font-bold">What's Happening</h3>
            <div className="text-sm">{data.issue}</div>
          </div>
        </div>
        
        {/* Reasoning */}
        <div className="bg-info/10 p-4 rounded-lg mb-4">
          <h4 className="font-semibold text-info mb-2">🤔 Why These Changes?</h4>
          <p className="text-sm">{data.reasoning}</p>
        </div>
        
        {/* Recommendations */}
        <div className="mb-4">
          <h3 className="font-semibold mb-3">💡 Recommended Adjustments:</h3>
          <div className="space-y-3">
            {data.recommendations.map(rec => {
              const isSelected = selectedRecommendations.includes(rec.id);
              
              return (
                <div 
                  key={rec.id}
                  className={`card cursor-pointer transition-all border-2 ${
                    isSelected 
                      ? 'bg-primary text-primary-content border-primary' 
                      : 'bg-base-200 hover:bg-base-300 border-transparent hover:border-primary/30'
                  }`}
                  onClick={() => handleRecommendationToggle(rec.id)}
                >
                  <div className="card-body p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold">{rec.title}</h4>
                          {isSelected && <span className="text-xl">✓</span>}
                        </div>
                        <p className="text-sm opacity-80 mb-2">{rec.description}</p>
                      </div>
                      <div className="flex flex-col gap-1 ml-4">
                        <span className={`badge badge-sm ${impactColors[rec.impact]}`}>
                          {rec.impact} impact
                        </span>
                        <span className={`badge badge-sm ${effortColors[rec.effort]}`}>
                          {rec.effort} effort
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        
        {/* Selection Summary */}
        {selectedRecommendations.length > 0 && (
          <div className="bg-primary/10 p-3 rounded-lg mb-4">
            <p className="text-sm">
              <strong>{selectedRecommendations.length}</strong> adjustment{selectedRecommendations.length !== 1 ? 's' : ''} selected
            </p>
          </div>
        )}
        
        <div className="card-actions justify-between">
          <button 
            className="btn btn-outline"
            onClick={() => onInteraction(artifact.id, 'reject_adjustments', {})}
          >
            Keep Current Strategy
          </button>
          <button 
            className="btn btn-warning"
            onClick={() => onInteraction(artifact.id, 'accept_adjustments', { 
              selectedRecommendations 
            })}
            disabled={selectedRecommendations.length === 0}
          >
            Apply Selected Changes ({selectedRecommendations.length})
          </button>
        </div>
      </div>
    </div>
  );
}
```

## API Routes for Mentor System
Create API routes for the mentor system:

```typescript
// Trigger progress analysis
app.post('/api/mentor/analyze-progress', async (req, res) => {
  const { roadmapId } = req.body;
  const { userId } = auth();
  
  try {
    const mentorService = new AIMentorService();
    const analysis = await mentorService.analyzeProgress(userId, roadmapId);
    
    res.json(analysis);
  } catch (error) {
    console.error('Error analyzing progress:', error);
    res.status(500).json({ error: 'Failed to analyze progress' });
  }
});

// Create progress check-in
app.post('/api/mentor/checkin', async (req, res) => {
  const { roadmapId, triggerReason } = req.body;
  const { userId } = auth();
  
  try {
    const mentorService = new AIMentorService();
    const checkin = await mentorService.createProgressCheckin(
      userId, 
      roadmapId, 
      triggerReason
    );
    
    res.json(checkin);
  } catch (error) {
    console.error('Error creating check-in:', error);
    res.status(500).json({ error: 'Failed to create check-in' });
  }
});

// Apply strategy adjustments
app.post('/api/mentor/apply-adjustments', async (req, res) => {
  const { roadmapId, recommendationId, selectedRecommendations } = req.body;
  const { userId } = auth();
  
  try {
    const mentorService = new AIMentorService();
    await mentorService.applyStrategyAdjustments(
      userId,
      roadmapId,
      recommendationId,
      selectedRecommendations
    );
    
    res.json({ success: true });
  } catch (error) {
    console.error('Error applying adjustments:', error);
    res.status(500).json({ error: 'Failed to apply adjustments' });
  }
});
```

## Additional Requirements
- Implement automated progress monitoring
- Create progress snapshot scheduling
- Add mentor notification system
- Implement strategy effectiveness tracking
- Create mentor performance analytics
- Add user feedback collection for recommendations
- Implement mentor learning and improvement
- Create mentor conversation templates
- Add mentor escalation procedures
- Implement mentor success metrics tracking

