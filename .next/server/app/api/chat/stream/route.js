const CHUNK_PUBLIC_PATH = "server/app/api/chat/stream/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_6aaad0cc._.js");
runtime.loadChunk("server/chunks/node_modules_next_4e2037bb._.js");
runtime.loadChunk("server/chunks/node_modules_@clerk_backend_dist_50c0fd3b._.js");
runtime.loadChunk("server/chunks/node_modules_drizzle-orm_a24f12f3._.js");
runtime.loadChunk("server/chunks/node_modules_zod_v3_a726d9e4._.js");
runtime.loadChunk("server/chunks/node_modules_b31e0181._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__bd8972d4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/chat/stream/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/chat/stream/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/chat/stream/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
