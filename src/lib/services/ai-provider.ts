// AI Provider types and configurations for Influtify
export type AIProvider = 'deepseek' | 'anthropic' | 'openai' | 'gemini';

// Provider Configuration Types
export interface ProviderConfig {
  endpoint: string;
  model: string;
  headers: (apiKey: string) => Record<string, string>;
  formatRequest: (prompt: string, messages: any[], systemMessage?: string) => any;
  streamProcessor: (response: Response, controller: ReadableStreamDefaultController) => Promise<void>;
}

// Helper function to remove Markdown code block delimiters from streams
function sanitizeStreamText(text: string): string {
  // Remove opening Markdown delimiters at the beginning of text
  if (text.startsWith('```') || text.startsWith('```html')) {
    return text.replace(/^```(?:html|markdown|text)?\n?/, '');
  }
  
  // Remove trailing Markdown delimiters at the end of text
  if (text.endsWith('```')) {
    return text.replace(/```$/, '');
  }
  
  return text;
}

// OpenAI Configuration
export const openaiConfig: ProviderConfig = {
  endpoint: 'https://api.openai.com/v1/chat/completions',
  model: 'gpt-4o-mini',
  headers: (apiKey) => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
  }),
  formatRequest: (prompt, messages = [], systemMessage = '') => ({
    model: 'gpt-4o-mini',
    messages: [
      {
        role: "system",
        content: systemMessage
      },
      {
        role: "user",
        content: prompt,
      },
      ...messages
    ],
    stream: true,
    temperature: 0.1,
  }),
  streamProcessor: async (response, controller) => {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("Response body reader not available");
    }

    let buffer = "";
    const encoder = new TextEncoder();
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = new TextDecoder().decode(value);
      buffer += chunk;
      
      let lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          const data = line.slice(6);
          if (data === "[DONE]") continue;
          try {
            const json = JSON.parse(data);
            const content = json.choices[0]?.delta?.content || "";
            if (content) {
              const sanitizedContent = sanitizeStreamText(content);
              controller.enqueue(encoder.encode(sanitizedContent));
            }
          } catch (e) {
            console.error("Error parsing OpenAI stream:", e);
          }
        }
      }
    }
    
    if (buffer && buffer.startsWith("data: ") && buffer !== "data: [DONE]") {
      try {
        const data = buffer.slice(6);
        const json = JSON.parse(data);
        const content = json.choices[0]?.delta?.content || "";
        if (content) {
          const sanitizedContent = sanitizeStreamText(content);
          controller.enqueue(encoder.encode(sanitizedContent));
        }
      } catch (e) {
        console.error("Error parsing final buffer in OpenAI stream:", e);
      }
    }
  }
};

// Anthropic Configuration
export const anthropicConfig: ProviderConfig = {
  endpoint: 'https://api.anthropic.com/v1/messages',
  model: 'claude-3-haiku-20240307',
  headers: (apiKey) => ({
    'Content-Type': 'application/json',
    'x-api-key': apiKey,
    'anthropic-version': '2023-06-01',
  }),
  formatRequest: (prompt, messages = [], systemMessage = '') => ({
    model: 'claude-3-haiku-20240307',
    system: systemMessage,
    messages: [
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: 0.1,
    stream: true
  }),
  streamProcessor: async (response, controller) => {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("Response body reader not available");
    }

    let buffer = "";
    const encoder = new TextEncoder();
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = new TextDecoder().decode(value);
      buffer += chunk;
      
      let lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (line.startsWith("event: content_block_delta")) {
          const dataIndex = lines.indexOf(line) + 1;
          if (dataIndex < lines.length && lines[dataIndex].startsWith("data: ")) {
            try {
              const data = JSON.parse(lines[dataIndex].slice(6));
              if (data.delta && data.delta.text) {
                const sanitizedText = sanitizeStreamText(data.delta.text);
                controller.enqueue(encoder.encode(sanitizedText));
              }
            } catch (e) {
              console.error("Error parsing Anthropic stream:", e);
            }
          }
        } else if (line.startsWith("data: ")) {
          try {
            const data = JSON.parse(line.slice(6));
            if (data.type === "content_block_delta" && data.delta && data.delta.text) {
              const sanitizedText = sanitizeStreamText(data.delta.text);
              controller.enqueue(encoder.encode(sanitizedText));
            }
          } catch (e) {
            // Ignore parsing errors for lines that aren't content blocks
          }
        }
      }
    }
  }
};

// DeepSeek Configuration
export const deepseekConfig: ProviderConfig = {
  endpoint: 'https://api.deepseek.com/v1/chat/completions',
  model: 'deepseek-chat',
  headers: (apiKey) => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
  }),
  formatRequest: (prompt, messages = [], systemMessage = '') => ({
    model: 'deepseek-chat',
    messages: [
      {
        role: "system",
        content: systemMessage
      },
      {
        role: "user",
        content: prompt,
      },
      ...messages
    ],
    temperature: 0.1,
    stream: true,
  }),
  streamProcessor: async (response, controller) => {
    // DeepSeek uses the same response format as OpenAI
    await openaiConfig.streamProcessor(response, controller);
  }
};

// Gemini Configuration
export const geminiConfig: ProviderConfig = {
  endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:streamGenerateContent',
  model: 'gemini-2.0-flash-lite',
  headers: (apiKey) => ({
    'Content-Type': 'application/json',
  }),
  formatRequest: (prompt, messages = [], systemMessage = '') => ({
    contents: [
      {
        parts: [
          { text: systemMessage },
          { text: prompt }
        ]
      }
    ],
    generationConfig: {
      temperature: 0.1,
    }
  }),
  streamProcessor: async (response, controller) => {
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("Response body reader not available for Gemini stream");
    }

    let buffer = "";
    const decoder = new TextDecoder();
    const encoder = new TextEncoder();

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log("[Gemini Stream] Stream finished.");
          break;
        }

        buffer += decoder.decode(value, { stream: true });

        let parseStartIndex = 0;
        while (parseStartIndex < buffer.length) {
          const objStartIndex = buffer.indexOf('{', parseStartIndex);
          if (objStartIndex === -1) {
            break;
          }

          let braceCount = 1;
          let objEndIndex = objStartIndex + 1;
          while (objEndIndex < buffer.length && braceCount > 0) {
            if (buffer[objEndIndex] === '{') {
              braceCount++;
            } else if (buffer[objEndIndex] === '}') {
              braceCount--;
            }
            objEndIndex++;
          }

          if (braceCount === 0) {
            const jsonString = buffer.substring(objStartIndex, objEndIndex);

            try {
              const data = JSON.parse(jsonString);

              if (data.candidates && data.candidates[0]?.content?.parts) {
                for (const part of data.candidates[0].content.parts) {
                  if (part.text) {
                    const sanitizedText = sanitizeStreamText(part.text);
                    controller.enqueue(encoder.encode(sanitizedText));
                  }
                }
              }

              buffer = buffer.substring(objEndIndex);
              parseStartIndex = 0;
            } catch (e) {
              parseStartIndex = objStartIndex + 1;
            }
          } else {
            break;
          }
        }
      }
    } catch (error) {
      console.error("Error reading Gemini stream:", error);
      controller.error(error);
    } finally {
      const finalChunk = decoder.decode();
      reader.releaseLock();
      console.log("[Gemini Stream] Processor finished.");
    }
  }
};

// Provider Configuration Map
export const PROVIDER_CONFIG = {
  deepseek: deepseekConfig,
  anthropic: anthropicConfig,
  openai: openaiConfig,
  gemini: geminiConfig
};

// Get current provider from environment
export function getCurrentProvider(): AIProvider {
  const provider = process.env.AI_PROVIDER as AIProvider;
  if (!provider || !PROVIDER_CONFIG[provider]) {
    // Default to gemini if no provider is specified or invalid
    return 'gemini';
  }
  return provider;
}

// Get API key for current provider
export function getApiKey(provider: AIProvider): string {
  const keys = {
    deepseek: process.env.DEEPSEEK_API_KEY,
    anthropic: process.env.ANTHROPIC_API_KEY,
    openai: process.env.OPENAI_API_KEY,
    gemini: process.env.GEMINI_API_KEY
  };

  const apiKey = keys[provider];
  if (!apiKey) {
    throw new Error(`API key not configured for provider: ${provider}`);
  }

  return apiKey;
}

// Helper function for non-streaming requests
export async function callAIForCompletion(prompt: string, systemMessage = '', temperature = 0.1): Promise<string> {
  const provider = getCurrentProvider();
  const config = PROVIDER_CONFIG[provider];
  const apiKey = getApiKey(provider);

  console.log(`Using AI provider for completion: ${provider}`);

  // Create endpoint URL (special handling for Gemini)
  let endpoint = config.endpoint;
  let requestBody;

  if (provider === 'gemini') {
    // Use a different model for non-streaming Gemini calls
    const baseUrl = endpoint.split('/models/')[0] + '/models/';
    const nonStreamingModel = 'gemini-2.0-flash-lite';
    endpoint = `${baseUrl}${nonStreamingModel}:generateContent?key=${apiKey}`;

    console.log(`[callAIForCompletion] Using ${nonStreamingModel} for non-streaming Gemini call`);

    requestBody = {
      ...config.formatRequest(prompt, [], systemMessage),
      model: nonStreamingModel
    };
  } else {
    endpoint = endpoint.replace('stream', '');
    requestBody = config.formatRequest(prompt, [], systemMessage);
  }

  // For non-streaming requests, set stream to false
  if (requestBody.stream !== undefined) {
    requestBody.stream = false;
  }

  if (requestBody.temperature !== undefined) {
    requestBody.temperature = temperature;
  }

  try {
    const response = await fetch(endpoint, {
      method: "POST",
      headers: config.headers(apiKey),
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`${provider} API error: ${response.status} ${response.statusText} ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();

    // Extract content based on provider
    let content = '';
    if (provider === 'gemini') {
      content = data.candidates?.[0]?.content?.parts?.[0]?.text || '';
    } else if (provider === 'anthropic') {
      content = data.content?.[0]?.text || '';
    } else {
      // OpenAI and DeepSeek format
      content = data.choices?.[0]?.message?.content || '';
    }

    // Clean up any markdown code blocks
    content = content.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();

    return content;
  } catch (error) {
    console.error(`Error calling ${provider} API for completion:`, error);
    throw error;
  }
}

/**
 * Function to call AI and parse the response as JSON
 * This is a wrapper around callAIForCompletion that ensures the response is valid JSON
 */
export async function callAIForJSON<T = any>(prompt: string, systemMessage = '', temperature = 0.1): Promise<T> {
  const result = await callAIForCompletion(prompt, systemMessage, temperature);

  // Clean up any markdown code fences from the response
  let cleanResult = result;
  if (cleanResult.includes('```')) {
    cleanResult = cleanResult.replace(/```json\n?|```\n?|```$/gm, '');
  }

  try {
    return JSON.parse(cleanResult) as T;
  } catch (error) {
    console.error('Failed to parse AI response as JSON:', error);
    throw new Error(`Failed to parse AI response as JSON: ${error}`);
  }
}

// Generic function to call AI with streaming
export async function callAIWithStreaming(prompt: string, systemMessage = ''): Promise<ReadableStream> {
  const provider = getCurrentProvider();
  const config = PROVIDER_CONFIG[provider];
  const apiKey = getApiKey(provider);

  console.log(`Using AI provider for streaming: ${provider}`);

  // Create the endpoint URL (special handling for Gemini)
  let endpoint = config.endpoint;
  if (provider === 'gemini') {
    // Add API key to the URL for Gemini
    endpoint = `${endpoint}?key=${apiKey}`;
  }

  // Create request body based on provider
  const requestBody = config.formatRequest(prompt, [], systemMessage);

  // Create stream
  return new ReadableStream({
    async start(controller) {
      try {
        console.log(`Calling ${provider} API at ${config.endpoint}`);

        const res = await fetch(endpoint, {
          method: "POST",
          headers: config.headers(apiKey),
          body: JSON.stringify(requestBody),
        });

        if (!res.ok) {
          const errorData = await res.json().catch(() => ({}));
          const errorMessage = `${provider} API error: ${res.status} ${res.statusText} ${JSON.stringify(errorData)}`;
          console.error(errorMessage);
          controller.error(new Error(errorMessage));
          return;
        }

        // Process the stream using provider-specific logic
        await config.streamProcessor(res, controller);

      } catch (e) {
        console.error(`Error in ${provider} stream:`, e);
        controller.error(e);
      } finally {
        controller.close();
      }
    },
  });
}
