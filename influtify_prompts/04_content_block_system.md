# Influtify Content Block System (Chat-Integrated) Prompt

## Feature Overview
Integrate the content block system with the chat interface, allowing users to access and complete content blocks through conversational interactions and artifacts. Users can ask for specific guidance, view block details, and mark completion through the chat. This system provides structured, actionable content creation guidance based on <PERSON>'s viral formats.

## Technical Requirements
- Implement using NextJS with the sliced-feature architecture
- Use Zod for data validation
- Use Drizzle ORM for database operations
- Implement UI with DaisyUI components
- Support multilanguage with next-intl
- Integrate with the AI chat system
- Create reusable content block components

## Database Schema
Extend the Drizzle schema with content block tables:

```typescript
// Content Blocks Schema
export const contentBlocks = pgTable('content_blocks', {
  id: serial('id').primaryKey(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  
  // Block Information
  title: text('title').notNull(),
  description: text('description').notNull(),
  category: text('category').notNull(), // setup, content_creation, engagement, optimization
  viralFormat: text('viral_format'), // visual_metaphor, two_characters, untold_stories, 30_day_challenge
  
  // Block Content
  estimatedTime: text('estimated_time').notNull(),
  difficulty: text('difficulty').notNull(), // beginner, intermediate, advanced
  
  // Block Data
  tasks: json('tasks').$type<{
    id: string;
    title: string;
    description: string;
    estimatedMinutes: number;
    resources?: {
      title: string;
      type: string;
      url?: string;
      content?: string;
    }[];
  }[]>().notNull(),
  
  resources: json('resources').$type<{
    title: string;
    type: string;
    url?: string;
    content?: string;
  }[]>(),
  
  examples: json('examples').$type<string[]>(),
  tips: json('tips').$type<string[]>(),
  
  // Prerequisites
  dependencies: json('dependencies').$type<string[]>(),
  
  // Metadata
  isActive: boolean('is_active').default(true),
  sortOrder: integer('sort_order').default(0),
});

// User Block Progress Schema
export const userBlockProgress = pgTable('user_block_progress', {
  id: serial('id').primaryKey(),
  userId: text('user_id').notNull().references(() => auth.users.id),
  blockId: integer('block_id').references(() => contentBlocks.id).notNull(),
  roadmapId: integer('roadmap_id').references(() => roadmaps.id),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  
  // Progress Information
  status: text('status').notNull().default('not_started'), // not_started, in_progress, completed
  completedTasks: json('completed_tasks').$type<string[]>().default([]),
  startedAt: timestamp('started_at'),
  completedAt: timestamp('completed_at'),
  
  // User Data
  userNotes: text('user_notes'),
  timeSpent: integer('time_spent_minutes').default(0),
  
  // Content Created
  createdContent: json('created_content').$type<{
    title?: string;
    description?: string;
    script?: string;
    hashtags?: string[];
    mediaUrls?: string[];
  }>(),
});

// Block Templates Schema (for different viral formats)
export const blockTemplates = pgTable('block_templates', {
  id: serial('id').primaryKey(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
  // Template Information
  name: text('name').notNull(),
  viralFormat: text('viral_format').notNull(),
  platform: text('platform'), // instagram, tiktok, youtube, etc.
  
  // Template Content
  structure: json('structure').$type<string[]>().notNull(),
  tips: json('tips').$type<string[]>().notNull(),
  examples: json('examples').$type<string[]>().notNull(),
  hooks: json('hooks').$type<string[]>().notNull(),
  
  // Customization
  variables: json('variables').$type<{
    name: string;
    type: string;
    description: string;
    defaultValue?: any;
  }[]>(),
});
```

## Content Block Service
Implement the core content block service:

```typescript
export class ContentBlockService {
  // Get blocks for a specific roadmap
  async getBlocksForRoadmap(roadmapId: number): Promise<ContentBlock[]> {
    const roadmap = await db.query.roadmaps.findFirst({
      where: eq(roadmaps.id, roadmapId),
      with: {
        blocks: {
          orderBy: asc(contentBlocks.sortOrder),
        },
      },
    });
    
    return roadmap?.blocks || [];
  }
  
  // Get user progress for blocks
  async getUserBlockProgress(userId: string, roadmapId: number): Promise<UserBlockProgress[]> {
    return await db.query.userBlockProgress.findMany({
      where: and(
        eq(userBlockProgress.userId, userId),
        eq(userBlockProgress.roadmapId, roadmapId)
      ),
      with: {
        block: true,
      },
    });
  }
  
  // Start a block
  async startBlock(userId: string, blockId: number, roadmapId: number): Promise<UserBlockProgress> {
    // Check if progress already exists
    const existingProgress = await db.query.userBlockProgress.findFirst({
      where: and(
        eq(userBlockProgress.userId, userId),
        eq(userBlockProgress.blockId, blockId)
      ),
    });
    
    if (existingProgress) {
      // Update to in_progress if not started
      if (existingProgress.status === 'not_started') {
        return await db.update(userBlockProgress)
          .set({
            status: 'in_progress',
            startedAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(userBlockProgress.id, existingProgress.id))
          .returning()
          .then(rows => rows[0]);
      }
      return existingProgress;
    }
    
    // Create new progress
    return await db.insert(userBlockProgress).values({
      userId,
      blockId,
      roadmapId,
      status: 'in_progress',
      startedAt: new Date(),
      completedTasks: [],
    }).returning().then(rows => rows[0]);
  }
  
  // Complete a task within a block
  async completeTask(
    userId: string, 
    blockId: number, 
    taskId: string
  ): Promise<UserBlockProgress> {
    const progress = await db.query.userBlockProgress.findFirst({
      where: and(
        eq(userBlockProgress.userId, userId),
        eq(userBlockProgress.blockId, blockId)
      ),
    });
    
    if (!progress) {
      throw new Error('Block progress not found');
    }
    
    const completedTasks = progress.completedTasks || [];
    if (!completedTasks.includes(taskId)) {
      completedTasks.push(taskId);
    }
    
    // Check if all tasks are completed
    const block = await db.query.contentBlocks.findFirst({
      where: eq(contentBlocks.id, blockId),
    });
    
    const allTasksCompleted = block?.tasks.every(task => 
      completedTasks.includes(task.id)
    );
    
    return await db.update(userBlockProgress)
      .set({
        completedTasks,
        status: allTasksCompleted ? 'completed' : 'in_progress',
        completedAt: allTasksCompleted ? new Date() : null,
        updatedAt: new Date(),
      })
      .where(eq(userBlockProgress.id, progress.id))
      .returning()
      .then(rows => rows[0]);
  }
  
  // Save user content for a block
  async saveBlockContent(
    userId: string,
    blockId: number,
    content: any
  ): Promise<UserBlockProgress> {
    const progress = await db.query.userBlockProgress.findFirst({
      where: and(
        eq(userBlockProgress.userId, userId),
        eq(userBlockProgress.blockId, blockId)
      ),
    });
    
    if (!progress) {
      throw new Error('Block progress not found');
    }
    
    return await db.update(userBlockProgress)
      .set({
        createdContent: content,
        updatedAt: new Date(),
      })
      .where(eq(userBlockProgress.id, progress.id))
      .returning()
      .then(rows => rows[0]);
  }
  
  // Get content templates for viral formats
  async getContentTemplate(viralFormat: string, platform?: string): Promise<BlockTemplate | null> {
    return await db.query.blockTemplates.findFirst({
      where: and(
        eq(blockTemplates.viralFormat, viralFormat),
        platform ? eq(blockTemplates.platform, platform) : undefined
      ),
    });
  }
}
```

## Chat Integration for Content Blocks
Integrate content blocks with the AI chat system:

```typescript
// AI function for recommending content blocks
const recommendContentBlockFunction = {
  name: "recommend_content_block",
  description: "Recommend a specific content block to the user",
  parameters: {
    type: "object",
    properties: {
      blockId: {
        type: "string",
        description: "ID of the content block to recommend"
      },
      reason: {
        type: "string",
        description: "Reason for recommending this block"
      },
      urgency: {
        type: "string",
        enum: ["low", "medium", "high"],
        description: "Urgency level for completing this block"
      }
    }
  }
};

// AI function for creating block detail artifact
const createBlockDetailFunction = {
  name: "create_block_detail",
  description: "Create a detailed view of a content block",
  parameters: {
    type: "object",
    properties: {
      blockId: {
        type: "string",
        description: "ID of the content block"
      },
      showProgress: {
        type: "boolean",
        description: "Whether to show user progress"
      }
    }
  }
};

// Enhanced AI prompt for content block integration
const contentBlockPrompt = `
  You can help users with content blocks by:
  1. Recommending specific blocks based on their goals and progress
  2. Explaining block content and requirements
  3. Providing guidance on completing tasks
  4. Helping with content creation using viral formats
  5. Suggesting next steps after block completion
  
  AVAILABLE CONTENT BLOCKS:
  - Channel Setup (setup category)
  - Content Strategy Planning (setup category)
  - Visual Metaphor Content Creation (content_creation category)
  - Two Characters Content Creation (content_creation category)
  - Audience Engagement Tactics (engagement category)
  - Performance Optimization (optimization category)
  
  VIRAL FORMATS:
  1. Visual Metaphor Format - Using visual comparisons to explain complex ideas
  2. Two Characters, One Lightbulb Format - One character teaches another
  3. Untold Stories Format - Behind-the-scenes narratives
  4. 30-Day Challenge Format - Documenting a journey
`;
```

## Block Detail Artifact Component
Create the block detail artifact component:

```typescript
interface BlockDetailArtifact {
  type: 'block_detail';
  data: {
    blockId: string;
    title: string;
    description: string;
    category: string;
    viralFormat?: string;
    estimatedTime: string;
    difficulty: string;
    tasks: {
      id: string;
      title: string;
      description: string;
      isCompleted: boolean;
      estimatedMinutes: number;
      resources?: {
        title: string;
        type: string;
        url?: string;
        content?: string;
      }[];
    }[];
    resources: {
      title: string;
      type: string;
      url?: string;
      content?: string;
    }[];
    examples: string[];
    tips: string[];
    userProgress?: {
      status: string;
      completedTasks: string[];
      timeSpent: number;
    };
  };
}

export function BlockDetailArtifact({ artifact, onInteraction }: ArtifactProps) {
  const { data } = artifact;
  const completedTasks = data.userProgress?.completedTasks || [];
  const progressPercentage = (completedTasks.length / data.tasks.length) * 100;
  
  const categoryColors = {
    setup: 'badge-info',
    content_creation: 'badge-primary',
    engagement: 'badge-secondary',
    optimization: 'badge-accent',
  };
  
  const difficultyColors = {
    beginner: 'badge-success',
    intermediate: 'badge-warning',
    advanced: 'badge-error',
  };
  
  return (
    <div className="card bg-base-100 shadow-xl">
      <div className="card-body">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h2 className="card-title text-primary">{data.title}</h2>
            <p className="text-base-content/70 mt-1">{data.description}</p>
          </div>
          <div className="flex flex-col gap-2">
            <span className={`badge ${categoryColors[data.category as keyof typeof categoryColors]}`}>
              {data.category.replace('_', ' ')}
            </span>
            <span className={`badge ${difficultyColors[data.difficulty as keyof typeof difficultyColors]}`}>
              {data.difficulty}
            </span>
          </div>
        </div>
        
        {/* Block Info */}
        <div className="flex flex-wrap gap-2 mb-4">
          <span className="badge badge-outline">{data.estimatedTime}</span>
          <span className="badge badge-outline">{data.tasks.length} tasks</span>
          {data.viralFormat && (
            <span className="badge badge-outline">{data.viralFormat}</span>
          )}
        </div>
        
        {/* Progress */}
        {data.userProgress && (
          <div className="mb-4">
            <div className="flex justify-between text-sm mb-2">
              <span>Progress</span>
              <span>{completedTasks.length}/{data.tasks.length} tasks</span>
            </div>
            <progress 
              className="progress progress-primary w-full" 
              value={progressPercentage} 
              max="100"
            ></progress>
            <div className="flex justify-between text-xs text-base-content/60 mt-1">
              <span>Status: {data.userProgress.status}</span>
              <span>Time spent: {data.userProgress.timeSpent}min</span>
            </div>
          </div>
        )}
        
        {/* Tasks */}
        <div className="mb-4">
          <h3 className="font-semibold mb-3">📋 Tasks:</h3>
          <div className="space-y-3">
            {data.tasks.map(task => {
              const isCompleted = completedTasks.includes(task.id);
              
              return (
                <div key={task.id} className="card bg-base-200">
                  <div className="card-body p-4">
                    <div className="flex items-start gap-3">
                      <input 
                        type="checkbox" 
                        className="checkbox checkbox-primary mt-1" 
                        checked={isCompleted}
                        onChange={() => onInteraction(artifact.id, 'toggle_task', { taskId: task.id })}
                      />
                      <div className="flex-1">
                        <h4 className={`font-medium ${isCompleted ? 'line-through opacity-60' : ''}`}>
                          {task.title}
                        </h4>
                        <p className="text-sm opacity-70 mt-1">{task.description}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs opacity-50">⏱️ {task.estimatedMinutes} min</span>
                          {task.resources && task.resources.length > 0 && (
                            <span className="text-xs opacity-50">📚 {task.resources.length} resources</span>
                          )}
                        </div>
                        
                        {/* Task Resources */}
                        {task.resources && task.resources.length > 0 && (
                          <div className="mt-2 space-y-1">
                            {task.resources.map((resource, index) => (
                              <div key={index} className="flex items-center gap-2">
                                <span className="badge badge-xs">{resource.type}</span>
                                <span className="text-xs">{resource.title}</span>
                                {resource.url && (
                                  <a 
                                    href={resource.url} 
                                    target="_blank" 
                                    className="btn btn-xs btn-outline"
                                  >
                                    View
                                  </a>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        
        {/* Resources */}
        {data.resources.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2">📚 Additional Resources:</h3>
            <div className="grid grid-cols-1 gap-2">
              {data.resources.map((resource, index) => (
                <div key={index} className="flex items-center justify-between bg-base-200 p-2 rounded">
                  <div className="flex items-center gap-2">
                    <span className="badge badge-sm">{resource.type}</span>
                    <span className="text-sm">{resource.title}</span>
                  </div>
                  {resource.url && (
                    <a href={resource.url} target="_blank" className="btn btn-xs btn-outline">
                      Open
                    </a>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
        
        {/* Tips */}
        {data.tips.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2">💡 Pro Tips:</h3>
            <ul className="space-y-1">
              {data.tips.map((tip, index) => (
                <li key={index} className="text-sm flex items-start gap-2">
                  <span className="text-primary">•</span>
                  {tip}
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Examples */}
        {data.examples.length > 0 && (
          <div className="mb-4">
            <h3 className="font-semibold mb-2">🌟 Examples:</h3>
            <div className="space-y-2">
              {data.examples.map((example, index) => (
                <div key={index} className="bg-base-200 p-3 rounded text-sm">
                  {example}
                </div>
              ))}
            </div>
          </div>
        )}
        
        <div className="card-actions justify-between">
          <button 
            className="btn btn-outline btn-sm"
            onClick={() => onInteraction(artifact.id, 'get_help', {})}
          >
            💬 Get Help
          </button>
          
          {data.viralFormat && (
            <button 
              className="btn btn-secondary btn-sm"
              onClick={() => onInteraction(artifact.id, 'create_content', { viralFormat: data.viralFormat })}
            >
              🎨 Create Content
            </button>
          )}
          
          {progressPercentage === 100 ? (
            <button 
              className="btn btn-success btn-sm"
              onClick={() => onInteraction(artifact.id, 'complete_block', {})}
            >
              ✅ Mark Complete
            </button>
          ) : (
            <button 
              className="btn btn-primary btn-sm"
              onClick={() => onInteraction(artifact.id, 'continue_block', {})}
            >
              ▶️ Continue
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
```

## Block Recommendation System
Implement AI-powered block recommendations:

```typescript
export class BlockRecommendationService {
  async getRecommendedBlocks(
    userId: string,
    roadmapId: number,
    context?: string
  ): Promise<{
    block: ContentBlock;
    reason: string;
    urgency: 'low' | 'medium' | 'high';
  }[]> {
    // Get user progress
    const userProgress = await db.query.userBlockProgress.findMany({
      where: and(
        eq(userBlockProgress.userId, userId),
        eq(userBlockProgress.roadmapId, roadmapId)
      ),
      with: {
        block: true,
      },
    });
    
    // Get roadmap blocks
    const roadmapBlocks = await db.query.roadmaps.findFirst({
      where: eq(roadmaps.id, roadmapId),
      with: {
        blocks: {
          orderBy: asc(contentBlocks.sortOrder),
        },
      },
    });
    
    if (!roadmapBlocks) return [];
    
    const recommendations: any[] = [];
    
    // Find next logical block
    const completedBlocks = userProgress
      .filter(p => p.status === 'completed')
      .map(p => p.blockId);
    
    const inProgressBlocks = userProgress
      .filter(p => p.status === 'in_progress')
      .map(p => p.blockId);
    
    // Recommend continuing in-progress blocks
    for (const progress of userProgress) {
      if (progress.status === 'in_progress') {
        recommendations.push({
          block: progress.block,
          reason: `Continue working on "${progress.block.title}" - you're ${this.calculateProgress(progress)}% complete`,
          urgency: 'high' as const,
        });
      }
    }
    
    // Recommend next blocks based on dependencies
    for (const block of roadmapBlocks.blocks) {
      if (completedBlocks.includes(block.id) || inProgressBlocks.includes(block.id)) {
        continue;
      }
      
      // Check if dependencies are met
      const dependenciesMet = block.dependencies?.every(depId => 
        completedBlocks.includes(parseInt(depId))
      ) ?? true;
      
      if (dependenciesMet) {
        recommendations.push({
          block,
          reason: `Ready to start "${block.title}" - all prerequisites completed`,
          urgency: 'medium' as const,
        });
        break; // Only recommend one next block
      }
    }
    
    return recommendations.slice(0, 3); // Limit to 3 recommendations
  }
  
  private calculateProgress(progress: any): number {
    const completedTasks = progress.completedTasks?.length || 0;
    const totalTasks = progress.block.tasks?.length || 1;
    return Math.round((completedTasks / totalTasks) * 100);
  }
}
```

## API Routes for Content Blocks
Create API routes for content block operations:

```typescript
// Get block details
app.get('/api/blocks/:id', async (req, res) => {
  const { id } = req.params;
  const { userId } = auth();
  
  try {
    const block = await db.query.contentBlocks.findFirst({
      where: eq(contentBlocks.id, parseInt(id)),
    });
    
    if (!block) {
      return res.status(404).json({ error: 'Block not found' });
    }
    
    // Get user progress
    const progress = await db.query.userBlockProgress.findFirst({
      where: and(
        eq(userBlockProgress.userId, userId),
        eq(userBlockProgress.blockId, parseInt(id))
      ),
    });
    
    res.json({ block, progress });
  } catch (error) {
    console.error('Error fetching block:', error);
    res.status(500).json({ error: 'Failed to fetch block' });
  }
});

// Start a block
app.post('/api/blocks/:id/start', async (req, res) => {
  const { id } = req.params;
  const { roadmapId } = req.body;
  const { userId } = auth();
  
  try {
    const blockService = new ContentBlockService();
    const progress = await blockService.startBlock(userId, parseInt(id), roadmapId);
    
    res.json(progress);
  } catch (error) {
    console.error('Error starting block:', error);
    res.status(500).json({ error: 'Failed to start block' });
  }
});

// Complete a task
app.post('/api/blocks/:id/tasks/:taskId/complete', async (req, res) => {
  const { id, taskId } = req.params;
  const { userId } = auth();
  
  try {
    const blockService = new ContentBlockService();
    const progress = await blockService.completeTask(userId, parseInt(id), taskId);
    
    res.json(progress);
  } catch (error) {
    console.error('Error completing task:', error);
    res.status(500).json({ error: 'Failed to complete task' });
  }
});

// Save block content
app.post('/api/blocks/:id/content', async (req, res) => {
  const { id } = req.params;
  const { content } = req.body;
  const { userId } = auth();
  
  try {
    const blockService = new ContentBlockService();
    const progress = await blockService.saveBlockContent(userId, parseInt(id), content);
    
    res.json(progress);
  } catch (error) {
    console.error('Error saving content:', error);
    res.status(500).json({ error: 'Failed to save content' });
  }
});
```

## Multilanguage Support
Implement translations for content blocks:

```typescript
// Example translation keys
export const blockTranslations = {
  en: {
    "block.category.setup": "Setup",
    "block.category.content_creation": "Content Creation",
    "block.category.engagement": "Engagement",
    "block.category.optimization": "Optimization",
    "block.difficulty.beginner": "Beginner",
    "block.difficulty.intermediate": "Intermediate",
    "block.difficulty.advanced": "Advanced",
    "block.status.not_started": "Not Started",
    "block.status.in_progress": "In Progress",
    "block.status.completed": "Completed",
    // Additional translations...
  },
  es: {
    "block.category.setup": "Configuración",
    "block.category.content_creation": "Creación de Contenido",
    "block.category.engagement": "Participación",
    "block.category.optimization": "Optimización",
    "block.difficulty.beginner": "Principiante",
    "block.difficulty.intermediate": "Intermedio",
    "block.difficulty.advanced": "Avanzado",
    "block.status.not_started": "No Iniciado",
    "block.status.in_progress": "En Progreso",
    "block.status.completed": "Completado",
    // Additional translations...
  }
};
```

## Additional Requirements
- Implement block progress synchronization
- Create block completion celebrations
- Add block sharing and collaboration features
- Implement block customization options
- Create block performance analytics
- Add block feedback and rating system
- Implement block versioning and updates
- Create block export functionality
- Add block search and filtering
- Implement block recommendation engine improvements

