{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/lib/db/schema.ts"], "sourcesContent": ["import { pgTable, serial, text, timestamp, json, integer, boolean, date } from 'drizzle-orm/pg-core';\n\n// Users table (extends Clerk user data)\nexport const users = pgTable('users', {\n  id: text('id').primaryKey(), // Clerk user ID\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Profile Information\n  email: text('email').notNull(),\n  firstName: text('first_name'),\n  lastName: text('last_name'),\n  imageUrl: text('image_url'),\n  \n  // Social Media Goals\n  primaryGoal: text('primary_goal'), // follower_growth, engagement, monetization, brand_awareness\n  targetAudience: text('target_audience'),\n  currentFollowers: integer('current_followers').default(0),\n  targetFollowers: integer('target_followers'),\n  \n  // Platform Information\n  platforms: json('platforms').$type<{\n    platform: string;\n    username: string;\n    followers: number;\n    isActive: boolean;\n  }[]>().default([]),\n  \n  // Preferences\n  contentStyle: text('content_style'), // educational, entertaining, inspirational, promotional\n  postingFrequency: text('posting_frequency'), // daily, weekly, bi_weekly\n  timezone: text('timezone').default('UTC'),\n  language: text('language').default('en'),\n  \n  // Subscription\n  subscriptionStatus: text('subscription_status').default('free'), // free, pro, premium\n  subscriptionId: text('subscription_id'),\n  subscriptionEndsAt: timestamp('subscription_ends_at'),\n});\n\n// Conversations table\nexport const conversations = pgTable('conversations', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Conversation metadata\n  title: text('title'),\n  isActive: boolean('is_active').default(true),\n  \n  // Context and state\n  context: json('context').$type<{\n    currentGoal?: string;\n    roadmapId?: number;\n    lastArtifactId?: string;\n    userPreferences?: Record<string, any>;\n  }>().default({}),\n});\n\n// Messages table\nexport const messages = pgTable('messages', {\n  id: serial('id').primaryKey(),\n  conversationId: integer('conversation_id').references(() => conversations.id).notNull(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  \n  // Message content\n  role: text('role').notNull(), // user, assistant, system\n  content: text('content').notNull(),\n  \n  // AI function calling\n  functionCall: json('function_call').$type<{\n    name: string;\n    arguments: Record<string, any>;\n  }>(),\n  functionResult: json('function_result'),\n  \n  // Message metadata\n  metadata: json('metadata').$type<{\n    artifactId?: string;\n    tokens?: number;\n    model?: string;\n    processingTime?: number;\n  }>().default({}),\n});\n\n// Artifacts table\nexport const artifacts = pgTable('artifacts', {\n  id: text('id').primaryKey(), // UUID\n  conversationId: integer('conversation_id').references(() => conversations.id).notNull(),\n  userId: text('user_id').notNull().references(() => users.id),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Artifact information\n  type: text('type').notNull(), // goal_selector, roadmap_preview, progress_chart, etc.\n  title: text('title').notNull(),\n  description: text('description'),\n  \n  // Artifact data\n  data: json('data').notNull(),\n  \n  // State and interactions\n  isActive: boolean('is_active').default(true),\n  interactions: json('interactions').$type<{\n    timestamp: string;\n    action: string;\n    data: any;\n  }[]>().default([]),\n});\n\n// Roadmaps table\nexport const roadmaps = pgTable('roadmaps', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Roadmap information\n  title: text('title').notNull(),\n  description: text('description'),\n  goal: text('goal').notNull(), // follower_growth, engagement, etc.\n  \n  // Roadmap configuration\n  duration: integer('duration').notNull(), // days\n  difficulty: text('difficulty').notNull(), // beginner, intermediate, advanced\n  \n  // Progress tracking\n  status: text('status').default('active'), // active, completed, paused\n  progress: integer('progress').default(0), // percentage\n  \n  // Roadmap data\n  blocks: json('blocks').$type<{\n    id: string;\n    title: string;\n    description: string;\n    order: number;\n    estimatedTime: number;\n    dependencies: string[];\n  }[]>().notNull(),\n  \n  // Metadata\n  isCustom: boolean('is_custom').default(false),\n  templateId: text('template_id'),\n});\n\n// Content blocks table\nexport const contentBlocks = pgTable('content_blocks', {\n  id: serial('id').primaryKey(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  \n  // Block information\n  title: text('title').notNull(),\n  description: text('description').notNull(),\n  category: text('category').notNull(), // viral_format, strategy, analysis, etc.\n  \n  // Content structure\n  format: text('format').notNull(), // visual_metaphor, two_characters, untold_stories, challenge\n  difficulty: text('difficulty').notNull(), // beginner, intermediate, advanced\n  estimatedTime: integer('estimated_time').notNull(), // minutes\n  \n  // Block content\n  content: json('content').$type<{\n    sections: {\n      id: string;\n      title: string;\n      type: string; // text, checklist, template, example\n      content: string;\n      order: number;\n    }[];\n    examples: {\n      platform: string;\n      description: string;\n      content: string;\n    }[];\n    templates: {\n      name: string;\n      template: string;\n      variables: string[];\n    }[];\n  }>().notNull(),\n  \n  // Requirements and outcomes\n  prerequisites: json('prerequisites').$type<string[]>().default([]),\n  learningOutcomes: json('learning_outcomes').$type<string[]>().default([]),\n  \n  // Metadata\n  isActive: boolean('is_active').default(true),\n  sortOrder: integer('sort_order').default(0),\n  tags: json('tags').$type<string[]>().default([]),\n});\n\n// User block progress table\nexport const userBlockProgress = pgTable('user_block_progress', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  blockId: integer('block_id').references(() => contentBlocks.id).notNull(),\n  roadmapId: integer('roadmap_id').references(() => roadmaps.id),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n  \n  // Progress tracking\n  status: text('status').default('not_started'), // not_started, in_progress, completed\n  progress: integer('progress').default(0), // percentage\n  timeSpent: integer('time_spent').default(0), // minutes\n  \n  // Completion data\n  startedAt: timestamp('started_at'),\n  completedAt: timestamp('completed_at'),\n  \n  // User responses and work\n  responses: json('responses').$type<{\n    sectionId: string;\n    response: any;\n    timestamp: string;\n  }[]>().default([]),\n  \n  // Generated content\n  generatedContent: json('generated_content').$type<{\n    type: string;\n    content: string;\n    platform: string;\n    timestamp: string;\n  }[]>().default([]),\n  \n  // Feedback and notes\n  userNotes: text('user_notes'),\n  aifeedback: json('ai_feedback').$type<{\n    score: number;\n    feedback: string;\n    suggestions: string[];\n    timestamp: string;\n  }>(),\n});\n\n// Achievements table\nexport const achievements = pgTable('achievements', {\n  id: serial('id').primaryKey(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n\n  // Achievement Information\n  name: text('name').notNull(),\n  title: text('title').notNull(),\n  description: text('description').notNull(),\n  category: text('category').notNull(), // milestone, consistency, engagement, growth, content\n\n  // Achievement Criteria\n  criteria: json('criteria').$type<{\n    type: string; // follower_count, post_count, engagement_rate, streak_days, etc.\n    threshold: number;\n    timeframe?: number; // days\n    platform?: string;\n  }>().notNull(),\n\n  // Visual Elements\n  icon: text('icon').notNull(),\n  color: text('color').notNull(),\n  rarity: text('rarity').notNull(), // common, rare, epic, legendary\n\n  // Rewards\n  points: integer('points').default(0),\n  unlocks: json('unlocks').$type<string[]>(), // Features or content unlocked\n\n  // Metadata\n  isActive: boolean('is_active').default(true),\n  sortOrder: integer('sort_order').default(0),\n});\n\n// User Achievements table\nexport const userAchievements = pgTable('user_achievements', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  achievementId: integer('achievement_id').references(() => achievements.id).notNull(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n\n  // Achievement Data\n  unlockedAt: timestamp('unlocked_at').notNull(),\n  progress: integer('progress').default(100), // Percentage when unlocked\n\n  // Context\n  roadmapId: integer('roadmap_id').references(() => roadmaps.id),\n  triggerData: json('trigger_data'), // Data that triggered the achievement\n\n  // Celebration\n  wasNotified: boolean('was_notified').default(false),\n  wasCelebrated: boolean('was_celebrated').default(false),\n});\n\n// User Stats table\nexport const userStats = pgTable('user_stats', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  updatedAt: timestamp('updated_at').defaultNow().notNull(),\n\n  // Overall Stats\n  totalPoints: integer('total_points').default(0),\n  level: integer('level').default(1),\n  experiencePoints: integer('experience_points').default(0),\n\n  // Achievement Stats\n  totalAchievements: integer('total_achievements').default(0),\n  commonAchievements: integer('common_achievements').default(0),\n  rareAchievements: integer('rare_achievements').default(0),\n  epicAchievements: integer('epic_achievements').default(0),\n  legendaryAchievements: integer('legendary_achievements').default(0),\n\n  // Activity Stats\n  streakDays: integer('streak_days').default(0),\n  longestStreak: integer('longest_streak').default(0),\n  lastActivityDate: timestamp('last_activity_date'),\n\n  // Content Stats\n  totalContentCreated: integer('total_content_created').default(0),\n  totalBlocksCompleted: integer('total_blocks_completed').default(0),\n  totalRoadmapsCompleted: integer('total_roadmaps_completed').default(0),\n});\n\n// Daily Activity table\nexport const dailyActivity = pgTable('daily_activity', {\n  id: serial('id').primaryKey(),\n  userId: text('user_id').notNull().references(() => users.id),\n  date: date('date').notNull(),\n  createdAt: timestamp('created_at').defaultNow().notNull(),\n\n  // Activity Data\n  activitiesCompleted: json('activities_completed').$type<{\n    type: string;\n    count: number;\n    details?: any;\n  }[]>().default([]),\n\n  // Points and Experience\n  pointsEarned: integer('points_earned').default(0),\n  experienceEarned: integer('experience_earned').default(0),\n\n  // Streaks\n  isStreakDay: boolean('is_streak_day').default(false),\n  streakCount: integer('streak_count').default(0),\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAGO,MAAM,QAAQ,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IACpC,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,sBAAsB;IACtB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACf,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAEf,qBAAqB;IACrB,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,gBAAgB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACrB,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IACvD,iBAAiB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE;IAEzB,uBAAuB;IACvB,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,aAAa,KAAK,GAK3B,OAAO,CAAC,EAAE;IAEjB,cAAc;IACd,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACnB,kBAAkB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACvB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,CAAC;IACnC,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO,CAAC;IAEnC,eAAe;IACf,oBAAoB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,uBAAuB,OAAO,CAAC;IACxD,gBAAgB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACrB,oBAAoB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;AAChC;AAGO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB;IACpD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,wBAAwB;IACxB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IACZ,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IAEvC,oBAAoB;IACpB,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,KAAK,GAKzB,OAAO,CAAC,CAAC;AAChB;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,gBAAgB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,UAAU,CAAC,IAAM,cAAc,EAAE,EAAE,OAAO;IACrF,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,kBAAkB;IAClB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO;IAEhC,sBAAsB;IACtB,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,KAAK;IAIzC,gBAAgB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAErB,mBAAmB;IACnB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,KAAK,GAK3B,OAAO,CAAC,CAAC;AAChB;AAGO,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;IAC5C,IAAI,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU;IACzB,gBAAgB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,UAAU,CAAC,IAAM,cAAc,EAAE,EAAE,OAAO;IACrF,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,uBAAuB;IACvB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAElB,gBAAgB;IAChB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAE1B,yBAAyB;IACzB,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,cAAc,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,KAAK,GAIjC,OAAO,CAAC,EAAE;AACnB;AAGO,MAAM,WAAW,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,YAAY;IAC1C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,sBAAsB;IACtB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAClB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAE1B,wBAAwB;IACxB,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO;IACrC,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IAEtC,oBAAoB;IACpB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO,CAAC;IAC/B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IAEtC,eAAe;IACf,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,KAAK,GAOrB,OAAO;IAEd,WAAW;IACX,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;AACnB;AAGO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IACrD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,oBAAoB;IACpB,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACxC,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAElC,oBAAoB;IACpB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAC9B,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,cAAc,OAAO;IACtC,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO;IAEhD,gBAAgB;IAChB,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,KAAK,GAkBzB,OAAO;IAEZ,4BAA4B;IAC5B,eAAe,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,KAAK,GAAa,OAAO,CAAC,EAAE;IACjE,kBAAkB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,KAAK,GAAa,OAAO,CAAC,EAAE;IAExE,WAAW;IACX,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,CAAC;IACzC,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,KAAK,GAAa,OAAO,CAAC,EAAE;AACjD;AAGO,MAAM,oBAAoB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB;IAC9D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,SAAS,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,UAAU,CAAC,IAAM,cAAc,EAAE,EAAE,OAAO;IACvE,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC7D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IACvD,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,oBAAoB;IACpB,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO,CAAC;IAC/B,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IACtC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,CAAC;IAEzC,kBAAkB;IAClB,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IACrB,aAAa,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAEvB,0BAA0B;IAC1B,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,aAAa,KAAK,GAI3B,OAAO,CAAC,EAAE;IAEjB,oBAAoB;IACpB,kBAAkB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,KAAK,GAK1C,OAAO,CAAC,EAAE;IAEjB,qBAAqB;IACrB,WAAW,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAChB,YAAY,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,KAAK;AAMvC;AAGO,MAAM,eAAe,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB;IAClD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,0BAA0B;IAC1B,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO;IACxC,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,OAAO;IAElC,uBAAuB;IACvB,UAAU,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,YAAY,KAAK,GAK3B,OAAO;IAEZ,kBAAkB;IAClB,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,OAAO,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO;IAC5B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,UAAU,OAAO;IAE9B,UAAU;IACV,QAAQ,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,CAAC;IAClC,SAAS,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,KAAK;IAE9B,WAAW;IACX,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,aAAa,OAAO,CAAC;IACvC,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,OAAO,CAAC;AAC3C;AAGO,MAAM,mBAAmB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB;IAC3D,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,UAAU,CAAC,IAAM,aAAa,EAAE,EAAE,OAAO;IAClF,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,mBAAmB;IACnB,YAAY,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,eAAe,OAAO;IAC5C,UAAU,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,YAAY,OAAO,CAAC;IAEtC,UAAU;IACV,WAAW,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU,CAAC,IAAM,SAAS,EAAE;IAC7D,aAAa,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE;IAElB,cAAc;IACd,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO,CAAC;IAC7C,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO,CAAC;AACnD;AAGO,MAAM,YAAY,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IAC7C,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,gBAAgB;IAChB,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO,CAAC;IAC7C,OAAO,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,SAAS,OAAO,CAAC;IAChC,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IAEvD,oBAAoB;IACpB,mBAAmB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,OAAO,CAAC;IACzD,oBAAoB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,uBAAuB,OAAO,CAAC;IAC3D,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IACvD,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IACvD,uBAAuB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,0BAA0B,OAAO,CAAC;IAEjE,iBAAiB;IACjB,YAAY,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,eAAe,OAAO,CAAC;IAC3C,eAAe,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,OAAO,CAAC;IACjD,kBAAkB,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;IAE5B,gBAAgB;IAChB,qBAAqB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,yBAAyB,OAAO,CAAC;IAC9D,sBAAsB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,0BAA0B,OAAO,CAAC;IAChE,wBAAwB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,4BAA4B,OAAO,CAAC;AACtE;AAGO,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB;IACrD,IAAI,CAAA,GAAA,mKAAA,CAAA,SAAM,AAAD,EAAE,MAAM,UAAU;IAC3B,QAAQ,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,WAAW,OAAO,GAAG,UAAU,CAAC,IAAM,MAAM,EAAE;IAC3D,MAAM,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,OAAO;IAC1B,WAAW,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,cAAc,UAAU,GAAG,OAAO;IAEvD,gBAAgB;IAChB,qBAAqB,CAAA,GAAA,iKAAA,CAAA,OAAI,AAAD,EAAE,wBAAwB,KAAK,GAIhD,OAAO,CAAC,EAAE;IAEjB,wBAAwB;IACxB,cAAc,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO,CAAC;IAC/C,kBAAkB,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,qBAAqB,OAAO,CAAC;IAEvD,UAAU;IACV,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,iBAAiB,OAAO,CAAC;IAC9C,aAAa,CAAA,GAAA,oKAAA,CAAA,UAAO,AAAD,EAAE,gBAAgB,OAAO,CAAC;AAC/C", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/lib/db/index.ts"], "sourcesContent": ["import { drizzle } from 'drizzle-orm/node-postgres';\nimport { Pool } from 'pg';\nimport * as schema from './schema';\n\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n});\n\nexport const db = drizzle(pool, { schema });\n\nexport * from './schema';\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;;;;;AAEA,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;AAC5C;AAEO,MAAM,KAAK,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,MAAM;IAAE,QAAA;AAAO", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/app/api/webhooks/clerk/route.ts"], "sourcesContent": ["import { headers } from 'next/headers';\nimport { NextRequest, NextResponse } from 'next/server';\nimport { Webhook } from 'svix';\nimport { db } from '@/lib/db';\nimport { users } from '@/lib/db/schema';\nimport { eq } from 'drizzle-orm';\n\nconst webhookSecret = process.env.CLERK_WEBHOOK_SECRET;\n\nexport async function POST(req: NextRequest) {\n  if (!webhookSecret) {\n    throw new Error('Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local');\n  }\n\n  // Get the headers\n  const headerPayload = await headers();\n  const svix_id = headerPayload.get('svix-id');\n  const svix_timestamp = headerPayload.get('svix-timestamp');\n  const svix_signature = headerPayload.get('svix-signature');\n\n  // If there are no headers, error out\n  if (!svix_id || !svix_timestamp || !svix_signature) {\n    return new Response('Error occured -- no svix headers', {\n      status: 400,\n    });\n  }\n\n  // Get the body\n  const payload = await req.json();\n  const body = JSON.stringify(payload);\n\n  // Create a new Svix instance with your secret.\n  const wh = new Webhook(webhookSecret);\n\n  let evt: any;\n\n  // Verify the payload with the headers\n  try {\n    evt = wh.verify(body, {\n      'svix-id': svix_id,\n      'svix-timestamp': svix_timestamp,\n      'svix-signature': svix_signature,\n    }) as any;\n  } catch (err) {\n    console.error('Error verifying webhook:', err);\n    return new Response('Error occured', {\n      status: 400,\n    });\n  }\n\n  // Handle the webhook\n  const eventType = evt.type;\n  \n  try {\n    switch (eventType) {\n      case 'user.created':\n        await handleUserCreated(evt.data);\n        break;\n      case 'user.updated':\n        await handleUserUpdated(evt.data);\n        break;\n      case 'user.deleted':\n        await handleUserDeleted(evt.data);\n        break;\n      default:\n        console.log(`Unhandled event type: ${eventType}`);\n    }\n\n    return NextResponse.json({ message: 'Webhook processed successfully' });\n  } catch (error) {\n    console.error('Error processing webhook:', error);\n    return new Response('Error processing webhook', { status: 500 });\n  }\n}\n\nasync function handleUserCreated(userData: any) {\n  const { id, email_addresses, first_name, last_name, image_url, created_at } = userData;\n\n  const primaryEmail = email_addresses.find((email: any) => email.id === userData.primary_email_address_id);\n\n  await db.insert(users).values({\n    id: id,\n    email: primaryEmail?.email_address || '',\n    firstName: first_name || '',\n    lastName: last_name || '',\n    imageUrl: image_url || '',\n    createdAt: new Date(created_at),\n    updatedAt: new Date(),\n  });\n\n  console.log(`User created: ${id}`);\n}\n\nasync function handleUserUpdated(userData: any) {\n  const { id, email_addresses, first_name, last_name, image_url } = userData;\n\n  const primaryEmail = email_addresses.find((email: any) => email.id === userData.primary_email_address_id);\n\n  await db\n    .update(users)\n    .set({\n      email: primaryEmail?.email_address || '',\n      firstName: first_name || '',\n      lastName: last_name || '',\n      imageUrl: image_url || '',\n      updatedAt: new Date(),\n    })\n    .where(eq(users.id, id));\n\n  console.log(`User updated: ${id}`);\n}\n\nasync function handleUserDeleted(userData: any) {\n  const { id } = userData;\n\n  await db.delete(users).where(eq(users.id, id));\n\n  console.log(`User deleted: ${id}`);\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;;;AAEA,MAAM,gBAAgB,QAAQ,GAAG,CAAC,oBAAoB;AAE/C,eAAe,KAAK,GAAgB;IACzC,IAAI,CAAC,eAAe;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,UAAU,cAAc,GAAG,CAAC;IAClC,MAAM,iBAAiB,cAAc,GAAG,CAAC;IACzC,MAAM,iBAAiB,cAAc,GAAG,CAAC;IAEzC,qCAAqC;IACrC,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,gBAAgB;QAClD,OAAO,IAAI,SAAS,oCAAoC;YACtD,QAAQ;QACV;IACF;IAEA,eAAe;IACf,MAAM,UAAU,MAAM,IAAI,IAAI;IAC9B,MAAM,OAAO,KAAK,SAAS,CAAC;IAE5B,+CAA+C;IAC/C,MAAM,KAAK,IAAI,uIAAA,CAAA,UAAO,CAAC;IAEvB,IAAI;IAEJ,sCAAsC;IACtC,IAAI;QACF,MAAM,GAAG,MAAM,CAAC,MAAM;YACpB,WAAW;YACX,kBAAkB;YAClB,kBAAkB;QACpB;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,IAAI,SAAS,iBAAiB;YACnC,QAAQ;QACV;IACF;IAEA,qBAAqB;IACrB,MAAM,YAAY,IAAI,IAAI;IAE1B,IAAI;QACF,OAAQ;YACN,KAAK;gBACH,MAAM,kBAAkB,IAAI,IAAI;gBAChC;YACF,KAAK;gBACH,MAAM,kBAAkB,IAAI,IAAI;gBAChC;YACF,KAAK;gBACH,MAAM,kBAAkB,IAAI,IAAI;gBAChC;YACF;gBACE,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,WAAW;QACpD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAiC;IACvE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,IAAI,SAAS,4BAA4B;YAAE,QAAQ;QAAI;IAChE;AACF;AAEA,eAAe,kBAAkB,QAAa;IAC5C,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG;IAE9E,MAAM,eAAe,gBAAgB,IAAI,CAAC,CAAC,QAAe,MAAM,EAAE,KAAK,SAAS,wBAAwB;IAExG,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,QAAK,EAAE,MAAM,CAAC;QAC5B,IAAI;QACJ,OAAO,cAAc,iBAAiB;QACtC,WAAW,cAAc;QACzB,UAAU,aAAa;QACvB,UAAU,aAAa;QACvB,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI;IACjB;IAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;AACnC;AAEA,eAAe,kBAAkB,QAAa;IAC5C,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;IAElE,MAAM,eAAe,gBAAgB,IAAI,CAAC,CAAC,QAAe,MAAM,EAAE,KAAK,SAAS,wBAAwB;IAExG,MAAM,2IAAA,CAAA,KAAE,CACL,MAAM,CAAC,4HAAA,CAAA,QAAK,EACZ,GAAG,CAAC;QACH,OAAO,cAAc,iBAAiB;QACtC,WAAW,cAAc;QACzB,UAAU,aAAa;QACvB,UAAU,aAAa;QACvB,WAAW,IAAI;IACjB,GACC,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,EAAE,EAAE;IAEtB,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;AACnC;AAEA,eAAe,kBAAkB,QAAa;IAC5C,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,MAAM,2IAAA,CAAA,KAAE,CAAC,MAAM,CAAC,4HAAA,CAAA,QAAK,EAAE,KAAK,CAAC,CAAA,GAAA,oKAAA,CAAA,KAAE,AAAD,EAAE,4HAAA,CAAA,QAAK,CAAC,EAAE,EAAE;IAE1C,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;AACnC", "debugId": null}}]}