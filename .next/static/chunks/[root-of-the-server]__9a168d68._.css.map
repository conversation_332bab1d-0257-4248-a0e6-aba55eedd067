{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 100 900;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/PROJECTS/influtify/src/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  -o-tab-size: 4;\n     tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::-moz-placeholder, textarea::-moz-placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n\n:where(:root),:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {\n  color-scheme: light;\n  --color-base-100: oklch(100% 0 0);\n  --color-base-200: oklch(98% 0 0);\n  --color-base-300: oklch(95% 0 0);\n  --color-base-content: oklch(21% 0.006 285.885);\n  --color-primary: oklch(45% 0.24 277.023);\n  --color-primary-content: oklch(93% 0.034 272.788);\n  --color-secondary: oklch(65% 0.241 354.308);\n  --color-secondary-content: oklch(94% 0.028 342.258);\n  --color-accent: oklch(77% 0.152 181.912);\n  --color-accent-content: oklch(38% 0.063 188.416);\n  --color-neutral: oklch(14% 0.005 285.823);\n  --color-neutral-content: oklch(92% 0.004 286.32);\n  --color-info: oklch(74% 0.16 232.661);\n  --color-info-content: oklch(29% 0.066 243.157);\n  --color-success: oklch(76% 0.177 163.223);\n  --color-success-content: oklch(37% 0.077 168.94);\n  --color-warning: oklch(82% 0.189 84.429);\n  --color-warning-content: oklch(41% 0.112 45.904);\n  --color-error: oklch(71% 0.194 13.428);\n  --color-error-content: oklch(27% 0.105 12.094);\n  --radius-selector: 0.5rem;\n  --radius-field: 0.25rem;\n  --radius-box: 0.5rem;\n  --size-selector: 0.25rem;\n  --size-field: 0.25rem;\n  --border: 1px;\n  --depth: 1;\n  --noise: 0;\n}\n\n@media (prefers-color-scheme: dark) {\n\n  :root {\n    color-scheme: dark;\n    --color-base-100: oklch(25.33% 0.016 252.42);\n    --color-base-200: oklch(23.26% 0.014 253.1);\n    --color-base-300: oklch(21.15% 0.012 254.09);\n    --color-base-content: oklch(97.807% 0.029 256.847);\n    --color-primary: oklch(58% 0.233 277.117);\n    --color-primary-content: oklch(96% 0.018 272.314);\n    --color-secondary: oklch(65% 0.241 354.308);\n    --color-secondary-content: oklch(94% 0.028 342.258);\n    --color-accent: oklch(77% 0.152 181.912);\n    --color-accent-content: oklch(38% 0.063 188.416);\n    --color-neutral: oklch(14% 0.005 285.823);\n    --color-neutral-content: oklch(92% 0.004 286.32);\n    --color-info: oklch(74% 0.16 232.661);\n    --color-info-content: oklch(29% 0.066 243.157);\n    --color-success: oklch(76% 0.177 163.223);\n    --color-success-content: oklch(37% 0.077 168.94);\n    --color-warning: oklch(82% 0.189 84.429);\n    --color-warning-content: oklch(41% 0.112 45.904);\n    --color-error: oklch(71% 0.194 13.428);\n    --color-error-content: oklch(27% 0.105 12.094);\n    --radius-selector: 0.5rem;\n    --radius-field: 0.25rem;\n    --radius-box: 0.5rem;\n    --size-selector: 0.25rem;\n    --size-field: 0.25rem;\n    --border: 1px;\n    --depth: 1;\n    --noise: 0;\n  }\n}\n\n:root:has(input.theme-controller[value=light]:checked),[data-theme=light] {\n  color-scheme: light;\n  --color-base-100: oklch(100% 0 0);\n  --color-base-200: oklch(98% 0 0);\n  --color-base-300: oklch(95% 0 0);\n  --color-base-content: oklch(21% 0.006 285.885);\n  --color-primary: oklch(45% 0.24 277.023);\n  --color-primary-content: oklch(93% 0.034 272.788);\n  --color-secondary: oklch(65% 0.241 354.308);\n  --color-secondary-content: oklch(94% 0.028 342.258);\n  --color-accent: oklch(77% 0.152 181.912);\n  --color-accent-content: oklch(38% 0.063 188.416);\n  --color-neutral: oklch(14% 0.005 285.823);\n  --color-neutral-content: oklch(92% 0.004 286.32);\n  --color-info: oklch(74% 0.16 232.661);\n  --color-info-content: oklch(29% 0.066 243.157);\n  --color-success: oklch(76% 0.177 163.223);\n  --color-success-content: oklch(37% 0.077 168.94);\n  --color-warning: oklch(82% 0.189 84.429);\n  --color-warning-content: oklch(41% 0.112 45.904);\n  --color-error: oklch(71% 0.194 13.428);\n  --color-error-content: oklch(27% 0.105 12.094);\n  --radius-selector: 0.5rem;\n  --radius-field: 0.25rem;\n  --radius-box: 0.5rem;\n  --size-selector: 0.25rem;\n  --size-field: 0.25rem;\n  --border: 1px;\n  --depth: 1;\n  --noise: 0;\n}\n\n:root:has(input.theme-controller[value=dark]:checked),[data-theme=dark] {\n  color-scheme: dark;\n  --color-base-100: oklch(25.33% 0.016 252.42);\n  --color-base-200: oklch(23.26% 0.014 253.1);\n  --color-base-300: oklch(21.15% 0.012 254.09);\n  --color-base-content: oklch(97.807% 0.029 256.847);\n  --color-primary: oklch(58% 0.233 277.117);\n  --color-primary-content: oklch(96% 0.018 272.314);\n  --color-secondary: oklch(65% 0.241 354.308);\n  --color-secondary-content: oklch(94% 0.028 342.258);\n  --color-accent: oklch(77% 0.152 181.912);\n  --color-accent-content: oklch(38% 0.063 188.416);\n  --color-neutral: oklch(14% 0.005 285.823);\n  --color-neutral-content: oklch(92% 0.004 286.32);\n  --color-info: oklch(74% 0.16 232.661);\n  --color-info-content: oklch(29% 0.066 243.157);\n  --color-success: oklch(76% 0.177 163.223);\n  --color-success-content: oklch(37% 0.077 168.94);\n  --color-warning: oklch(82% 0.189 84.429);\n  --color-warning-content: oklch(41% 0.112 45.904);\n  --color-error: oklch(71% 0.194 13.428);\n  --color-error-content: oklch(27% 0.105 12.094);\n  --radius-selector: 0.5rem;\n  --radius-field: 0.25rem;\n  --radius-box: 0.5rem;\n  --size-selector: 0.25rem;\n  --size-field: 0.25rem;\n  --border: 1px;\n  --depth: 1;\n  --noise: 0;\n}\n\n@property --radialprogress {\n  syntax: \"<percentage>\";\n  inherits: true;\n  initial-value: 0%;\n}\n\n:root {\n  scrollbar-color: color-mix(in oklch, currentColor 35%, #0000) #0000;\n  --fx-noise: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='a'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='1.34' numOctaves='4' stitchTiles='stitch'%3E%3C/feTurbulence%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23a)' opacity='0.2'%3E%3C/rect%3E%3C/svg%3E\");\n}\n\n:root, [data-theme] {\n  background-color: var(--root-bg, var(--color-base-100));\n  color: var(--color-base-content);\n}\n  html {\n  scroll-behavior: smooth;\n}\n\n  body {\n  background-color: var(--color-base-100);\n  color: var(--color-base-content);\n}\n.container {\n  width: 100%;\n}\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\n@media (min-width: 1280px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\n@media (min-width: 1536px) {\n\n  .container {\n    max-width: 1536px;\n  }\n}\n@keyframes radio {\n\n  0% {\n    padding: 5px;\n  }\n\n  50% {\n    padding: 3px;\n  }\n}\n.tabs {\n  display: flex;\n  flex-wrap: wrap;\n  --tabs-height: auto;\n  --tabs-direction: row;\n  --tab-height: calc(var(--size-field, 0.25rem) * 10);\n  height: var(--tabs-height);\n  flex-direction: var(--tabs-direction);\n}\n.tab {\n  position: relative;\n  display: inline-flex;\n  cursor: pointer;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  flex-wrap: wrap;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  webkit-user-select: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n@media (hover: hover) {\n\n  .tab:hover {\n    color: var(--color-base-content);\n  }\n}\n.tab {\n  --tab-p: 1rem;\n  --tab-bg: var(--color-base-100);\n  --tab-border-color: var(--color-base-300);\n  --tab-radius-ss: 0;\n  --tab-radius-se: 0;\n  --tab-radius-es: 0;\n  --tab-radius-ee: 0;\n  --tab-order: 0;\n  --tab-radius-min: calc(0.75rem - var(--border));\n  border-color: #0000;\n  order: var(--tab-order);\n  height: var(--tab-height);\n  font-size: 0.875rem;\n  padding-inline-start: var(--tab-p);\n  padding-inline-end: var(--tab-p);\n}\n.tab:is(input[type=\"radio\"]) {\n  min-width: -moz-fit-content;\n  min-width: fit-content;\n}\n.tab:is(input[type=\"radio\"]):after {\n  content: attr(aria-label);\n}\n.tab:is(label) {\n  position: relative;\n}\n.tab:is(label) input {\n  position: absolute;\n  inset: calc(0.25rem * 0);\n  cursor: pointer;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  opacity: 0%;\n}\n.tab:checked + .tab-content, .tab:is(label:has(:checked)) + .tab-content, .tab:is(.tab-active, [aria-selected=\"true\"]) + .tab-content {\n  display: block;\n  height: calc(100% - var(--tab-height) + var(--border));\n}\n.tab:not(:checked, label:has(:checked), :hover, .tab-active, [aria-selected=\"true\"]) {\n  color: color-mix(in oklab, var(--color-base-content) 50%, transparent);\n}\n.tab:not(input):empty {\n  flex-grow: 1;\n  cursor: default;\n}\n.tab:focus {\n  --tw-outline-style: none;\n  outline-style: none;\n}\n@media (forced-colors: active) {\n\n  .tab:focus {\n    outline: 2px solid transparent;\n    outline-offset: 2px;\n  }\n}\n.tab:focus-visible, .tab:is(label:has(:checked:focus-visible)) {\n  outline: 2px solid currentColor;\n  outline-offset: -5px;\n}\n.tab[disabled] {\n  pointer-events: none;\n  opacity: 40%;\n}\n.tabs-border .tab {\n  --tab-border-color: #0000 #0000 var(--tab-border-color) #0000;\n  position: relative;\n  border-radius: var(--radius-field);\n}\n.tabs-border .tab:before {\n  --tw-content: \"\";\n  content: var(--tw-content);\n  background-color: var(--tab-border-color);\n  transition: background-color 0.2s ease;\n  width: 80%;\n  height: 3px;\n  border-radius: var(--radius-field);\n  bottom: 0;\n  left: 10%;\n  position: absolute;\n}\n.tabs-border .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]):before, .tabs-border .tab:is(input:checked):before, .tabs-border .tab:is(label:has(:checked)):before {\n  --tab-border-color: currentColor;\n  border-top: 3px solid;\n}\n.tabs-lift > .tab {\n  --tab-border: 0 0 var(--border) 0;\n  --tab-radius-ss: min(var(--radius-field), var(--tab-radius-min));\n  --tab-radius-se: min(var(--radius-field), var(--tab-radius-min));\n  --tab-radius-es: 0;\n  --tab-radius-ee: 0;\n  --tab-paddings: var(--border) var(--tab-p) 0 var(--tab-p);\n  --tab-border-colors: #0000 #0000 var(--tab-border-color) #0000;\n  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);\n  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));\n  --tab-corner-position: top left, top right;\n  border-width: var(--tab-border);\n  border-start-start-radius: var(--tab-radius-ss);\n  border-start-end-radius: var(--tab-radius-se);\n  border-end-start-radius: var(--tab-radius-es);\n  border-end-end-radius: var(--tab-radius-ee);\n  padding: var(--tab-paddings);\n  border-color: var(--tab-border-colors);\n}\n.tabs-lift > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), .tabs-lift > .tab:is(input:checked, label:has(:checked)) {\n  --tab-border: var(--border) var(--border) 0 var(--border);\n  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000\n        var(--tab-border-color);\n  --tab-paddings: 0 calc(var(--tab-p) - var(--border)) var(--border)\n        calc(var(--tab-p) - var(--border));\n  --tab-inset: auto auto 0 auto;\n  --tab-grad: calc(69% - var(--border));\n  --radius-start: radial-gradient(\n        circle at top left,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      );\n  --radius-end: radial-gradient(\n        circle at top right,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      );\n  background-color: var(--tab-bg);\n}\n.tabs-lift > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]):before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):before {\n  z-index: 1;\n  content: \"\";\n  display: block;\n  position: absolute;\n  width: var(--tab-corner-width);\n  height: var(--tab-corner-height);\n  background-position: var(--tab-corner-position);\n  background-image: var(--radius-start), var(--radius-end);\n  background-size: min(var(--radius-field), var(--tab-radius-min)) min(var(--radius-field), var(--tab-radius-min));\n  background-repeat: no-repeat;\n  inset: var(--tab-inset);\n}\n.tabs-lift > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]):first-child:before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):first-child:before {\n  --radius-start: none;\n}\n[dir=\"rtl\"] .tabs-lift > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]):first-child:before, [dir=\"rtl\"] .tabs-lift > .tab:is(input:checked, label:has(:checked)):first-child:before {\n  transform: rotateY(180deg);\n}\n.tabs-lift > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]):last-child:before, .tabs-lift > .tab:is(input:checked, label:has(:checked)):last-child:before {\n  --radius-end: none;\n}\n[dir=\"rtl\"] .tabs-lift > .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]):last-child:before, [dir=\"rtl\"] .tabs-lift > .tab:is(input:checked, label:has(:checked)):last-child:before {\n  transform: rotateY(180deg);\n}\n.tabs-lift:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected=\"true\"]) {\n  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000\n          var(--tab-border-color);\n}\n.tabs-lift :checked + .tab-content:nth-child(1), .tabs-lift :checked + .tab-content:nth-child(n + 3), .tabs-lift label:has(:checked) + .tab-content:nth-child(1), .tabs-lift label:has(:checked) + .tab-content:nth-child(n + 3), .tabs-lift :is(.tab-active, [aria-selected=\"true\"]) + .tab-content:nth-child(1), .tabs-lift :is(.tab-active, [aria-selected=\"true\"]) + .tab-content:nth-child(n + 3) {\n  --tabcontent-radius-ss: var(--radius-box);\n}\n.tabs-top .tab {\n  --tab-order: 0;\n  --tab-border: 0 0 var(--border) 0;\n  --tab-radius-ss: min(var(--radius-field), var(--tab-radius-min));\n  --tab-radius-se: min(var(--radius-field), var(--tab-radius-min));\n  --tab-radius-es: 0;\n  --tab-radius-ee: 0;\n  --tab-paddings: var(--border) var(--tab-p) 0 var(--tab-p);\n  --tab-border-colors: #0000 #0000 var(--tab-border-color) #0000;\n  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);\n  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));\n  --tab-corner-position: top left, top right;\n}\n.tabs-top .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), .tabs-top .tab:is(input:checked), .tabs-top .tab:is(label:has(:checked)) {\n  --tab-border: var(--border) var(--border) 0 var(--border);\n  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000\n        var(--tab-border-color);\n  --tab-paddings: 0 calc(var(--tab-p) - var(--border)) var(--border)\n        calc(var(--tab-p) - var(--border));\n  --tab-inset: auto auto 0 auto;\n  --radius-start: radial-gradient(\n        circle at top left,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      );\n  --radius-end: radial-gradient(\n        circle at top right,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      );\n}\n.tabs-top:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected=\"true\"]) {\n  --tab-border-colors: var(--tab-border-color) var(--tab-border-color) #0000\n          var(--tab-border-color);\n}\n.tabs-top :checked + .tab-content:nth-child(1), .tabs-top :checked + .tab-content:nth-child(n + 3), .tabs-top label:has(:checked) + .tab-content:nth-child(1), .tabs-top label:has(:checked) + .tab-content:nth-child(n + 3), .tabs-top :is(.tab-active, [aria-selected=\"true\"]) + .tab-content:nth-child(1), .tabs-top :is(.tab-active, [aria-selected=\"true\"]) + .tab-content:nth-child(n + 3) {\n  --tabcontent-radius-ss: var(--radius-box);\n}\n.tabs-bottom .tab {\n  --tab-order: 1;\n  --tab-border: var(--border) 0 0 0;\n  --tab-radius-ss: 0;\n  --tab-radius-se: 0;\n  --tab-radius-es: min(var(--radius-field), var(--tab-radius-min));\n  --tab-radius-ee: min(var(--radius-field), var(--tab-radius-min));\n  --tab-border-colors: var(--tab-border-color) #0000 #0000 #0000;\n  --tab-paddings: 0 var(--tab-p) var(--border) var(--tab-p);\n  --tab-corner-width: calc(100% + min(var(--radius-field), var(--tab-radius-min)) * 2);\n  --tab-corner-height: min(var(--radius-field), var(--tab-radius-min));\n  --tab-corner-position: top left, top right;\n}\n.tabs-bottom .tab:is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), .tabs-bottom .tab:is(input:checked), .tabs-bottom .tab:is(label:has(:checked)) {\n  --tab-border: 0 var(--border) var(--border) var(--border);\n  --tab-border-colors: #0000 var(--tab-border-color) var(--tab-border-color)\n        var(--tab-border-color);\n  --tab-paddings: var(--border) calc(var(--tab-p) - var(--border)) 0\n        calc(var(--tab-p) - var(--border));\n  --tab-inset: 0 auto auto auto;\n  --radius-start: radial-gradient(\n        circle at bottom left,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      );\n  --radius-end: radial-gradient(\n        circle at bottom right,\n        #0000 var(--tab-grad),\n        var(--tab-border-color) calc(var(--tab-grad) + 0.25px),\n        var(--tab-border-color) calc(var(--tab-grad) + var(--border)),\n        var(--tab-bg) calc(var(--tab-grad) + var(--border) + 0.25px)\n      );\n}\n.tabs-bottom:has(.tab-content) > .tab:first-child:not(.tab-active, [aria-selected=\"true\"]) {\n  --tab-border-colors: #0000 var(--tab-border-color) var(--tab-border-color)\n          var(--tab-border-color);\n}\n.tabs-bottom > :checked + .tab-content:not(:nth-child(2)), .tabs-bottom > :is(label:has(:checked)) + .tab-content:not(:nth-child(2)), .tabs-bottom > :is(.tab-active, [aria-selected=\"true\"]) + .tab-content:not(:nth-child(2)) {\n  --tabcontent-radius-es: var(--radius-box);\n}\n.tabs-box .tab {\n  border-radius: var(--radius-field);\n  border-style: none;\n}\n.tabs-box .tab:focus-visible, .tabs-box .tab:is(label:has(:checked:focus-visible)) {\n  outline-offset: 2px;\n}\n.tabs-box > :is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), .tabs-box > :is(input:checked), .tabs-box > :is(label:has(:checked)) {\n  background-color: var(--tab-bg, var(--color-base-100));\n  box-shadow: 0 1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset, 0 1px 1px -1px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 50%), #0000), 0 1px 6px -4px color-mix(in oklab, var(--color-neutral) calc(var(--depth) * 100%), #0000);\n}\n@media (forced-colors: active) {\n\n  .tabs-box > :is(.tab-active, [aria-selected=\"true\"]):not(.tab-disabled, [disabled]), .tabs-box > :is(input:checked), .tabs-box > :is(label:has(:checked)) {\n    border: 1px solid;\n  }\n}\n.tabs-xs :where(.tab) {\n  font-size: 0.75rem;\n  --tab-p: 0.375rem;\n  --tab-radius-min: calc(0.5rem - var(--border));\n}\n.tabs-sm :where(.tab) {\n  font-size: 0.875rem;\n  --tab-p: 0.5rem;\n  --tab-radius-min: calc(0.5rem - var(--border));\n}\n.tabs-md :where(.tab) {\n  font-size: 0.875rem;\n  --tab-p: 0.75rem;\n  --tab-radius-min: calc(0.75rem - var(--border));\n}\n.tabs-lg :where(.tab) {\n  font-size: 1.125rem;\n  --tab-p: 1rem;\n  --tab-radius-min: calc(1.5rem - var(--border));\n}\n.tabs-xl :where(.tab) {\n  font-size: 1.125rem;\n  --tab-p: 1.25rem;\n  --tab-radius-min: calc(2rem - var(--border));\n}\n.hero {\n  display: grid;\n  width: 100%;\n  place-items: center;\n  background-size: cover;\n  background-position: center;\n}\n.hero > * {\n  grid-column-start: 1;\n  grid-row-start: 1;\n}\n.hero-content {\n  isolation: isolate;\n  display: flex;\n  max-width: 80rem;\n  align-items: center;\n  justify-content: center;\n  gap: calc(0.25rem * 4);\n  padding: calc(0.25rem * 4);\n}\n.avatar-group :where(.avatar) {\n  overflow: hidden;\n  border-radius: calc(infinity * 1px);\n  border: 4px solid var(--color-base-100);\n}\n.avatar {\n  position: relative;\n  display: inline-flex;\n  vertical-align: middle;\n}\n.avatar > div {\n  display: block;\n  aspect-ratio: 1 / 1;\n  overflow: hidden;\n}\n.avatar img {\n  height: 100%;\n  width: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n}\n@keyframes skeleton {\n\n  0% {\n    background-position: 150%;\n  }\n\n  100% {\n    background-position: -50%;\n  }\n}\n.progress {\n  position: relative;\n  height: calc(0.25rem * 2);\n  width: 100%;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  overflow: hidden;\n  border-radius: var(--radius-box);\n  background-color: color-mix(in oklab, currentColor 20%, transparent);\n  color: var(--color-base-content);\n}\n.progress:indeterminate {\n  background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );\n  background-size: 200%;\n  background-position-x: 15%;\n  animation: progress 5s ease-in-out infinite;\n}\n@supports (-moz-appearance: none) {\n\n  .progress:indeterminate::-moz-progress-bar {\n    background-color: transparent;\n    background-image: repeating-linear-gradient( 90deg, currentColor -1%, currentColor 10%, #0000 10%, #0000 90% );\n    background-size: 200%;\n    background-position-x: 15%;\n    animation: progress 5s ease-in-out infinite;\n  }\n\n  .progress::-moz-progress-bar {\n    border-radius: var(--radius-box);\n    background-color: currentColor;\n  }\n}\n@supports (-webkit-appearance: none) {\n\n  .progress::-webkit-progress-bar {\n    border-radius: var(--radius-box);\n    background-color: transparent;\n  }\n\n  .progress::-webkit-progress-value {\n    border-radius: var(--radius-box);\n    background-color: currentColor;\n  }\n}\n@keyframes progress {\n\n  50% {\n    background-position-x: -115%;\n  }\n}\n.menu :where(li) .badge {\n  justify-self: flex-end;\n}\n:where(.btn) {\n  width: unset;\n}\n.btn {\n  display: inline-flex;\n  flex-shrink: 0;\n  cursor: pointer;\n  flex-wrap: nowrap;\n  align-items: center;\n  justify-content: center;\n  gap: calc(0.25rem * 1.5);\n  text-align: center;\n  vertical-align: middle;\n  outline-offset: 2px;\n  webkit-user-select: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  padding-inline: var(--btn-p);\n  color: var(--btn-fg);\n  --tw-prose-links: var(--btn-fg);\n  height: var(--size);\n  font-size: var(--fontsize, 0.875rem);\n  font-weight: 600;\n  outline-color: var(--btn-color, var(--color-base-content));\n  transition-property: color, background-color, border-color, box-shadow;\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n  transition-duration: 0.2s;\n  border-start-start-radius: var(--join-ss, var(--radius-field));\n  border-start-end-radius: var(--join-se, var(--radius-field));\n  border-end-start-radius: var(--join-es, var(--radius-field));\n  border-end-end-radius: var(--join-ee, var(--radius-field));\n  background-color: var(--btn-bg);\n  background-size: auto, calc(var(--noise) * 100%);\n  background-image: none, var(--btn-noise);\n  border-width: var(--border);\n  border-style: solid;\n  border-color: var(--btn-border);\n  text-shadow: 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 0.15));\n  touch-action: manipulation;\n  box-shadow: 0 0.5px 0 0.5px oklch(100% 0 0 / calc(var(--depth) * 6%)) inset, var(--btn-shadow);\n  --size: calc(var(--size-field, 0.25rem) * 10);\n  --btn-bg: var(--btn-color, var(--color-base-200));\n  --btn-fg: var(--color-base-content);\n  --btn-p: 1rem;\n  --btn-border: color-mix(in oklab, var(--btn-bg), #000 calc(var(--depth) * 5%));\n  --btn-shadow: 0 3px 2px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000),\n    0 4px 3px -2px color-mix(in oklab, var(--btn-bg) calc(var(--depth) * 30%), #0000);\n  --btn-noise: var(--fx-noise);\n}\n.prose .btn {\n  text-decoration-line: none;\n}\n@media (hover: hover) {\n\n  .btn:hover {\n    --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);\n  }\n}\n.btn:focus-visible {\n  outline-width: 2px;\n  outline-style: solid;\n  isolation: isolate;\n}\n.btn:active:not(.btn-active) {\n  translate: 0 0.5px;\n  --btn-bg: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 5%);\n  --btn-border: color-mix(in oklab, var(--btn-color, var(--color-base-200)), #000 7%);\n  --btn-shadow: 0 0 0 0 oklch(0% 0 0/0), 0 0 0 0 oklch(0% 0 0/0);\n}\n.btn:is(:disabled, [disabled], .btn-disabled):not(.btn-link, .btn-ghost) {\n  background-color: color-mix(in oklab, var(--color-base-content) 10%, transparent);\n  box-shadow: none;\n}\n.btn:is(:disabled, [disabled], .btn-disabled) {\n  pointer-events: none;\n  --btn-border: #0000;\n  --btn-noise: none;\n  --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);\n}\n@media (hover: hover) {\n\n  .btn:is(:disabled, [disabled], .btn-disabled):hover {\n    pointer-events: none;\n    background-color: color-mix(in oklab, var(--color-neutral) 20%, transparent);\n    --btn-border: #0000;\n    --btn-fg: color-mix(in oklch, var(--color-base-content) 20%, #0000);\n  }\n}\n.btn:is(input[type=\"checkbox\"], input[type=\"radio\"]) {\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n}\n.btn:is(input[type=\"checkbox\"], input[type=\"radio\"])::after {\n  content: attr(aria-label);\n}\n.btn:where(input:checked:not(.filter .btn)) {\n  --btn-color: var(--color-primary);\n  --btn-fg: var(--color-primary-content);\n  isolation: isolate;\n}\n.btn-primary {\n  --btn-color: var(--color-primary);\n  --btn-fg: var(--color-primary-content);\n}\n.btn-secondary {\n  --btn-color: var(--color-secondary);\n  --btn-fg: var(--color-secondary-content);\n}\n.btn-accent {\n  --btn-color: var(--color-accent);\n  --btn-fg: var(--color-accent-content);\n}\n.btn-error {\n  --btn-color: var(--color-error);\n  --btn-fg: var(--color-error-content);\n}\n.btn-ghost:not(.btn-active, :hover, :active:focus, :focus-visible) {\n  --btn-shadow: \"\";\n  --btn-bg: #0000;\n  --btn-border: #0000;\n  --btn-noise: none;\n}\n.btn-ghost:not(.btn-active, :hover, :active:focus, :focus-visible):not(:disabled, [disabled], .btn-disabled) {\n  outline-color: currentColor;\n  --btn-fg: currentColor;\n}\n@media (hover: none) {\n\n  .btn-ghost:hover:not(.btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled) {\n    --btn-shadow: \"\";\n    --btn-bg: #0000;\n    --btn-border: #0000;\n    --btn-noise: none;\n    --btn-fg: currentColor;\n  }\n}\n.btn-outline:not( .btn-active, :hover, :active:focus, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {\n  --btn-shadow: \"\";\n  --btn-bg: #0000;\n  --btn-fg: var(--btn-color);\n  --btn-border: var(--btn-color);\n  --btn-noise: none;\n}\n@media (hover: none) {\n\n  .btn-outline:hover:not( .btn-active, :active, :focus-visible, :disabled, [disabled], .btn-disabled, :checked ) {\n    --btn-shadow: \"\";\n    --btn-bg: #0000;\n    --btn-fg: var(--btn-color);\n    --btn-border: var(--btn-color);\n    --btn-noise: none;\n  }\n}\n.btn-sm {\n  --fontsize: 0.75rem;\n  --btn-p: 0.75rem;\n  --size: calc(var(--size-field, 0.25rem) * 8);\n}\n.btn-lg {\n  --fontsize: 1.125rem;\n  --btn-p: 1.25rem;\n  --size: calc(var(--size-field, 0.25rem) * 12);\n}\n.tooltip {\n  position: relative;\n  display: inline-block;\n  --tt-bg: var(--color-neutral);\n  --tt-off: calc(100% + 0.5rem);\n  --tt-tail: calc(100% + 1px + 0.25rem);\n}\n.tooltip > :where(.tooltip-content), .tooltip:where([data-tip]):before {\n  position: absolute;\n  max-width: 20rem;\n  border-radius: var(--radius-field);\n  padding-inline: calc(0.25rem * 2);\n  padding-block: calc(0.25rem * 1);\n  text-align: center;\n  white-space: normal;\n  color: var(--color-neutral-content);\n  opacity: 0%;\n  font-size: 0.875rem;\n  line-height: 1.25;\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;\n  background-color: var(--tt-bg);\n  width: -moz-max-content;\n  width: max-content;\n  pointer-events: none;\n  z-index: 2;\n  --tw-content: attr(data-tip);\n  content: var(--tw-content);\n}\n.tooltip:after {\n  position: absolute;\n  opacity: 0%;\n  background-color: var(--tt-bg);\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 75ms;\n  content: \"\";\n  pointer-events: none;\n  width: 0.625rem;\n  height: 0.25rem;\n  display: block;\n  -webkit-mask-repeat: no-repeat;\n          mask-repeat: no-repeat;\n  -webkit-mask-position: -1px 0;\n          mask-position: -1px 0;\n  --mask-tooltip: url(\"data:image/svg+xml,%3Csvg width='10' height='4' viewBox='0 0 8 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.500009 1C3.5 1 3.00001 4 5.00001 4C7 4 6.5 1 9.5 1C10 1 10 0.499897 10 0H0C-1.99338e-08 0.5 0 1 0.500009 1Z' fill='black'/%3E%3C/svg%3E%0A\");\n  -webkit-mask-image: var(--mask-tooltip);\n          mask-image: var(--mask-tooltip);\n}\n.tooltip.tooltip-open > .tooltip-content, .tooltip.tooltip-open[data-tip]:before, .tooltip.tooltip-open:after, .tooltip[data-tip]:not([data-tip=\"\"]):hover > .tooltip-content, .tooltip[data-tip]:not([data-tip=\"\"]):hover[data-tip]:before, .tooltip[data-tip]:not([data-tip=\"\"]):hover:after, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover > .tooltip-content, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover[data-tip]:before, .tooltip:not(:has(.tooltip-content:empty)):has(.tooltip-content):hover:after, .tooltip:has(:focus-visible) > .tooltip-content, .tooltip:has(:focus-visible)[data-tip]:before, .tooltip:has(:focus-visible):after {\n  opacity: 100%;\n  --tt-pos: 0rem;\n  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0s, transform 0.2s cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n}\n.tooltip > .tooltip-content, .tooltip[data-tip]:before, .tooltip-top > .tooltip-content, .tooltip-top[data-tip]:before {\n  transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));\n  inset: auto auto var(--tt-off) 50%;\n}\n.tooltip:after, .tooltip-top:after {\n  transform: translateX(-50%) translateY(var(--tt-pos, 0.25rem));\n  inset: auto auto var(--tt-tail) 50%;\n}\n.label:is(.\\!input > *, .select > *) {\n  display: flex !important;\n  height: calc(100% - 0.5rem) !important;\n  align-items: center !important;\n  padding-inline: calc(0.25rem * 3) !important;\n  white-space: nowrap !important;\n  font-size: inherit !important;\n}\n.label:is(.input > *, .select > *) {\n  display: flex;\n  height: calc(100% - 0.5rem);\n  align-items: center;\n  padding-inline: calc(0.25rem * 3);\n  white-space: nowrap;\n  font-size: inherit;\n}\n.label:is(.\\!input > *, .select > *):first-child {\n  margin-inline-start: calc(0.25rem * -3) !important;\n  margin-inline-end: calc(0.25rem * 3) !important;\n  border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000) !important;\n}\n.label:is(.input > *, .select > *):first-child {\n  margin-inline-start: calc(0.25rem * -3);\n  margin-inline-end: calc(0.25rem * 3);\n  border-inline-end: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);\n}\n.label:is(.\\!input > *, .select > *):last-child {\n  margin-inline-start: calc(0.25rem * 3) !important;\n  margin-inline-end: calc(0.25rem * -3) !important;\n  border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000) !important;\n}\n.label:is(.input > *, .select > *):last-child {\n  margin-inline-start: calc(0.25rem * 3);\n  margin-inline-end: calc(0.25rem * -3);\n  border-inline-start: var(--border) solid color-mix(in oklab, currentColor 10%, #0000);\n}\n.navbar {\n  display: flex;\n  width: 100%;\n  align-items: center;\n  padding: 0.5rem;\n  min-height: 4rem;\n}\n.navbar-start {\n  display: inline-flex;\n  align-items: center;\n  width: 50%;\n  justify-content: flex-start;\n}\n.navbar-center {\n  display: inline-flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n.navbar-end {\n  display: inline-flex;\n  align-items: center;\n  width: 50%;\n  justify-content: flex-end;\n}\n.card {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  border-radius: var(--radius-box);\n  outline-width: 2px;\n  transition: outline 0.2s ease-in-out;\n  outline: 0 solid #0000;\n  outline-offset: 2px;\n}\n.card:focus {\n  --tw-outline-style: none;\n  outline-style: none;\n}\n@media (forced-colors: active) {\n\n  .card:focus {\n    outline: 2px solid transparent;\n    outline-offset: 2px;\n  }\n}\n.card:focus-visible {\n  outline-color: currentColor;\n}\n.card :where(figure:first-child) {\n  overflow: hidden;\n  border-start-start-radius: inherit;\n  border-start-end-radius: inherit;\n  border-end-start-radius: unset;\n  border-end-end-radius: unset;\n}\n.card :where(figure:last-child) {\n  overflow: hidden;\n  border-start-start-radius: unset;\n  border-start-end-radius: unset;\n  border-end-start-radius: inherit;\n  border-end-end-radius: inherit;\n}\n.card:where(.card-border) {\n  border: var(--border) solid var(--color-base-200);\n}\n.card:where(.card-dash) {\n  border: var(--border) dashed var(--color-base-200);\n}\n.card.image-full {\n  display: grid;\n}\n.card.image-full > * {\n  grid-column-start: 1;\n  grid-row-start: 1;\n}\n.card.image-full > .card-body {\n  position: relative;\n  color: var(--color-neutral-content);\n}\n.card.image-full :where(figure) {\n  overflow: hidden;\n  border-radius: inherit;\n}\n.card.image-full > figure img {\n  height: 100%;\n  -o-object-fit: cover;\n     object-fit: cover;\n  filter: brightness(28%);\n}\n.card figure {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.card:has(> input:is(input[type=\"checkbox\"], input[type=\"radio\"])) {\n  cursor: pointer;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.card:has(> :checked) {\n  outline: 2px solid currentColor;\n}\n.card-title {\n  display: flex;\n  align-items: center;\n  gap: calc(0.25rem * 2);\n  font-size: var(--cardtitle-fs, 1.125rem);\n  font-weight: 600;\n}\n.card-body {\n  display: flex;\n  flex: auto;\n  flex-direction: column;\n  gap: calc(0.25rem * 2);\n  padding: var(--card-p, 1.5rem);\n  font-size: var(--card-fs, 0.875rem);\n}\n.card-body :where(p) {\n  flex-grow: 1;\n}\n.card-xs .card-body {\n  --card-p: 0.5rem;\n  --card-fs: 0.6875rem;\n}\n.card-xs .card-title {\n  --cardtitle-fs: 0.875rem;\n}\n.card-sm .card-body {\n  --card-p: 1rem;\n  --card-fs: 0.75rem;\n}\n.card-sm .card-title {\n  --cardtitle-fs: 1rem;\n}\n.card-md .card-body {\n  --card-p: 1.5rem;\n  --card-fs: 0.875rem;\n}\n.card-md .card-title {\n  --cardtitle-fs: 1.125rem;\n}\n.card-lg .card-body {\n  --card-p: 2rem;\n  --card-fs: 1rem;\n}\n.card-lg .card-title {\n  --cardtitle-fs: 1.25rem;\n}\n.card-xl .card-body {\n  --card-p: 2.5rem;\n  --card-fs: 1.125rem;\n}\n.card-xl .card-title {\n  --cardtitle-fs: 1.375rem;\n}\n@keyframes toast {\n\n  0% {\n    scale: 0.9;\n    opacity: 0;\n  }\n\n  100% {\n    scale: 1;\n    opacity: 1;\n  }\n}\n.status {\n  display: inline-block;\n  aspect-ratio: 1 / 1;\n  width: calc(0.25rem * 2);\n  height: calc(0.25rem * 2);\n  border-radius: var(--radius-selector);\n  background-color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n  background-position: center;\n  background-repeat: no-repeat;\n  vertical-align: middle;\n  color: color-mix(in srgb, #000 30%, transparent);\n}\n@supports (color: color-mix(in lab, red, red)) {\n\n  .status {\n    color: color-mix(in oklab, var(--color-black) 30%, transparent);\n  }\n}\n.status {\n  background-image: radial-gradient( circle at 35% 30%, oklch(1 0 0 / calc(var(--depth) * 0.5)), #0000 );\n  box-shadow: 0 2px 3px -1px color-mix(in oklab, currentColor calc(var(--depth) * 100%), #0000);\n}\n.badge {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: calc(0.25rem * 2);\n  border-radius: var(--radius-selector);\n  vertical-align: middle;\n  color: var(--badge-fg);\n  border: var(--border) solid var(--badge-color, var(--color-base-200));\n  font-size: 0.875rem;\n  width: -moz-fit-content;\n  width: fit-content;\n  padding-inline: calc(0.25rem * 3 - var(--border));\n  background-size: auto, calc(var(--noise) * 100%);\n  background-image: none, var(--fx-noise);\n  background-color: var(--badge-bg);\n  --badge-bg: var(--badge-color, var(--color-base-100));\n  --badge-fg: var(--color-base-content);\n  --size: calc(var(--size-selector, 0.25rem) * 6);\n  height: var(--size);\n}\n.badge-primary {\n  --badge-color: var(--color-primary);\n  --badge-fg: var(--color-primary-content);\n}\n.badge-ghost {\n  border-color: var(--color-base-200);\n  background-color: var(--color-base-200);\n  color: var(--color-base-content);\n  background-image: none;\n}\n.badge-sm {\n  --size: calc(var(--size-selector, 0.25rem) * 5);\n  font-size: 0.75rem;\n  padding-inline: calc(0.25rem * 2.5 - var(--border));\n}\n.badge-lg {\n  --size: calc(var(--size-selector, 0.25rem) * 7);\n  font-size: 1rem;\n  padding-inline: calc(0.25rem * 3.5 - var(--border));\n}\n.mockup-browser .mockup-browser-toolbar .\\!input {\n  margin-inline: auto !important;\n  display: flex !important;\n  height: 100% !important;\n  align-items: center !important;\n  gap: calc(0.25rem * 2) !important;\n  overflow: hidden !important;\n  background-color: var(--color-base-200) !important;\n  text-overflow: ellipsis !important;\n  white-space: nowrap !important;\n  font-size: 0.75rem !important;\n  direction: ltr !important;\n}\n.mockup-browser .mockup-browser-toolbar .input {\n  margin-inline: auto;\n  display: flex;\n  height: 100%;\n  align-items: center;\n  gap: calc(0.25rem * 2);\n  overflow: hidden;\n  background-color: var(--color-base-200);\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  font-size: 0.75rem;\n  direction: ltr;\n}\n.mockup-browser .mockup-browser-toolbar .\\!input:before {\n  content: \"\" !important;\n  width: calc(0.25rem * 4) !important;\n  height: calc(0.25rem * 4) !important;\n  opacity: 30% !important;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='currentColor' class='size-4'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' clip-rule='evenodd' /%3E%3C/svg%3E%0A\") !important;\n}\n.mockup-browser .mockup-browser-toolbar .input:before {\n  content: \"\";\n  width: calc(0.25rem * 4);\n  height: calc(0.25rem * 4);\n  opacity: 30%;\n  background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='currentColor' class='size-4'%3E%3Cpath fill-rule='evenodd' d='M9.965 11.026a5 5 0 1 1 1.06-1.06l2.755 2.754a.75.75 0 1 1-1.06 1.06l-2.755-2.754ZM10.5 7a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z' clip-rule='evenodd' /%3E%3C/svg%3E%0A\");\n}\n@keyframes dropdown {\n\n  0% {\n    opacity: 0;\n  }\n}\n.\\!input {\n  cursor: text !important;\n  border: var(--border) solid #0000 !important;\n  position: relative !important;\n  display: inline-flex !important;\n  flex-shrink: 1 !important;\n  -webkit-appearance: none !important;\n     -moz-appearance: none !important;\n          appearance: none !important;\n  align-items: center !important;\n  gap: calc(0.25rem * 2) !important;\n  background-color: var(--color-base-100) !important;\n  padding-inline: calc(0.25rem * 3) !important;\n  vertical-align: middle !important;\n  white-space: nowrap !important;\n  width: clamp(3rem, 20rem, 100%) !important;\n  height: var(--size) !important;\n  font-size: 0.875rem !important;\n  touch-action: manipulation !important;\n  border-start-start-radius: var(--join-ss, var(--radius-field)) !important;\n  border-start-end-radius: var(--join-se, var(--radius-field)) !important;\n  border-end-start-radius: var(--join-es, var(--radius-field)) !important;\n  border-end-end-radius: var(--join-ee, var(--radius-field)) !important;\n  border-color: var(--input-color) !important;\n  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset !important;\n  --size: calc(var(--size-field, 0.25rem) * 10) !important;\n  --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000) !important;\n}\n.input {\n  cursor: text;\n  border: var(--border) solid #0000;\n  position: relative;\n  display: inline-flex;\n  flex-shrink: 1;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  align-items: center;\n  gap: calc(0.25rem * 2);\n  background-color: var(--color-base-100);\n  padding-inline: calc(0.25rem * 3);\n  vertical-align: middle;\n  white-space: nowrap;\n  width: clamp(3rem, 20rem, 100%);\n  height: var(--size);\n  font-size: 0.875rem;\n  touch-action: manipulation;\n  border-start-start-radius: var(--join-ss, var(--radius-field));\n  border-start-end-radius: var(--join-se, var(--radius-field));\n  border-end-start-radius: var(--join-es, var(--radius-field));\n  border-end-end-radius: var(--join-ee, var(--radius-field));\n  border-color: var(--input-color);\n  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) inset, 0 -1px oklch(100% 0 0 / calc(var(--depth) * 0.1)) inset;\n  --size: calc(var(--size-field, 0.25rem) * 10);\n  --input-color: color-mix(in oklab, var(--color-base-content) 20%, #0000);\n}\n.\\!input:where(input) {\n  display: inline-flex !important;\n}\n.input:where(input) {\n  display: inline-flex;\n}\n.\\!input :where(input) {\n  display: inline-flex !important;\n  height: 100% !important;\n  width: 100% !important;\n  -webkit-appearance: none !important;\n     -moz-appearance: none !important;\n          appearance: none !important;\n  background-color: transparent !important;\n  border: none !important;\n}\n.input :where(input) {\n  display: inline-flex;\n  height: 100%;\n  width: 100%;\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  background-color: transparent;\n  border: none;\n}\n.\\!input :where(input):focus, .\\!input :where(input):focus-within {\n  --tw-outline-style: none !important;\n  outline-style: none !important;\n}\n.input :where(input):focus, .input :where(input):focus-within {\n  --tw-outline-style: none;\n  outline-style: none;\n}\n.\\!input :where(input):focus, .\\!input :where(input):focus-within {\n  --tw-outline-style: none !important;\n  outline-style: none !important;\n}\n@media (forced-colors: active) {\n\n  .\\!input :where(input):focus, .\\!input :where(input):focus-within {\n    outline: 2px solid transparent !important;\n    outline-offset: 2px !important;\n  }\n\n  .input :where(input):focus, .input :where(input):focus-within {\n    outline: 2px solid transparent;\n    outline-offset: 2px;\n  }\n\n  .\\!input :where(input):focus, .\\!input :where(input):focus-within {\n    outline: 2px solid transparent !important;\n    outline-offset: 2px !important;\n  }\n}\n.\\!input :where(input[type=\"url\"]), .\\!input :where(input[type=\"email\"]) {\n  direction: ltr !important;\n}\n.input :where(input[type=\"url\"]), .input :where(input[type=\"email\"]) {\n  direction: ltr;\n}\n.\\!input :where(input[type=\"url\"]), .\\!input :where(input[type=\"email\"]) {\n  direction: ltr !important;\n}\n.\\!input :where(input[type=\"date\"]) {\n  display: inline-block !important;\n}\n.input :where(input[type=\"date\"]) {\n  display: inline-block;\n}\n.\\!input:focus, .\\!input:focus-within {\n  --input-color: var(--color-base-content) !important;\n  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) !important;\n  outline: 2px solid var(--input-color) !important;\n  outline-offset: 2px !important;\n  isolation: isolate !important;\n  z-index: 1 !important;\n}\n.input:focus, .input:focus-within {\n  --input-color: var(--color-base-content);\n  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000);\n  outline: 2px solid var(--input-color);\n  outline-offset: 2px;\n  isolation: isolate;\n  z-index: 1;\n}\n.\\!input:focus, .\\!input:focus-within {\n  --input-color: var(--color-base-content) !important;\n  box-shadow: 0 1px color-mix(in oklab, var(--input-color) calc(var(--depth) * 10%), #0000) !important;\n  outline: 2px solid var(--input-color) !important;\n  outline-offset: 2px !important;\n  isolation: isolate !important;\n  z-index: 1 !important;\n}\n.\\!input:has(> input[disabled]), .\\!input:is(:disabled, [disabled]) {\n  cursor: not-allowed !important;\n  border-color: var(--color-base-200) !important;\n  background-color: var(--color-base-200) !important;\n  color: color-mix(in oklab, var(--color-base-content) 40%, transparent) !important;\n}\n.input:has(> input[disabled]), .input:is(:disabled, [disabled]) {\n  cursor: not-allowed;\n  border-color: var(--color-base-200);\n  background-color: var(--color-base-200);\n  color: color-mix(in oklab, var(--color-base-content) 40%, transparent);\n}\n.\\!input:has(> input[disabled]), .\\!input:is(:disabled, [disabled]) {\n  cursor: not-allowed !important;\n  border-color: var(--color-base-200) !important;\n  background-color: var(--color-base-200) !important;\n  color: color-mix(in oklab, var(--color-base-content) 40%, transparent) !important;\n}\n.\\!input:has(> input[disabled])::-moz-placeholder, .\\!input:is(:disabled, [disabled])::-moz-placeholder {\n  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;\n}\n.\\!input:has(> input[disabled])::placeholder, .\\!input:is(:disabled, [disabled])::placeholder {\n  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;\n}\n.input:has(> input[disabled])::-moz-placeholder, .input:is(:disabled, [disabled])::-moz-placeholder {\n  color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n}\n.input:has(> input[disabled])::placeholder, .input:is(:disabled, [disabled])::placeholder {\n  color: color-mix(in oklab, var(--color-base-content) 20%, transparent);\n}\n.\\!input:has(> input[disabled])::-moz-placeholder, .\\!input:is(:disabled, [disabled])::-moz-placeholder {\n  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;\n}\n.\\!input:has(> input[disabled])::placeholder, .\\!input:is(:disabled, [disabled])::placeholder {\n  color: color-mix(in oklab, var(--color-base-content) 20%, transparent) !important;\n}\n.\\!input:has(> input[disabled]), .\\!input:is(:disabled, [disabled]) {\n  box-shadow: none !important;\n}\n.input:has(> input[disabled]), .input:is(:disabled, [disabled]) {\n  box-shadow: none;\n}\n.\\!input:has(> input[disabled]), .\\!input:is(:disabled, [disabled]) {\n  box-shadow: none !important;\n}\n.\\!input:has(> input[disabled]) > input[disabled] {\n  cursor: not-allowed !important;\n}\n.input:has(> input[disabled]) > input[disabled] {\n  cursor: not-allowed;\n}\n.\\!input::-webkit-date-and-time-value {\n  text-align: inherit !important;\n}\n.input::-webkit-date-and-time-value {\n  text-align: inherit;\n}\n.\\!input[type=\"number\"]::-webkit-inner-spin-button {\n  margin-block: calc(0.25rem * -3) !important;\n  margin-inline-end: calc(0.25rem * -3) !important;\n}\n.input[type=\"number\"]::-webkit-inner-spin-button {\n  margin-block: calc(0.25rem * -3);\n  margin-inline-end: calc(0.25rem * -3);\n}\n.\\!input::-webkit-calendar-picker-indicator {\n  position: absolute !important;\n  inset-inline-end: 0.75em !important;\n}\n.input::-webkit-calendar-picker-indicator {\n  position: absolute;\n  inset-inline-end: 0.75em;\n}\n.chat {\n  display: grid;\n  -moz-column-gap: calc(0.25rem * 3);\n       column-gap: calc(0.25rem * 3);\n  padding-block: calc(0.25rem * 1);\n  --mask-chat: url(\"data:image/svg+xml,%3csvg width='13' height='13' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='black' d='M0 11.5004C0 13.0004 2 13.0004 2 13.0004H12H13V0.00036329L12.5 0C12.5 0 11.977 2.09572 11.8581 2.50033C11.6075 3.35237 10.9149 4.22374 9 5.50036C6 7.50036 0 10.0004 0 11.5004Z'/%3e%3c/svg%3e\");\n}\n.chat-bubble {\n  position: relative;\n  display: block;\n  width: -moz-fit-content;\n  width: fit-content;\n  border-radius: var(--radius-field);\n  background-color: var(--color-base-300);\n  padding-inline: calc(0.25rem * 4);\n  padding-block: calc(0.25rem * 2);\n  color: var(--color-base-content);\n  grid-row-end: 3;\n  min-height: 2rem;\n  min-width: 2.5rem;\n  max-width: 90%;\n}\n.chat-bubble:before {\n  position: absolute;\n  bottom: calc(0.25rem * 0);\n  height: calc(0.25rem * 3);\n  width: calc(0.25rem * 3);\n  background-color: inherit;\n  content: \"\";\n  -webkit-mask-repeat: no-repeat;\n          mask-repeat: no-repeat;\n  -webkit-mask-image: var(--mask-chat);\n          mask-image: var(--mask-chat);\n  -webkit-mask-position: 0px -1px;\n          mask-position: 0px -1px;\n  -webkit-mask-size: 13px;\n          mask-size: 13px;\n}\n.chat-bubble-primary {\n  background-color: var(--color-primary);\n  color: var(--color-primary-content);\n}\n.chat-bubble-accent {\n  background-color: var(--color-accent);\n  color: var(--color-accent-content);\n}\n.chat-image {\n  grid-row: span 2 / span 2;\n  align-self: flex-end;\n}\n.chat-footer {\n  grid-row-start: 3;\n  display: flex;\n  gap: calc(0.25rem * 1);\n  font-size: 0.6875rem;\n}\n.chat-start {\n  place-items: start;\n  grid-template-columns: auto 1fr;\n}\n.chat-start .chat-header {\n  grid-column-start: 2;\n}\n.chat-start .chat-footer {\n  grid-column-start: 2;\n}\n.chat-start .chat-image {\n  grid-column-start: 1;\n}\n.chat-start .chat-bubble {\n  grid-column-start: 2;\n  border-end-start-radius: 0;\n}\n.chat-start .chat-bubble:before {\n  transform: rotateY(0deg);\n  inset-inline-start: -0.75rem;\n}\n[dir=\"rtl\"] .chat-start .chat-bubble:before {\n  transform: rotateY(180deg);\n}\n.chat-end {\n  place-items: end;\n  grid-template-columns: 1fr auto;\n}\n.chat-end .chat-header {\n  grid-column-start: 1;\n}\n.chat-end .chat-footer {\n  grid-column-start: 1;\n}\n.chat-end .chat-image {\n  grid-column-start: 2;\n}\n.chat-end .chat-bubble {\n  grid-column-start: 1;\n  border-end-end-radius: 0;\n}\n.chat-end .chat-bubble:before {\n  transform: rotateY(180deg);\n  inset-inline-start: 100%;\n}\n[dir=\"rtl\"] .chat-end .chat-bubble:before {\n  transform: rotateY(0deg);\n}\n@keyframes rating {\n\n  0%, 40% {\n    scale: 1.1;\n    filter: brightness(1.05) contrast(1.05);\n  }\n}\n.stats {\n  position: relative;\n  display: inline-grid;\n  grid-auto-flow: column;\n  overflow-x: auto;\n  border-radius: var(--radius-box);\n}\n.stat {\n  display: inline-grid;\n  width: 100%;\n  -moz-column-gap: calc(0.25rem * 4);\n       column-gap: calc(0.25rem * 4);\n  padding-inline: calc(0.25rem * 6);\n  padding-block: calc(0.25rem * 4);\n  grid-template-columns: repeat(1, 1fr);\n}\n.stat:not(:last-child) {\n  border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);\n  border-block-end: none;\n}\n.stat-figure {\n  grid-column-start: 2;\n  grid-row: span 3 / span 3;\n  grid-row-start: 1;\n  place-self: center;\n  justify-self: flex-end;\n}\n.stat-title {\n  grid-column-start: 1;\n  white-space: nowrap;\n  color: color-mix(in oklab, var(--color-base-content) 60%, transparent);\n  font-size: 0.75rem;\n}\n.stat-value {\n  grid-column-start: 1;\n  white-space: nowrap;\n  font-size: 2rem;\n  font-weight: 800;\n}\n.stat-desc {\n  grid-column-start: 1;\n  white-space: nowrap;\n  color: color-mix(in oklab, var(--color-base-content) 60%, transparent);\n  font-size: 0.75rem;\n}\n.stats-horizontal .stat:not(:last-child) {\n  border-inline-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);\n  border-block-end: none;\n}\n.stats-vertical {\n  grid-auto-flow: row;\n  overflow-y: auto;\n}\n.stats-vertical .stat:not(:last-child) {\n  border-inline-end: none;\n  border-block-end: var(--border) dashed color-mix(in oklab, currentColor 10%, #0000);\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.mb-16 {\n  margin-bottom: 4rem;\n}\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\n.mb-4 {\n  margin-bottom: 1rem;\n}\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n.mb-8 {\n  margin-bottom: 2rem;\n}\n.ml-1 {\n  margin-left: 0.25rem;\n}\n.mt-3 {\n  margin-top: 0.75rem;\n}\n.inline-block {\n  display: inline-block;\n}\n.flex {\n  display: flex;\n}\n.grid {\n  display: grid;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-12 {\n  height: 3rem;\n}\n.h-16 {\n  height: 4rem;\n}\n.h-4 {\n  height: 1rem;\n}\n.h-5 {\n  height: 1.25rem;\n}\n.h-8 {\n  height: 2rem;\n}\n.h-\\[calc\\(100vh-5rem\\)\\] {\n  height: calc(100vh - 5rem);\n}\n.h-full {\n  height: 100%;\n}\n.min-h-\\[calc\\(100vh-5rem\\)\\] {\n  min-height: calc(100vh - 5rem);\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-10 {\n  width: 2.5rem;\n}\n.w-12 {\n  width: 3rem;\n}\n.w-16 {\n  width: 4rem;\n}\n.w-2 {\n  width: 0.5rem;\n}\n.w-4 {\n  width: 1rem;\n}\n.w-8 {\n  width: 2rem;\n}\n.w-96 {\n  width: 24rem;\n}\n.w-full {\n  width: 100%;\n}\n.max-w-3xl {\n  max-width: 48rem;\n}\n.max-w-4xl {\n  max-width: 56rem;\n}\n.max-w-5xl {\n  max-width: 64rem;\n}\n.max-w-lg {\n  max-width: 32rem;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.grow {\n  flex-grow: 1;\n}\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.flex-col {\n  flex-direction: column;\n}\n.items-center {\n  align-items: center;\n}\n.justify-center {\n  justify-content: center;\n}\n.gap-2 {\n  gap: 0.5rem;\n}\n.gap-3 {\n  gap: 0.75rem;\n}\n.gap-4 {\n  gap: 1rem;\n}\n.gap-8 {\n  gap: 2rem;\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.whitespace-pre-wrap {\n  white-space: pre-wrap;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: 0.5rem;\n}\n.border {\n  border-width: 1px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-l {\n  border-left-width: 1px;\n}\n.border-t {\n  border-top-width: 1px;\n}\n.border-base-200 {\n  border-color: var(--color-base-200);\n}\n.border-base-300 {\n  border-color: var(--color-base-300);\n}\n.border-current {\n  border-color: currentColor;\n}\n.border-t-transparent {\n  border-top-color: transparent;\n}\n.bg-base-100 {\n  background-color: var(--color-base-100);\n}\n.bg-base-200 {\n  background-color: var(--color-base-200);\n}\n.bg-current {\n  background-color: currentColor;\n}\n.bg-gradient-to-b {\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.from-accent {\n  --tw-gradient-from: var(--color-accent) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-base-100 {\n  --tw-gradient-from: var(--color-base-100) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-base-200 {\n  --tw-gradient-from: var(--color-base-200) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary {\n  --tw-gradient-from: var(--color-primary) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.to-base-100 {\n  --tw-gradient-to: var(--color-base-100) var(--tw-gradient-to-position);\n}\n.to-base-200 {\n  --tw-gradient-to: var(--color-base-200) var(--tw-gradient-to-position);\n}\n.to-base-300 {\n  --tw-gradient-to: var(--color-base-300) var(--tw-gradient-to-position);\n}\n.to-secondary {\n  --tw-gradient-to: var(--color-secondary) var(--tw-gradient-to-position);\n}\n.bg-clip-text {\n  -webkit-background-clip: text;\n          background-clip: text;\n}\n.p-3 {\n  padding: 0.75rem;\n}\n.p-5 {\n  padding: 1.25rem;\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.p-8 {\n  padding: 2rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.text-center {\n  text-align: center;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-6xl {\n  font-size: 3.75rem;\n  line-height: 1;\n}\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold {\n  font-weight: 700;\n}\n.font-extrabold {\n  font-weight: 800;\n}\n.font-medium {\n  font-weight: 500;\n}\n.leading-relaxed {\n  line-height: 1.625;\n}\n.text-accent {\n  color: var(--color-accent);\n}\n.text-primary {\n  color: var(--color-primary);\n}\n.text-secondary {\n  color: var(--color-secondary);\n}\n.text-transparent {\n  color: transparent;\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.opacity-50 {\n  opacity: 0.5;\n}\n.shadow-2xl {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-inner {\n  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.backdrop-blur-md {\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-300 {\n  transition-duration: 300ms;\n}\n\n/* Custom styles for Influtify */\n.chat-container {\n  height: calc(100vh - 4rem);\n}\n\n.artifacts-panel {\n  height: calc(100vh - 4rem);\n  overflow-y: auto;\n}\n\n.message-bubble {\n  animation: fadeInUp 0.3s ease-out;\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.typing-indicator {\n  animation: pulse 1.5s ease-in-out infinite;\n}\n\n.achievement-celebration {\n  animation: bounceIn 0.6s ease-out;\n}\n\n@keyframes bounceIn {\n  0% {\n    opacity: 0;\n    transform: scale(0.3);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.05);\n  }\n  70% {\n    transform: scale(0.9);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n.hover\\:btn-primary:hover {\n  --btn-color: var(--color-primary);\n  --btn-fg: var(--color-primary-content);\n}\n\n.hover\\:btn-secondary:hover {\n  --btn-color: var(--color-secondary);\n  --btn-fg: var(--color-secondary-content);\n}\n\n.hover\\:btn-accent:hover {\n  --btn-color: var(--color-accent);\n  --btn-fg: var(--color-accent-content);\n}\n\n.hover\\:badge-primary:hover {\n  --badge-color: var(--color-primary);\n  --badge-fg: var(--color-primary-content);\n}\n\n.hover\\:badge-secondary:hover {\n  --badge-color: var(--color-secondary);\n  --badge-fg: var(--color-secondary-content);\n}\n\n.hover\\:badge-accent:hover {\n  --badge-color: var(--color-accent);\n  --badge-fg: var(--color-accent-content);\n}\n\n.hover\\:text-accent:hover {\n  color: var(--color-accent);\n}\n\n.hover\\:text-secondary:hover {\n  color: var(--color-secondary);\n}\n\n.hover\\:text-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-xl:hover {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.focus\\:shadow-md:focus {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n@media (min-width: 640px) {\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n}\n\n@media (min-width: 768px) {\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;;;;;AASA;;;;AAeA;;;;;;;;;;;;AAkBA;;;;;AAWA;;;;;;AAUA;;;;AASA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAAA;;;;;AAeA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;;;;;;AAMA;;;;;AAKA;;;;;AAIE;;;;AAIA;;;;;AAIF;;;;AAGA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;;AAUA;;;;;;;;;;AASA;;;;;;;;;;;;;;;;;AAgBA;EAEE;;;;;AAIF;;;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;EAEE;;;;;;AAKF;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;;;;;;;;AAYA;;;;;AAIA;;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;;;;;;AAwBA;;;;;;;;;;;;;;AAaA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;;;;;;;;;;;AAaA;;;;;;;;;AAsBA;;;;AAIA;;;;AAGA;;;;;;;;;;;;;;AAaA;;;;;;;;;AAsBA;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;EAEE;;;;;AAIF;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;;AAOA;;;;;AAIA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;;;;AAUA;;;;;;;;;;;;;AAYA;;;;;;;AAMA;EAEE;;;;;;;;EAQA;;;;;;AAKF;EAEE;;;;;EAKA;;;;;;AAKF;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;;;;AAGA;EAEE;;;;;AAIF;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;;;AAMA;EAEE;;;;;;;;AAOF;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;EAEE;;;;;;;;;AAQF;;;;;;;;AAOA;EAEE;;;;;;;;;AAQF;;;;;;AAKA;;;;;;AAKA;;;;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;;;;;;;;;;;;;AAkBA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;AAOA;;;;;;;AAMA;;;;;;AAKA;;;;;;;AAMA;;;;;;;;;;AAUA;;;;;AAIA;EAEE;;;;;;AAKF;;;;AAGA;;;;;;;;AAOA;;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;;AAKA;;;;;;;AAMA;;;;AAGA;;;;;;;;AAOA;;;;;;;;;AAQA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;;;;;;AAYA;;;;;;;;;;;;;AAYA;EAEE;;;;;AAIF;;;;;AAIA;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;AAIA;;;;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;;;;;;;;;;;AAaA;;;;;;;;;;;;;;AAaA;;;;;;;;AAOA;;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;;;AAGA;;;;AAGA;;;;;;;;;;;AAUA;;;;;;;;;;;AAcA;;;;;AAIA;;;;;AAIA;EAOE;;;;;EAKA;;;;;;AAQF;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAWA;;;;;;;;;AAQA;;;;;;;;;AAcA;;;;;;;AAMA;;;;;;;AAYA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;;AAOA;;;;;;;;;;;;;;;AAeA;;;;;;;;;;;;;;;;;AAgBA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;AAMA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAMA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;;AAOA;;;;;;;;AAOA;;;;;;;;;;AASA;;;;;AAIA;;;;;;AAOA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;;;;;;AAWA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;AAkBA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;EAEE;;;;;AAKF;EAEE", "debugId": null}}]}