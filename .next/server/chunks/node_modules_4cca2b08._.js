module.exports = {

"[project]/node_modules/@clerk/nextjs/dist/esm/server/fs/middleware-location.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/[root-of-the-server]__1b13beab._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@clerk/nextjs/dist/esm/server/fs/middleware-location.js [app-route] (ecmascript)");
    });
});
}}),
"[project]/node_modules/next/headers.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/node_modules_next_d81ba465._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
    });
});
}}),

};