import { AIProvider, AIMessage, AIFunction, AIResponse, AIServiceConfig } from './types';
import { OpenAIProvider } from './providers/openai';
import { AnthropicProvider } from './providers/anthropic';
import { GeminiProvider } from './providers/gemini';
import { DeepSeekProvider } from './providers/deepseek';

export class AIService {
  private provider: AIProvider;
  private config: AIServiceConfig;

  constructor(config: AIServiceConfig) {
    this.config = config;
    this.provider = this.createProvider(config);
  }

  private createProvider(config: AIServiceConfig): AIProvider {
    switch (config.provider) {
      case 'openai':
        return new OpenAIProvider(config.apiKey);
      case 'anthropic':
        return new AnthropicProvider(config.apiKey);
      case 'gemini':
        return new GeminiProvider(config.apiKey);
      case 'deepseek':
        return new DeepSeekProvider(config.apiKey);
      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }

  async generateResponse(
    messages: AIMessage[],
    functions?: AIFunction[],
    options?: {
      temperature?: number;
      maxTokens?: number;
      model?: string;
    }
  ): Promise<AIResponse> {
    const mergedOptions = {
      temperature: options?.temperature || this.config.defaultTemperature || 0.7,
      maxTokens: options?.maxTokens || this.config.defaultMaxTokens || 2000,
      model: options?.model || this.config.defaultModel,
    };

    return this.provider.generateResponse(messages, functions, mergedOptions);
  }

  getProviderName(): string {
    return this.provider.name;
  }
}

// Create singleton instance
export const createAIService = (): AIService => {
  const provider = (process.env.AI_PROVIDER as 'openai' | 'anthropic' | 'gemini' | 'deepseek') || 'openai';

  let apiKey: string;
  let defaultModel: string | undefined;

  switch (provider) {
    case 'openai':
      apiKey = process.env.OPENAI_API_KEY!;
      defaultModel = 'gpt-4o-mini';
      break;
    case 'anthropic':
      apiKey = process.env.ANTHROPIC_API_KEY!;
      defaultModel = 'claude-3-haiku-20240307';
      break;
    case 'gemini':
      apiKey = process.env.GEMINI_API_KEY!;
      defaultModel = 'gemini-2.0-flash-lite';
      break;
    case 'deepseek':
      apiKey = process.env.DEEPSEEK_API_KEY!;
      defaultModel = 'deepseek-chat';
      break;
    default:
      throw new Error(`Unsupported AI provider: ${provider}`);
  }

  if (!apiKey) {
    throw new Error(`API key not found for provider: ${provider}`);
  }

  return new AIService({
    provider,
    apiKey,
    defaultModel,
    defaultTemperature: 0.7,
    defaultMaxTokens: 2000,
  });
};

export * from './types';
